directories:
  output: release
  buildResources: build
appId: com.codec.paolife
productName: PaoLife
icon: build/icon.png
asar: false
files:
  - from: out
  - filter:
      - package.json
extraResources:
  - from: node_modules/better-sqlite3
    to: node_modules/better-sqlite3
    filter:
      - '**/*'
  - from: node_modules/kysely
    to: node_modules/kysely
  - from: src/main/migrations.sql
    to: src/main/migrations.sql
    filter:
      - '**/*'
  - from: src/main/migrations.sql
    to: src/main/migrations.sql
compression: normal
buildDependenciesFromSource: false
nodeGypRebuild: false
npmRebuild: false
win:
  target:
    - target: portable
      arch:
        - x64
  executableName: PaoLife
  icon: build/icon.ico
mac:
  icon: build/icon.icns
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${name}-${version}.${ext}
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: electronjs.org
  category: Utility
portable:
  artifactName: ${name}-${version}-portable.${ext}
  requestExecutionLevel: user
  unpackDirName: PaoLife
appImage:
  artifactName: ${name}-${version}.${ext}
publish:
  provider: generic
  url: https://example.com/auto-updates
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
electronVersion: 35.7.5
