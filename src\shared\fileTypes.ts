// File system operation types

export interface FileOperationResult<T = any> {
  success: boolean
  data?: T
  error?: string
}

export interface FileInfo {
  path: string
  name: string
  size: number
  isDirectory: boolean
  isFile: boolean
  createdAt: Date
  modifiedAt: Date
  extension?: string
}

export interface FileContent {
  path: string
  content: string
  encoding?: BufferEncoding
  lastModified: Date
}

export interface FileWatchEvent {
  type: 'add' | 'change' | 'unlink' | 'addDir' | 'unlinkDir'
  path: string
  stats?: FileInfo
  timestamp: Date
}

export interface FileSystemConfig {
  resourcesPath: string
  userDataPath: string
  watchPaths: string[]
  allowedExtensions: string[]
  maxFileSize: number // in bytes
  excludePatterns?: string[] // 排除的文件和目录模式
}

export interface FileLock {
  path: string
  lockId: string
  timestamp: Date
  processId: number
}

export interface FileCache {
  path: string
  content: string
  lastModified: Date
  size: number
  hits: number
  lastAccessed: Date
}

// File operation types
export type FileOperation =
  | 'read'
  | 'write'
  | 'create'
  | 'delete'
  | 'move'
  | 'copy'
  | 'exists'
  | 'stat'
  | 'list'

export interface FileOperationOptions {
  encoding?: BufferEncoding
  flag?: string
  mode?: number
  createDirs?: boolean
  overwrite?: boolean
  backup?: boolean
}

// Resource file specific types
export interface ResourceFile {
  id: string
  path: string
  title: string
  content: string
  tags: string[]
  createdAt: Date
  modifiedAt: Date
  linkedProjects: string[]
  linkedAreas: string[]
  backlinks: string[]
}

export interface ResourceFileMetadata {
  id: string
  title: string
  tags: string[]
  wordCount: number
  linkedProjects: string[]
  linkedAreas: string[]
  backlinks: string[]
  createdAt: Date
  modifiedAt: Date
}

// File system events
export interface FileSystemEvent {
  type: 'file-changed' | 'file-created' | 'file-deleted' | 'directory-changed'
  path: string
  resourceId?: string
  metadata?: ResourceFileMetadata
  timestamp: Date
}

// Error types
export class FileSystemError extends Error {
  constructor(
    message: string,
    public code: string,
    public path?: string,
    public operation?: FileOperation
  ) {
    super(message)
    this.name = 'FileSystemError'
  }
}

export const FILE_SYSTEM_ERRORS = {
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  FILE_LOCKED: 'FILE_LOCKED',
  INVALID_PATH: 'INVALID_PATH',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  UNSUPPORTED_FORMAT: 'UNSUPPORTED_FORMAT',
  DISK_FULL: 'DISK_FULL',
  NETWORK_ERROR: 'NETWORK_ERROR'
} as const
