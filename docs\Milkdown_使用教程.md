# Milkdown 详细使用教程

## 1. 基础介绍

### 什么是 Milkdown？

Milkdown 是一个强大的 WYSIWYG（所见即所得）Markdown 编辑器框架，它将 Markdown 的简洁性与现代编辑器的灵活性完美结合。Milkdown 基于 ProseMirror 构建，提供了插件驱动的架构，使开发者能够轻松定制和扩展编辑器功能。

### 主要特性和优势

- **🔌 插件驱动架构**：高度模块化，支持自定义插件开发
- **🎨 主题系统**：内置多种主题，支持完全自定义样式
- **⚡ 高性能**：基于 ProseMirror，提供流畅的编辑体验
- **🔄 实时协作**：内置协作编辑支持（基于 Y.js）
- **📱 响应式设计**：适配各种设备和屏幕尺寸
- **🛠️ TypeScript 支持**：完整的类型定义，提供优秀的开发体验
- **🧩 组件化**：丰富的内置组件，如图片块、代码块、表格等
- **🎯 框架无关**：支持 React、Vue、Angular、Svelte 等主流框架

## 2. 安装和设置

### 基础安装

#### 使用 Crepe（推荐新手）

Crepe 是 Milkdown 的预配置版本，提供开箱即用的完整编辑器体验：

```bash
npm install @milkdown/crepe
```

#### 使用 Kit（完全自定义）

如果需要完全控制编辑器功能和配置：

```bash
npm install @milkdown/kit
```

#### React 集成

```bash
npm install @milkdown/react @milkdown/kit @milkdown/theme-nord
```

#### Vue 集成

```bash
npm install @milkdown/vue @milkdown/kit @milkdown/crepe
```

### 基本配置

#### 使用 Crepe 的基础设置

```typescript
import { Crepe } from "@milkdown/crepe";
import "@milkdown/crepe/theme/common/style.css";
import "@milkdown/crepe/theme/frame.css";

const crepe = new Crepe({
  root: "#app",
  defaultValue: "# Hello, Milkdown!",
});

crepe.create();
```

#### 使用 Kit 的基础设置

```typescript
import { Editor } from "@milkdown/kit/core";
import { commonmark } from "@milkdown/kit/preset/commonmark";
import "@milkdown/kit/prose/view/style/prosemirror.css";

Editor.make().use(commonmark).create();
```

## 3. 核心概念

### 编辑器架构

Milkdown 采用分层架构设计：

1. **Core Layer（核心层）**：提供基础的编辑器功能和 API
2. **Plugin Layer（插件层）**：扩展编辑器功能的插件系统
3. **Preset Layer（预设层）**：预配置的功能集合
4. **Component Layer（组件层）**：可重用的 UI 组件
5. **Theme Layer（主题层）**：样式和主题系统

### 插件系统

Milkdown 的插件系统基于依赖注入和生命周期管理：

```typescript
interface MilkdownPlugin {
  (ctx: Ctx): Promise<() => Promise<void>>;
}

// 插件生命周期
const examplePlugin: MilkdownPlugin = (ctx) => {
  // 1. Setup Phase - 注册阶段
  ctx.inject(mySlice, defaultValue);
  ctx.record(myTimer);

  return async () => {
    // 2. Initialization Phase - 初始化阶段
    await ctx.wait(RequiredTimer);

    // 3. Runtime Phase - 运行时阶段
    const value = ctx.get(mySlice);
    ctx.set(mySlice, newValue);

    // 4. Cleanup Phase - 清理阶段
    return () => {
      ctx.remove(mySlice);
    };
  };
};
```

### 主题系统

Milkdown 提供了灵活的主题系统：

- **内置主题**：Nord、Frame、Classic 等
- **CSS 变量**：支持通过 CSS 变量自定义样式
- **完全自定义**：可以完全重写样式

## 4. 基础使用

### 创建基本编辑器实例

#### React + TypeScript 示例

```tsx
import React from 'react';
import { Editor, rootCtx } from "@milkdown/kit/core";
import { commonmark } from "@milkdown/kit/preset/commonmark";
import { Milkdown, MilkdownProvider, useEditor } from "@milkdown/react";
import { nord } from "@milkdown/theme-nord";
import "@milkdown/theme-nord/style.css";

const MilkdownEditor: React.FC = () => {
  const { get } = useEditor((root) =>
    Editor.make()
      .config(nord)
      .config((ctx) => {
        ctx.set(rootCtx, root);
      })
      .use(commonmark),
  );

  return <Milkdown />;
};

export const MilkdownEditorWrapper: React.FC = () => {
  return (
    <MilkdownProvider>
      <MilkdownEditor />
    </MilkdownProvider>
  );
};
```

#### 使用 Crepe 的 React 示例

```tsx
import React, { useEffect, useRef } from 'react';
import { Crepe } from "@milkdown/crepe";
import "@milkdown/crepe/theme/common/style.css";
import "@milkdown/crepe/theme/frame.css";

const CrepeEditor: React.FC = () => {
  const editorRef = useRef<HTMLDivElement>(null);
  const crepeRef = useRef<Crepe | null>(null);

  useEffect(() => {
    if (editorRef.current && !crepeRef.current) {
      crepeRef.current = new Crepe({
        root: editorRef.current,
        defaultValue: "# Hello, Milkdown!",
      });

      crepeRef.current.create().then(() => {
        console.log("Editor created");
      });
    }

    return () => {
      if (crepeRef.current) {
        crepeRef.current.destroy();
      }
    };
  }, []);

  return <div ref={editorRef} />;
};
```

### 基本的 Markdown 编辑功能

Milkdown 支持完整的 CommonMark 规范：

- **标题**：`# H1`, `## H2`, `### H3` 等
- **强调**：`*斜体*`, `**粗体**`, `***粗斜体***`
- **列表**：有序列表和无序列表
- **链接**：`[文本](URL)`
- **图片**：`![alt](URL)`
- **代码**：行内代码和代码块
- **引用**：`> 引用文本`
- **表格**：GitHub 风格的表格语法

### 常用的编辑器配置选项

```typescript
import { Editor, defaultValueCtx, rootCtx } from "@milkdown/kit/core";
import { commonmark } from "@milkdown/kit/preset/commonmark";
import { history } from "@milkdown/kit/plugin/history";
import { listener, listenerCtx } from "@milkdown/kit/plugin/listener";

const editor = Editor.make()
  .config((ctx) => {
    // 设置根元素
    ctx.set(rootCtx, document.getElementById('editor'));
    
    // 设置默认内容
    ctx.set(defaultValueCtx, "# 欢迎使用 Milkdown");
    
    // 监听内容变化
    ctx.get(listenerCtx).markdownUpdated((ctx, markdown, prevMarkdown) => {
      console.log('内容已更新:', markdown);
    });
  })
  .use(commonmark)
  .use(history)
  .use(listener)
  .create();
```

## 5. 高级功能

### 自定义插件开发

#### 创建简单的自定义命令

```typescript
import { $command, callCommand } from "@milkdown/kit/utils";
import { blockquoteSchema } from "@milkdown/kit/preset/commonmark";
import { wrapIn } from "@milkdown/kit/prose/commands";

const wrapInBlockquoteCommand = $command(
  "WrapInBlockquote",
  (ctx) => () => wrapIn(blockquoteSchema.type(ctx)),
);

// 注册命令
const editor = Editor.make()
  .use(wrapInBlockquoteCommand)
  .use(commonmark)
  .create();

// 调用命令
editor.action(callCommand(wrapInBlockquoteCommand.key));
```

#### 创建自定义键盘快捷键

```typescript
import { $useKeymap } from "@milkdown/kit/utils";
import { commandsCtx } from "@milkdown/kit/core";

const customKeymap = $useKeymap("customKeymap", {
  WrapInBlockquote: {
    shortcuts: "Mod-Shift-b",
    command: (ctx) => {
      const commands = ctx.get(commandsCtx);
      return () => commands.call(wrapInBlockquoteCommand.key);
    }
  }
});

Editor.make()
  .use(customKeymap)
  .use(wrapInBlockquoteCommand)
  .use(commonmark);
```

### 主题定制

#### 使用 CSS 变量自定义主题

```css
.milkdown {
  /* 颜色定制 */
  --crepe-color-primary: #3b82f6;
  --crepe-color-background: #ffffff;
  --crepe-color-text: #1f2937;
  
  /* 字体定制 */
  --crepe-font-default: "Inter", sans-serif;
  --crepe-font-code: "Fira Code", monospace;
  
  /* 间距定制 */
  --crepe-spacing-unit: 0.5rem;
}
```

#### 创建完全自定义的主题

```typescript
import { ThemeConfig } from "@milkdown/kit/core";

const customTheme: ThemeConfig = {
  color: {
    primary: '#3b82f6',
    secondary: '#64748b',
    background: '#ffffff',
    surface: '#f8fafc',
  },
  font: {
    typography: '"Inter", sans-serif',
    code: '"Fira Code", monospace',
  },
  size: {
    radius: '0.375rem',
    lineWidth: '1px',
  },
};

Editor.make()
  .config(customTheme)
  .use(commonmark);
```

### Crepe 内容更新机制

#### 1. 获取和设置内容

Crepe 提供了简单的 API 来获取和更新编辑器内容：

```typescript
import { Crepe } from '@milkdown/crepe';

const crepe = new Crepe({
  root: '#editor',
  defaultValue: '# 初始内容',
});

await crepe.create();

// 获取当前 Markdown 内容
const currentMarkdown = crepe.getMarkdown();
console.log('当前内容:', currentMarkdown);

// 通过底层编辑器 API 更新内容
import { replaceAll } from '@milkdown/kit/utils';

// 方法1：使用 replaceAll 宏完全替换内容
crepe.editor.action(replaceAll('# 新的内容\n\n这是更新后的内容'));

// 方法2：使用 insert 宏插入内容
import { insert } from '@milkdown/kit/utils';
crepe.editor.action(insert('## 新增的标题'));
```

#### 2. 监听内容变化

```typescript
// 监听内容更新事件
crepe.on((listener) => {
  listener.markdownUpdated((markdown) => {
    console.log('内容已更新:', markdown);
    // 可以在这里保存到服务器或更新状态
  });

  listener.updated((doc) => {
    console.log('文档结构更新:', doc.toJSON());
  });
});
```

#### 3. 程序化内容操作

```typescript
// 获取底层编辑器实例进行高级操作
const editor = crepe.editor;

// 使用编辑器 action 进行复杂操作
editor.action((ctx) => {
  const view = ctx.get(editorViewCtx);
  const { state } = view;

  // 获取当前选择
  const { from, to } = state.selection;

  // 插入内容到特定位置
  const tr = state.tr.insertText('插入的文本', from);
  view.dispatch(tr);
});
```

### Crepe 自定义插件支持

#### 1. 通过底层编辑器添加插件

Crepe 虽然是预配置的编辑器，但仍然可以通过访问底层的 Milkdown 编辑器实例来添加自定义插件：

```typescript
import { Crepe } from '@milkdown/crepe';
import { emoji } from '@milkdown/plugin-emoji';
import { math } from '@milkdown/plugin-math';

const crepe = new Crepe({
  root: '#editor',
  defaultValue: '# Hello World',
});

// 在创建之前添加自定义插件
crepe.editor.use(emoji).use(math);

await crepe.create();
```

#### 2. 使用 CrepeBuilder 进行精细控制

对于需要更多控制的场景，可以使用 `CrepeBuilder`：

```typescript
import { CrepeBuilder } from '@milkdown/crepe/builder';
import { blockEdit } from '@milkdown/crepe/feature/block-edit';
import { toolbar } from '@milkdown/crepe/feature/toolbar';
import { customPlugin } from './custom-plugin';

// 导入必要的样式
import '@milkdown/crepe/theme/common/prosemirror.css';
import '@milkdown/crepe/theme/common/reset.css';
import '@milkdown/crepe/theme/crepe.css';

const builder = new CrepeBuilder({
  root: '#editor',
  defaultValue: '# Hello World',
});

// 手动添加功能和自定义插件
builder
  .addFeature(blockEdit)
  .addFeature(toolbar)
  .use(customPlugin); // 添加自定义插件

const editor = builder.create();
```

#### 3. 自定义插件示例

```typescript
// 创建一个简单的自定义插件
import { $command, $useKeymap } from '@milkdown/kit/utils';
import { commandsCtx } from '@milkdown/kit/core';

// 自定义命令
const insertTimestampCommand = $command(
  'InsertTimestamp',
  (ctx) => () => (state, dispatch) => {
    const timestamp = new Date().toISOString();
    const tr = state.tr.insertText(`[${timestamp}]`);
    dispatch?.(tr);
    return true;
  }
);

// 自定义快捷键
const timestampKeymap = $useKeymap('timestampKeymap', {
  InsertTimestamp: {
    shortcuts: 'Mod-Shift-t',
    command: (ctx) => {
      const commands = ctx.get(commandsCtx);
      return () => commands.call(insertTimestampCommand.key);
    }
  }
});

// 在 Crepe 中使用
const crepe = new Crepe({
  root: '#editor',
  defaultValue: '# 带时间戳的编辑器',
});

crepe.editor
  .use(insertTimestampCommand)
  .use(timestampKeymap);

await crepe.create();
```

#### 4. 配置现有功能

Crepe 允许通过 `featureConfigs` 深度定制现有功能：

```typescript
const crepe = new Crepe({
  root: '#editor',
  features: {
    [Crepe.Feature.ImageBlock]: true,
    [Crepe.Feature.Toolbar]: true,
    [Crepe.Feature.CodeMirror]: true,
  },
  featureConfigs: {
    // 自定义图片上传
    [Crepe.Feature.ImageBlock]: {
      onUpload: async (file: File) => {
        // 自定义上传逻辑
        const formData = new FormData();
        formData.append('image', file);

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        const { url } = await response.json();
        return url;
      },
      blockUploadButton: () => '📁 上传图片',
      blockCaptionPlaceholderText: '添加图片说明...',
    },

    // 自定义工具栏图标
    [Crepe.Feature.Toolbar]: {
      boldIcon: '<svg>...</svg>',
      italicIcon: '<svg>...</svg>',
      linkIcon: '<svg>...</svg>',
    },

    // 自定义代码高亮
    [Crepe.Feature.CodeMirror]: {
      theme: oneDark,
      languages: [
        LanguageDescription.of({
          name: 'TypeScript',
          extensions: ['ts', 'tsx'],
          load() {
            return import('@codemirror/lang-javascript').then(m =>
              m.javascript({ typescript: true })
            );
          },
        }),
      ],
    },
  },
});
```

### 协作编辑功能

Milkdown 支持基于 Y.js 的实时协作编辑：

```typescript
import { collab, collabServiceCtx } from '@milkdown/plugin-collab';
import { Doc } from 'yjs';
import { WebsocketProvider } from 'y-websocket';

// 在 Crepe 中启用协作编辑
const crepe = new Crepe({
  root: '#editor',
  defaultValue: '# 协作文档',
});

// 添加协作插件
crepe.editor.use(collab);

await crepe.create();

// 设置协作
const doc = new Doc();
const wsProvider = new WebsocketProvider('ws://localhost:1234', 'milkdown', doc);

crepe.editor.action((ctx) => {
  const collabService = ctx.get(collabServiceCtx);

  collabService
    .bindDoc(doc)
    .setAwareness(wsProvider.awareness)
    .connect();
});
```

### 数学公式支持

```typescript
import { math } from '@milkdown/plugin-math';
import 'katex/dist/katex.min.css';

Editor.make()
  .use(commonmark)
  .use(math)
  .create();
```

在 Markdown 中使用数学公式：

- 行内公式：`$E = mc^2$`
- 块级公式：`$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$`

### 代码高亮

```typescript
import { prism, prismConfig } from '@milkdown/plugin-prism';
import javascript from 'refractor/lang/javascript';
import typescript from 'refractor/lang/typescript';
import css from 'refractor/lang/css';

Editor.make()
  .config((ctx) => {
    ctx.set(prismConfig.key, {
      configureRefractor: (refractor) => {
        refractor.register(javascript);
        refractor.register(typescript);
        refractor.register(css);
      }
    });
  })
  .use(prism)
  .use(commonmark)
  .create();
```

## 6. API 参考

### 核心 API

#### Editor 类

```typescript
class Editor {
  // 创建编辑器实例
  static make(): Editor;

  // 配置编辑器
  config(config: (ctx: Ctx) => void): Editor;

  // 使用插件
  use(plugin: MilkdownPlugin): Editor;

  // 创建编辑器
  create(): Promise<Editor>;

  // 销毁编辑器
  destroy(): Promise<void>;

  // 执行动作
  action(action: (ctx: Ctx) => void): void;

  // 移除插件
  remove(plugin: MilkdownPlugin): Promise<void>;
}
```

#### Crepe 类

```typescript
class Crepe {
  constructor(config: CrepeConfig);

  // 创建编辑器
  create(): Promise<void>;

  // 销毁编辑器
  destroy(): void;

  // 获取 Markdown 内容
  getMarkdown(): string;

  // 设置只读模式
  setReadonly(readonly: boolean): void;

  // 注册事件监听器
  on(listener: (api: ListenerAPI) => void): void;

  // 获取底层编辑器实例
  get editor(): Editor;
}
```

### 常用工具函数

#### 内容操作

```typescript
import {
  insert,
  replaceAll,
  getMarkdown,
  getHTML,
  callCommand
} from "@milkdown/kit/utils";

// 插入内容
editor.action(insert("# 新标题"));

// 替换所有内容
editor.action(replaceAll("# 全新文档"));

// 获取 Markdown
const markdown = editor.action(getMarkdown());

// 获取 HTML
const html = editor.action(getHTML());

// 调用命令
editor.action(callCommand(someCommand.key, payload));
```

#### 监听器 API

```typescript
import { listener, listenerCtx } from "@milkdown/kit/plugin/listener";

Editor.make()
  .config((ctx) => {
    const listenerManager = ctx.get(listenerCtx);

    // 监听 Markdown 更新
    listenerManager.markdownUpdated((ctx, markdown, prevMarkdown) => {
      console.log('内容更新:', markdown);
    });

    // 监听选择变化
    listenerManager.selectionUpdated((ctx, selection, prevSelection) => {
      console.log('选择变化:', selection);
    });

    // 监听焦点事件
    listenerManager.focus((ctx) => {
      console.log('编辑器获得焦点');
    });

    // 监听失焦事件
    listenerManager.blur((ctx) => {
      console.log('编辑器失去焦点');
    });
  })
  .use(listener);
```

### 配置选项

#### CrepeConfig 接口

```typescript
interface CrepeConfig {
  // 根元素
  root?: Node | string | null;

  // 默认值
  defaultValue?: string | { type: 'json' | 'html', value: any };

  // 功能开关
  features?: Partial<Record<CrepeFeature, boolean>>;

  // 功能配置
  featureConfigs?: CrepeFeatureConfig;
}

// 可用功能
enum CrepeFeature {
  Toolbar = 'toolbar',
  BlockEdit = 'blockEdit',
  Cursor = 'cursor',
  ListItem = 'listItem',
  Latex = 'latex',
  CodeMirror = 'codeMirror',
}
```

## 7. 实际示例

### 完整的 React + TypeScript 编辑器

```tsx
import React, { useState, useCallback } from 'react';
import { Crepe } from "@milkdown/crepe";
import "@milkdown/crepe/theme/common/style.css";
import "@milkdown/crepe/theme/frame.css";

interface MarkdownEditorProps {
  initialValue?: string;
  onChange?: (markdown: string) => void;
  readonly?: boolean;
}

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  initialValue = "# 开始编写...",
  onChange,
  readonly = false
}) => {
  const [editor, setEditor] = useState<Crepe | null>(null);
  const [isReady, setIsReady] = useState(false);

  const editorRef = useCallback((node: HTMLDivElement | null) => {
    if (node && !editor) {
      const crepe = new Crepe({
        root: node,
        defaultValue: initialValue,
        features: {
          [Crepe.Feature.Toolbar]: true,
          [Crepe.Feature.BlockEdit]: true,
          [Crepe.Feature.Latex]: true,
        },
      });

      crepe.create().then(() => {
        setEditor(crepe);
        setIsReady(true);

        // 设置只读模式
        crepe.setReadonly(readonly);

        // 监听内容变化
        crepe.on((listener) => {
          listener.markdownUpdated((markdown) => {
            onChange?.(markdown);
          });
        });
      });
    }
  }, [initialValue, onChange, readonly, editor]);

  // 获取内容
  const getContent = useCallback(() => {
    return editor?.getMarkdown() || '';
  }, [editor]);

  // 设置内容
  const setContent = useCallback((content: string) => {
    if (editor) {
      editor.action(replaceAll(content));
    }
  }, [editor]);

  return (
    <div className="markdown-editor">
      <div ref={editorRef} />
      {isReady && (
        <div className="editor-controls">
          <button onClick={() => console.log(getContent())}>
            获取内容
          </button>
          <button onClick={() => setContent("# 新内容")}>
            设置内容
          </button>
        </div>
      )}
    </div>
  );
};

export default MarkdownEditor;
```

### 带有自定义工具栏的编辑器

```tsx
import React, { useState } from 'react';
import { Editor, commandsCtx } from "@milkdown/kit/core";
import { commonmark, toggleStrongCommand, toggleEmphasisCommand } from "@milkdown/kit/preset/commonmark";
import { Milkdown, MilkdownProvider, useEditor } from "@milkdown/react";
import { callCommand } from "@milkdown/kit/utils";

const CustomToolbar: React.FC = () => {
  const { get } = useEditor();

  const handleBold = () => {
    get()?.action(callCommand(toggleStrongCommand.key));
  };

  const handleItalic = () => {
    get()?.action(callCommand(toggleEmphasisCommand.key));
  };

  return (
    <div className="custom-toolbar">
      <button onClick={handleBold}>粗体</button>
      <button onClick={handleItalic}>斜体</button>
    </div>
  );
};

const EditorWithToolbar: React.FC = () => {
  const { get } = useEditor((root) =>
    Editor.make()
      .config((ctx) => {
        ctx.set(rootCtx, root);
      })
      .use(commonmark)
  );

  return (
    <div>
      <CustomToolbar />
      <Milkdown />
    </div>
  );
};

export const App: React.FC = () => {
  return (
    <MilkdownProvider>
      <EditorWithToolbar />
    </MilkdownProvider>
  );
};
```

## 8. 最佳实践

### 性能优化

#### 1. 懒加载插件

```typescript
// 动态导入大型插件
const loadMathPlugin = async () => {
  const { math } = await import('@milkdown/plugin-math');
  return math;
};

// 条件加载
if (needsMathSupport) {
  const mathPlugin = await loadMathPlugin();
  editor.use(mathPlugin);
}
```

#### 2. 内容分页

```typescript
// 对于大型文档，考虑分页加载
const createPaginatedEditor = (content: string, pageSize: number = 1000) => {
  const pages = content.split('\n\n').reduce((acc, paragraph, index) => {
    const pageIndex = Math.floor(index / pageSize);
    if (!acc[pageIndex]) acc[pageIndex] = [];
    acc[pageIndex].push(paragraph);
    return acc;
  }, [] as string[][]);

  return pages.map(page => page.join('\n\n'));
};
```

#### 3. 防抖更新

```typescript
import { debounce } from 'lodash-es';

const debouncedSave = debounce((content: string) => {
  // 保存内容到服务器
  saveToServer(content);
}, 1000);

crepe.on((listener) => {
  listener.markdownUpdated((markdown) => {
    debouncedSave(markdown);
  });
});
```

### 常见使用场景

#### 1. 博客编辑器

```tsx
interface BlogEditorProps {
  post?: BlogPost;
  onSave: (content: string, title: string) => Promise<void>;
}

const BlogEditor: React.FC<BlogEditorProps> = ({ post, onSave }) => {
  const [title, setTitle] = useState(post?.title || '');
  const [content, setContent] = useState(post?.content || '');
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    setSaving(true);
    try {
      await onSave(content, title);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="blog-editor">
      <input
        type="text"
        value={title}
        onChange={(e) => setTitle(e.target.value)}
        placeholder="文章标题"
        className="title-input"
      />

      <MarkdownEditor
        initialValue={content}
        onChange={setContent}
      />

      <button onClick={handleSave} disabled={saving}>
        {saving ? '保存中...' : '保存文章'}
      </button>
    </div>
  );
};
```

#### 2. 评论系统

```tsx
const CommentEditor: React.FC<{
  onSubmit: (content: string) => void;
}> = ({ onSubmit }) => {
  const [content, setContent] = useState('');

  const handleSubmit = () => {
    if (content.trim()) {
      onSubmit(content);
      setContent('');
    }
  };

  return (
    <div className="comment-editor">
      <Crepe
        root="#comment-editor"
        defaultValue=""
        features={{
          [Crepe.Feature.Toolbar]: true,
          [Crepe.Feature.BlockEdit]: false, // 简化界面
          [Crepe.Feature.Latex]: false,
        }}
        onChange={setContent}
      />
      <button onClick={handleSubmit}>发表评论</button>
    </div>
  );
};
```

#### 3. 文档协作

```tsx
const CollaborativeEditor: React.FC<{
  documentId: string;
  userId: string;
}> = ({ documentId, userId }) => {
  const [editor, setEditor] = useState<Editor | null>(null);
  const [users, setUsers] = useState<User[]>([]);

  useEffect(() => {
    const setupCollaboration = async () => {
      const doc = new Doc();
      const wsProvider = new WebsocketProvider(
        `ws://localhost:1234`,
        documentId,
        doc
      );

      const milkdownEditor = await Editor.make()
        .use(collab)
        .use(commonmark)
        .create();

      milkdownEditor.action((ctx) => {
        const collabService = ctx.get(collabServiceCtx);

        collabService
          .bindDoc(doc)
          .setAwareness(wsProvider.awareness)
          .connect();

        // 监听用户状态
        wsProvider.awareness.on('change', () => {
          const states = Array.from(wsProvider.awareness.getStates().values());
          setUsers(states.map(state => state.user).filter(Boolean));
        });
      });

      setEditor(milkdownEditor);
    };

    setupCollaboration();
  }, [documentId]);

  return (
    <div className="collaborative-editor">
      <div className="user-list">
        {users.map(user => (
          <div key={user.id} className="user-avatar">
            {user.name}
          </div>
        ))}
      </div>
      <div id="editor" />
    </div>
  );
};
```

### 错误处理

#### 1. 编辑器初始化错误

```typescript
const createEditorSafely = async (config: CrepeConfig) => {
  try {
    const crepe = new Crepe(config);
    await crepe.create();
    return crepe;
  } catch (error) {
    console.error('编辑器初始化失败:', error);

    // 降级到简单的文本框
    const fallbackElement = document.createElement('textarea');
    fallbackElement.value = config.defaultValue || '';
    config.root?.appendChild(fallbackElement);

    return null;
  }
};
```

#### 2. 插件加载错误

```typescript
const loadPluginSafely = async (editor: Editor, pluginLoader: () => Promise<any>) => {
  try {
    const plugin = await pluginLoader();
    editor.use(plugin);
  } catch (error) {
    console.warn('插件加载失败，跳过:', error);
    // 继续使用基础功能
  }
};
```

## 9. 故障排除

### 常见问题和解决方案

#### 1. 样式不生效

**问题**：编辑器显示但样式混乱

**解决方案**：

```typescript
// 确保导入了必要的 CSS
import "@milkdown/kit/prose/view/style/prosemirror.css"; // 必需
import "@milkdown/theme-nord/style.css"; // 主题样式
```

#### 2. TypeScript 类型错误

**问题**：TypeScript 报告类型不匹配

**解决方案**：

```typescript
// 确保安装了类型定义
npm install @types/node

// 在 tsconfig.json 中添加类型声明
{
  "compilerOptions": {
    "types": ["node"]
  }
}
```

#### 3. React 中的内存泄漏

**问题**：组件卸载后编辑器实例未清理

**解决方案**：

```tsx
useEffect(() => {
  // 创建编辑器
  const crepe = new Crepe(config);

  return () => {
    // 清理资源
    crepe.destroy();
  };
}, []);
```

#### 4. 协作编辑连接问题

**问题**：多用户协作时出现同步问题

**解决方案**：

```typescript
// 添加连接状态监听
wsProvider.on('status', (event) => {
  console.log('连接状态:', event.status);
  if (event.status === 'disconnected') {
    // 处理断线重连
    setTimeout(() => wsProvider.connect(), 1000);
  }
});
```

#### 5. 移动端适配问题

**问题**：在移动设备上编辑体验不佳

**解决方案**：

```css
/* 移动端优化 */
@media (max-width: 768px) {
  .milkdown {
    font-size: 16px; /* 防止 iOS 缩放 */
  }

  .milkdown .editor {
    padding: 1rem;
    min-height: 200px;
  }
}
```

### 调试技巧

#### 1. 启用调试模式

```typescript
// 在开发环境中启用详细日志
if (process.env.NODE_ENV === 'development') {
  Editor.make()
    .config((ctx) => {
      // 监听所有事件
      ctx.get(listenerCtx).updated((ctx, doc) => {
        console.log('文档更新:', doc.toJSON());
      });
    })
    .use(listener);
}
```

#### 2. 检查插件状态

```typescript
// 检查插件是否正确加载
editor.action((ctx) => {
  console.log('已加载的插件:', ctx.plugins);
  console.log('编辑器状态:', ctx.get(editorViewCtx).state);
});
```

## 10. 常见问题解答 (FAQ)

### Q1: Crepe 是如何更新内容的？

**A:** Crepe 提供了多种更新内容的方式：

1. **通过 API 方法**：

   ```typescript
   // 获取内容
   const markdown = crepe.getMarkdown();

   // 通过底层编辑器更新内容
   import { replaceAll, insert } from '@milkdown/kit/utils';

   // 完全替换内容
   crepe.editor.action(replaceAll('# 新内容'));

   // 插入内容
   crepe.editor.action(insert('## 新标题'));
   ```

2. **监听内容变化**：

   ```typescript
   crepe.on((listener) => {
     listener.markdownUpdated((markdown) => {
       console.log('内容更新:', markdown);
     });
   });
   ```

3. **程序化操作**：

   ```typescript
   crepe.editor.action((ctx) => {
     const view = ctx.get(editorViewCtx);
     const tr = view.state.tr.insertText('新文本', position);
     view.dispatch(tr);
   });
   ```

### Q2: Crepe 是否可以加载自定义插件？

**A:** 是的，Crepe 完全支持自定义插件，有以下几种方式：

1. **直接添加到底层编辑器**：

   ```typescript
   import { Crepe } from '@milkdown/crepe';
   import { emoji } from '@milkdown/plugin-emoji';

   const crepe = new Crepe({ root: '#editor' });

   // 在创建前添加插件
   crepe.editor.use(emoji);
   await crepe.create();
   ```

2. **使用 CrepeBuilder 进行精细控制**：

   ```typescript
   import { CrepeBuilder } from '@milkdown/crepe/builder';
   import { customPlugin } from './custom-plugin';

   const builder = new CrepeBuilder({ root: '#editor' });
   builder.use(customPlugin);
   const editor = builder.create();
   ```

3. **创建自定义插件**：

   ```typescript
   import { $command, $useKeymap } from '@milkdown/kit/utils';

   const myCommand = $command('MyCommand', (ctx) => () => {
     // 插件逻辑
   });

   const myKeymap = $useKeymap('myKeymap', {
     MyAction: {
       shortcuts: 'Mod-k',
       command: (ctx) => () => ctx.get(commandsCtx).call(myCommand.key)
     }
   });

   crepe.editor.use(myCommand).use(myKeymap);
   ```

### Q3: Crepe 和 Kit 的区别是什么？

**A:**

- **Crepe**：预配置的完整编辑器，开箱即用，适合快速开发
  - 内置所有常用功能（工具栏、图片上传、代码高亮等）
  - 提供美观的默认主题
  - 配置简单，但定制性相对有限

- **Kit**：底层构建工具，完全可定制，适合复杂需求
  - 需要手动配置所有功能
  - 完全控制编辑器行为
  - 更小的包体积（按需加载）

### Q4: 如何在 React 项目中集成 Milkdown？

**A:** 有两种主要方式：

1. **使用 Crepe（推荐）**：

   ```tsx
   import { Crepe } from '@milkdown/crepe';

   const Editor = () => {
     const editorRef = useRef<HTMLDivElement>(null);

     useEffect(() => {
       if (editorRef.current) {
         const crepe = new Crepe({
           root: editorRef.current,
           defaultValue: '# Hello'
         });
         crepe.create();

         return () => crepe.destroy();
       }
     }, []);

     return <div ref={editorRef} />;
   };
   ```

2. **使用 React 集成包**：

   ```tsx
   import { MilkdownProvider, Milkdown, useEditor } from '@milkdown/react';

   const Editor = () => {
     useEditor((root) => Editor.make().use(commonmark));
     return <Milkdown />;
   };

   const App = () => (
     <MilkdownProvider>
       <Editor />
     </MilkdownProvider>
   );
   ```

### Q5: 如何自定义 Milkdown 的主题？

**A:**

1. **使用 CSS 变量**：

   ```css
   .milkdown {
     --crepe-color-primary: #3b82f6;
     --crepe-color-background: #ffffff;
     --crepe-font-default: "Inter", sans-serif;
   }
   ```

2. **导入预设主题**：

   ```typescript
   import '@milkdown/crepe/theme/frame.css';
   import '@milkdown/crepe/theme/nord.css';
   import '@milkdown/crepe/theme/crepe-dark.css';
   ```

3. **完全自定义主题**：

   ```typescript
   const customTheme = {
     color: { primary: '#ff6b6b' },
     font: { typography: '"Roboto", sans-serif' },
   };

   Editor.make().config(customTheme);
   ```

## 11. 总结

Milkdown 是一个功能强大且灵活的 Markdown 编辑器框架，适合各种应用场景。通过本教程，您应该能够：

1. **理解 Milkdown 的核心概念**和架构设计
2. **掌握基础使用方法**，包括安装、配置和基本操作
3. **学会高级功能**，如自定义插件、主题定制和协作编辑
4. **了解最佳实践**，包括性能优化和错误处理
5. **解决常见问题**，确保编辑器稳定运行

### 进一步学习资源

- [Milkdown 官方文档](https://milkdown.dev/)
- [GitHub 仓库](https://github.com/Milkdown/milkdown)
- [示例项目](https://github.com/Milkdown/examples)
- [社区讨论](https://github.com/Milkdown/milkdown/discussions)

### 社区支持

如果遇到问题或需要帮助，可以：

1. 查阅官方文档和 API 参考
2. 在 GitHub Issues 中搜索相关问题
3. 参与社区讨论
4. 贡献代码和文档

Milkdown 拥有活跃的开源社区，欢迎大家参与贡献！
