"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const electron = require("electron");
const fs = require("fs");
const path = require("path");
const utils = require("@electron-toolkit/utils");
const Database = require("better-sqlite3");
const kysely = require("kysely");
const fs$1 = require("fs/promises");
const chokidar = require("chokidar");
const events = require("events");
const origLog = console.log;
const origWarn = console.warn;
const origError = console.error;
const origInfo = console.info;
function getLogFile() {
  try {
    const baseDir = electron.app?.getPath?.("userData") || process.cwd();
    const logDir = path.join(baseDir, "logs");
    if (!fs.existsSync(logDir)) fs.mkdirSync(logDir, { recursive: true });
    const now = /* @__PURE__ */ new Date();
    const y = now.getFullYear();
    const m = String(now.getMonth() + 1).padStart(2, "0");
    const d = String(now.getDate()).padStart(2, "0");
    const file = path.join(logDir, `paolife-${y}${m}${d}.log`);
    return file;
  } catch {
    return null;
  }
}
function write(file, level, args) {
  try {
    if (!file) return;
    const line = `[${(/* @__PURE__ */ new Date()).toISOString()}] [${level}] ${args.map(String).join(" ")}
`;
    fs.appendFileSync(file, line);
  } catch {
  }
}
function installFileLogger() {
  const file = getLogFile();
  console.log = (...args) => {
    write(file, "LOG", args);
    origLog(...args);
  };
  console.warn = (...args) => {
    write(file, "WARN", args);
    origWarn(...args);
  };
  console.error = (...args) => {
    write(file, "ERROR", args);
    origError(...args);
  };
  console.info = (...args) => {
    write(file, "INFO", args);
    origInfo(...args);
  };
  console.log("File logger installed. Logs at:", file);
}
try {
  installFileLogger();
} catch (e) {
  origWarn("Failed to install file logger:", e);
}
const icon = path.join(__dirname, "./chunks/icon-FNk2O0iE.png");
let db = null;
let customWorkspaceDirectory = null;
function setWorkspaceDirectory(directory) {
  console.log("=== [kyselyClient] setWorkspaceDirectory CALLED ===");
  console.log("[kyselyClient] Previous customWorkspaceDirectory:", customWorkspaceDirectory);
  console.log("[kyselyClient] New directory:", directory);
  customWorkspaceDirectory = directory;
  console.log("[kyselyClient] ✅ Custom workspace directory set in kyselyClient:", directory);
  console.log("=== [kyselyClient] setWorkspaceDirectory END ===");
}
function loadUserSettingsFromStorage() {
  try {
    const userDataPath = electron.app.getPath("userData");
    console.log("=== [kyselyClient] loadUserSettingsFromStorage START ===");
    console.log("[kyselyClient] Searching for user settings in:", userDataPath);
    const possiblePaths = [
      path.join(userDataPath, "Local Storage", "leveldb"),
      path.join(userDataPath, "IndexedDB"),
      path.join(userDataPath, "Session Storage")
    ];
    for (const storagePath of possiblePaths) {
      if (fs.existsSync(storagePath)) {
        console.log("[kyselyClient] Found storage directory:", storagePath);
        try {
          const files = fs.readdirSync(storagePath);
          for (const file of files) {
            const filePath = path.join(storagePath, file);
            try {
              const content = fs.readFileSync(filePath, "utf8");
              console.log(`[kyselyClient] Checking file: ${file}, size: ${content.length}`);
              const hasUserSettings = content.includes("user-settings") || content.includes("workspaceDirectory") || content.includes('"settings"') || content.includes("userSettingsStore");
              if (hasUserSettings) {
                console.log("[kyselyClient] Found potential user-settings in file:", file);
                console.log("[kyselyClient] Content preview:", content.substring(0, 500) + "...");
                const patterns = [
                  // JSON格式
                  /"workspaceDirectory":"([^"]+)"/,
                  /'workspaceDirectory':'([^']+)'/,
                  // URL编码格式
                  /workspaceDirectory%22%3A%22([^%]+)%22/,
                  // 其他可能的格式
                  /workspaceDirectory['":\s]+([^'",\s}]+)/,
                  // Zustand persist格式
                  /"settings"[^}]*"workspaceDirectory":"([^"]+)"/
                ];
                for (const pattern of patterns) {
                  const match = content.match(pattern);
                  if (match && match[1]) {
                    let workspaceDir = match[1];
                    try {
                      workspaceDir = decodeURIComponent(workspaceDir);
                    } catch (e) {
                    }
                    workspaceDir = workspaceDir.replace(/\\\\/g, "\\");
                    console.log("[kyselyClient] ✅ Extracted workspaceDirectory:", workspaceDir);
                    console.log("=== [kyselyClient] loadUserSettingsFromStorage SUCCESS ===");
                    return workspaceDir;
                  }
                }
                console.log("[kyselyClient] Found user-settings but no workspaceDirectory");
              }
            } catch (e) {
              continue;
            }
          }
        } catch (error) {
          console.warn("Failed to read storage directory:", storagePath, error);
        }
      }
    }
    console.log("[kyselyClient] ❌ No user settings found in localStorage");
    console.log("=== [kyselyClient] loadUserSettingsFromStorage FAILED ===");
    return null;
  } catch (error) {
    console.warn("[kyselyClient] Failed to load user settings from storage:", error);
    console.log("=== [kyselyClient] loadUserSettingsFromStorage ERROR ===");
    return null;
  }
}
function getUserWorkspaceDirectory() {
  console.log("=== [kyselyClient] getUserWorkspaceDirectory START ===");
  console.log("[kyselyClient] customWorkspaceDirectory:", customWorkspaceDirectory);
  if (customWorkspaceDirectory) {
    console.log("[kyselyClient] ✅ Using custom workspace directory for database:", customWorkspaceDirectory);
    console.log("=== [kyselyClient] getUserWorkspaceDirectory END (custom) ===");
    return customWorkspaceDirectory;
  }
  console.log("[kyselyClient] Attempting to load from localStorage...");
  const savedWorkspaceDir = loadUserSettingsFromStorage();
  if (savedWorkspaceDir) {
    console.log("[kyselyClient] ✅ Using saved workspace directory for database:", savedWorkspaceDir);
    console.log("=== [kyselyClient] getUserWorkspaceDirectory END (saved) ===");
    return savedWorkspaceDir;
  }
  const defaultPath = electron.app.getPath("userData");
  console.log("[kyselyClient] ⚠️ Using default userData directory for database:", defaultPath);
  console.log("=== [kyselyClient] getUserWorkspaceDirectory END (default) ===");
  return defaultPath;
}
function createKyselyClient() {
  if (db) {
    return db;
  }
  const isDevelopment = process.env.NODE_ENV === "development" || !electron.app.isPackaged;
  let databasePath;
  console.log("=== [kyselyClient] createKyselyClient DATABASE PATH SETUP ===");
  console.log("[kyselyClient] isDevelopment:", isDevelopment);
  const userWorkspaceDir = getUserWorkspaceDirectory();
  console.log("[kyselyClient] Final userWorkspaceDir:", userWorkspaceDir);
  if (isDevelopment) {
    databasePath = path.join(userWorkspaceDir, "PaoLife", "dev.db");
    console.log("[kyselyClient] 🔧 Development database path (user workspace):", databasePath);
  } else {
    databasePath = path.join(userWorkspaceDir, "PaoLife", "paolife.db");
    console.log("[kyselyClient] 🚀 Production database path (user workspace):", databasePath);
  }
  console.log("=== [kyselyClient] FINAL DATABASE PATH:", databasePath, "===");
  const dbDir = path.dirname(databasePath);
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
  }
  const sqlite = new Database(databasePath);
  sqlite.pragma("foreign_keys = ON");
  sqlite.pragma("journal_mode = WAL");
  db = new kysely.Kysely({
    dialect: new kysely.SqliteDialect({
      database: sqlite
    })
  });
  console.log(`Kysely database client created successfully at: ${databasePath}`);
  console.log(`Database mode: ${isDevelopment ? "Development" : "Production"}`);
  return db;
}
function getKyselyClient() {
  if (!db) {
    throw new Error("Database not initialized. Call createKyselyClient() first.");
  }
  return db;
}
async function closeKyselyClient() {
  if (db) {
    await db.destroy();
    db = null;
    console.log("Kysely database client closed");
  }
}
function serializeJson(value) {
  return JSON.stringify(value);
}
function deserializeJson(value) {
  if (value === null) return null;
  try {
    return JSON.parse(value);
  } catch (error) {
    console.warn("Failed to parse JSON:", value, error);
    return null;
  }
}
function booleanToInt(value) {
  return value ? 1 : 0;
}
function intToBoolean(value) {
  return value === 1;
}
function getCurrentTimestamp() {
  return (/* @__PURE__ */ new Date()).toISOString();
}
function generateId() {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 15);
  return `c${timestamp}${randomPart}`;
}
async function withTransaction(db2, callback) {
  return await db2.transaction().execute(callback);
}
async function runMigrations() {
  const client = getKyselyClient();
  const possiblePaths = [
    path.join(__dirname, "migrations.sql"),
    path.join(process.cwd(), "src", "main", "migrations.sql"),
    path.join(electron.app.getAppPath(), "src", "main", "migrations.sql"),
    path.join(process.resourcesPath, "app", "src", "main", "migrations.sql")
  ];
  let migrationSql;
  let foundPath = null;
  for (const migrationPath of possiblePaths) {
    try {
      if (fs.existsSync(migrationPath)) {
        migrationSql = fs.readFileSync(migrationPath, "utf-8");
        foundPath = migrationPath;
        console.log(`Found migrations.sql at: ${migrationPath}`);
        break;
      }
    } catch (error) {
    }
  }
  if (!foundPath) {
    throw new Error(`Could not find migrations.sql file. Tried paths: ${possiblePaths.join(", ")}`);
  }
  const cleanedSql = migrationSql.split("\n").filter((line) => {
    const trimmed = line.trim();
    return trimmed.length > 0 && !trimmed.startsWith("--");
  }).join("\n");
  const statements = cleanedSql.split(";").map((stmt) => stmt.trim()).filter((stmt) => stmt.length > 0);
  console.log(`Running ${statements.length} migration statements...`);
  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i];
    try {
      console.log(`Executing statement ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`);
      await kysely.sql`${kysely.sql.raw(statement)}`.execute(client);
      console.log("✓ Migration statement executed successfully");
    } catch (error) {
      console.error("✗ Migration statement failed:", statement.substring(0, 100) + "...");
      console.error("Full statement:", statement);
      console.error("Error:", error);
      throw error;
    }
  }
  console.log("All migrations completed successfully");
}
class KyselyMigrationManager {
  get db() {
    return getKyselyClient();
  }
  /**
   * Initialize database with all tables and indexes
   */
  async initializeDatabase() {
    console.log("Initializing Kysely database...");
    try {
      await runMigrations();
      console.log("✅ Database initialization completed successfully");
    } catch (error) {
      console.error("❌ Database initialization failed:", error);
      throw error;
    }
  }
  /**
   * Check if database is properly initialized
   */
  async isDatabaseInitialized() {
    try {
      const tables = ["User", "Area", "Project", "Task", "Habit"];
      for (const table of tables) {
        await this.db.selectFrom(table).select("id").limit(1).execute();
      }
      return true;
    } catch (error) {
      console.log("Database not initialized or missing tables:", error);
      return false;
    }
  }
  /**
   * Migrate data from existing Prisma database
   */
  async migrateFromPrisma(prismaDbPath) {
    if (!fs.existsSync(prismaDbPath)) {
      console.log("No existing Prisma database found, skipping migration");
      return;
    }
    console.log("Starting data migration from Prisma database...");
    try {
      await this.createDefaultData();
      console.log("✅ Data migration completed successfully");
    } catch (error) {
      console.error("❌ Data migration failed:", error);
      throw error;
    }
  }
  /**
   * Create default data for new installations
   */
  async createDefaultData() {
    console.log("Creating default data...");
    const existingUsers = await this.db.selectFrom("User").select("id").limit(1).execute();
    if (existingUsers.length === 0) {
      await this.db.insertInto("User").values({
        id: "default-user",
        username: "user",
        password: "password",
        // In real app, this should be hashed
        settings: serializeJson({
          theme: "light",
          language: "zh-CN"
        })
      }).execute();
      console.log("✅ Default user created");
    }
    const existingAreas = await this.db.selectFrom("Area").select("id").limit(1).execute();
    if (existingAreas.length === 0) {
      const defaultAreas = [
        {
          id: "area-health",
          name: "健康",
          description: "身体健康和运动",
          icon: "🏃‍♂️",
          color: "#10B981",
          archived: booleanToInt(false),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        },
        {
          id: "area-work",
          name: "工作",
          description: "职业发展和工作项目",
          icon: "💼",
          color: "#3B82F6",
          archived: booleanToInt(false),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        },
        {
          id: "area-learning",
          name: "学习",
          description: "知识学习和技能提升",
          icon: "📚",
          color: "#8B5CF6",
          archived: booleanToInt(false),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        }
      ];
      await this.db.insertInto("Area").values(defaultAreas).execute();
      console.log("✅ Default areas created");
    }
    const existingTemplates = await this.db.selectFrom("ReviewTemplate").select("id").limit(1).execute();
    if (existingTemplates.length === 0) {
      const defaultTemplates = [
        {
          id: "template-daily",
          name: "每日回顾",
          description: "每日反思和计划",
          type: "daily",
          structure: serializeJson([
            { type: "text", label: "今天完成了什么？", key: "completed" },
            { type: "text", label: "遇到了什么挑战？", key: "challenges" },
            { type: "text", label: "明天的重点是什么？", key: "tomorrow" }
          ]),
          isDefault: booleanToInt(true),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        },
        {
          id: "template-weekly",
          name: "每周回顾",
          description: "每周总结和规划",
          type: "weekly",
          structure: serializeJson([
            { type: "text", label: "本周主要成就", key: "achievements" },
            { type: "text", label: "需要改进的地方", key: "improvements" },
            { type: "text", label: "下周目标", key: "next_goals" }
          ]),
          isDefault: booleanToInt(true),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        }
      ];
      await this.db.insertInto("ReviewTemplate").values(defaultTemplates).execute();
      console.log("✅ Default review templates created");
    }
    console.log("✅ Default data creation completed");
  }
  /**
   * Verify database integrity
   */
  async verifyDatabaseIntegrity() {
    try {
      console.log("Verifying database integrity...");
      const areas = await this.db.selectFrom("Area").select("id").execute();
      const projects = await this.db.selectFrom("Project").selectAll().execute();
      const tasks = await this.db.selectFrom("Task").selectAll().execute();
      for (const project of projects) {
        if (project.areaId && !areas.find((a) => a.id === project.areaId)) {
          console.warn(`Project ${project.id} references non-existent area ${project.areaId}`);
          return false;
        }
      }
      for (const task of tasks) {
        if (task.areaId && !areas.find((a) => a.id === task.areaId)) {
          console.warn(`Task ${task.id} references non-existent area ${task.areaId}`);
          return false;
        }
        if (task.projectId && !projects.find((p) => p.id === task.projectId)) {
          console.warn(`Task ${task.id} references non-existent project ${task.projectId}`);
          return false;
        }
      }
      console.log("✅ Database integrity verification passed");
      return true;
    } catch (error) {
      console.error("❌ Database integrity verification failed:", error);
      return false;
    }
  }
  /**
   * Get migration status and statistics
   */
  async getMigrationStatus() {
    const initialized = await this.isDatabaseInitialized();
    if (!initialized) {
      return {
        initialized: false,
        tableCount: 0,
        recordCounts: {}
      };
    }
    const tables = [
      "User",
      "Area",
      "Project",
      "ProjectKPI",
      "KPIRecord",
      "Deliverable",
      "DeliverableResource",
      "AreaMetric",
      "AreaMetricRecord",
      "Habit",
      "HabitRecord",
      "Checklist",
      "ChecklistInstance",
      "Task",
      "Tag",
      "TaskTag",
      "RecurringTask",
      "ResourceLink",
      "Review",
      "ReviewTemplate",
      "InboxNote",
      "DocumentLink"
    ];
    const recordCounts = {};
    for (const table of tables) {
      try {
        const result = await this.db.selectFrom(table).select(this.db.fn.count("id").as("count")).executeTakeFirst();
        recordCounts[table] = Number(result?.count || 0);
      } catch (error) {
        recordCounts[table] = 0;
      }
    }
    return {
      initialized: true,
      tableCount: tables.length,
      recordCounts
    };
  }
}
class KyselyDatabaseInitializer {
  migrationManager = null;
  getMigrationManager() {
    if (!this.migrationManager) {
      this.migrationManager = new KyselyMigrationManager();
    }
    return this.migrationManager;
  }
  /**
   * Initialize the database system
   */
  async initialize() {
    console.log("🚀 Starting Kysely database initialization...");
    try {
      const db2 = createKyselyClient();
      console.log("✅ Kysely client created successfully");
      const migrationManager = this.getMigrationManager();
      const isInitialized = await migrationManager.isDatabaseInitialized();
      if (!isInitialized) {
        console.log("📦 Database not initialized, running setup...");
        await migrationManager.initializeDatabase();
        await migrationManager.createDefaultData();
        console.log("✅ Database initialization completed");
      } else {
        console.log("✅ Database already initialized");
      }
      const isValid = await migrationManager.verifyDatabaseIntegrity();
      if (!isValid) {
        throw new Error("Database integrity check failed");
      }
      const status = await migrationManager.getMigrationStatus();
      console.log("📊 Database Status:", {
        initialized: status.initialized,
        tables: status.tableCount,
        totalRecords: Object.values(status.recordCounts).reduce((sum, count) => sum + count, 0)
      });
      console.log("🎉 Kysely database system ready!");
    } catch (error) {
      console.error("❌ Database initialization failed:", error);
      throw error;
    }
  }
  /**
   * Migrate from existing Prisma database
   */
  async migrateFromPrisma() {
    console.log("🔄 Starting migration from Prisma...");
    try {
      const isDevelopment = process.env.NODE_ENV === "development" || !electron.app.isPackaged;
      const prismaDbPath = isDevelopment ? path.join(process.cwd(), "prisma", "dev.db") : path.join(electron.app.getPath("userData"), "prisma", "dev.db");
      const migrationManager = this.getMigrationManager();
      await migrationManager.migrateFromPrisma(prismaDbPath);
      console.log("✅ Prisma migration completed");
    } catch (error) {
      console.error("❌ Prisma migration failed:", error);
      throw error;
    }
  }
  /**
   * Get current database status
   */
  async getStatus() {
    try {
      const migrationManager = this.getMigrationManager();
      const status = await migrationManager.getMigrationStatus();
      const integrity = await migrationManager.verifyDatabaseIntegrity();
      return {
        ...status,
        integrity
      };
    } catch (error) {
      console.error("Failed to get database status:", error);
      return {
        initialized: false,
        tableCount: 0,
        recordCounts: {},
        integrity: false
      };
    }
  }
  /**
   * Cleanup database connections
   */
  async cleanup() {
    try {
      await closeKyselyClient();
      console.log("✅ Database connections closed");
    } catch (error) {
      console.error("❌ Database cleanup failed:", error);
    }
  }
  /**
   * Reset database (for development/testing)
   */
  async reset() {
    console.log("🔄 Resetting database...");
    try {
      const migrationManager = this.getMigrationManager();
      await migrationManager.initializeDatabase();
      await migrationManager.createDefaultData();
      console.log("✅ Database reset completed");
    } catch (error) {
      console.error("❌ Database reset failed:", error);
      throw error;
    }
  }
  /**
   * Backup database
   */
  async backup(backupPath) {
    console.log(`📦 Creating database backup at: ${backupPath}`);
    try {
      const isDevelopment = process.env.NODE_ENV === "development" || !electron.app.isPackaged;
      const currentDbPath = isDevelopment ? path.join(process.cwd(), "prisma", "dev.db") : path.join(electron.app.getPath("userData"), "paolife.db");
      const fs2 = require("fs");
      fs2.copyFileSync(currentDbPath, backupPath);
      console.log("✅ Database backup completed");
    } catch (error) {
      console.error("❌ Database backup failed:", error);
      throw error;
    }
  }
  /**
   * Restore database from backup
   */
  async restore(backupPath) {
    console.log(`🔄 Restoring database from: ${backupPath}`);
    try {
      const isDevelopment = process.env.NODE_ENV === "development" || !electron.app.isPackaged;
      const currentDbPath = isDevelopment ? path.join(process.cwd(), "prisma", "dev.db") : path.join(electron.app.getPath("userData"), "paolife.db");
      await closeKyselyClient();
      const fs2 = require("fs");
      fs2.copyFileSync(backupPath, currentDbPath);
      createKyselyClient();
      console.log("✅ Database restore completed");
    } catch (error) {
      console.error("❌ Database restore failed:", error);
      throw error;
    }
  }
}
let kyselyDatabaseInitializer = null;
function getKyselyDatabaseInitializer() {
  if (!kyselyDatabaseInitializer) {
    kyselyDatabaseInitializer = new KyselyDatabaseInitializer();
  }
  return kyselyDatabaseInitializer;
}
class DocumentLinkService {
  prisma = null;
  /**
   * Set the Prisma client instance
   */
  setPrismaClient(prisma) {
    this.prisma = prisma;
  }
  /**
   * Get the Prisma client instance
   */
  getClient() {
    if (!this.prisma) {
      throw new Error("Prisma client not initialized. Call setPrismaClient first.");
    }
    return this.prisma;
  }
  /**
   * Create a new document link
   */
  async createLink(data) {
    try {
      const client = this.getClient();
      const link = await client.documentLink.create({
        data: {
          sourceDocPath: data.sourceDocPath,
          sourceDocTitle: data.sourceDocTitle,
          targetDocPath: data.targetDocPath,
          targetDocTitle: data.targetDocTitle,
          linkText: data.linkText,
          displayText: data.displayText,
          linkType: data.linkType || "wikilink",
          startPosition: data.startPosition,
          endPosition: data.endPosition,
          lineNumber: data.lineNumber,
          columnNumber: data.columnNumber,
          contextBefore: data.contextBefore,
          contextAfter: data.contextAfter,
          linkStrength: data.linkStrength || 1,
          isValid: true,
          lastValidated: /* @__PURE__ */ new Date()
        }
      });
      return {
        success: true,
        data: link
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create document link"
      };
    }
  }
  /**
   * Get all links for a document (both incoming and outgoing)
   */
  async getDocumentLinks(docPath) {
    try {
      const client = this.getClient();
      const [backlinks, outlinks] = await Promise.all([
        // Backlinks: links pointing TO this document
        client.documentLink.findMany({
          where: { targetDocPath: docPath },
          orderBy: { createdAt: "desc" }
        }),
        // Outlinks: links FROM this document
        client.documentLink.findMany({
          where: { sourceDocPath: docPath },
          orderBy: { createdAt: "desc" }
        })
      ]);
      return {
        success: true,
        data: { backlinks, outlinks }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get document links"
      };
    }
  }
  /**
   * Get backlinks for a document
   */
  async getBacklinks(docPath) {
    try {
      const client = this.getClient();
      const backlinks = await client.documentLink.findMany({
        where: { targetDocPath: docPath },
        orderBy: { linkStrength: "desc" }
      });
      return {
        success: true,
        data: backlinks
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get backlinks"
      };
    }
  }
  /**
   * Get outlinks for a document
   */
  async getOutlinks(docPath) {
    try {
      const client = this.getClient();
      const outlinks = await client.documentLink.findMany({
        where: { sourceDocPath: docPath },
        orderBy: { lineNumber: "asc" }
      });
      return {
        success: true,
        data: outlinks
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get outlinks"
      };
    }
  }
  /**
   * Update a document link
   */
  async updateLink(data) {
    try {
      const client = this.getClient();
      const link = await client.documentLink.update({
        where: { id: data.id },
        data: {
          sourceDocTitle: data.sourceDocTitle,
          targetDocTitle: data.targetDocTitle,
          linkText: data.linkText,
          displayText: data.displayText,
          startPosition: data.startPosition,
          endPosition: data.endPosition,
          lineNumber: data.lineNumber,
          columnNumber: data.columnNumber,
          contextBefore: data.contextBefore,
          contextAfter: data.contextAfter,
          isValid: data.isValid,
          linkStrength: data.linkStrength,
          lastValidated: /* @__PURE__ */ new Date()
        }
      });
      return {
        success: true,
        data: link
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update document link"
      };
    }
  }
  /**
   * Delete a document link
   */
  async deleteLink(id) {
    try {
      const client = this.getClient();
      await client.documentLink.delete({
        where: { id }
      });
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete document link"
      };
    }
  }
  /**
   * Delete all links for a document (when document is deleted)
   */
  async deleteDocumentLinks(docPath) {
    try {
      const client = this.getClient();
      await client.documentLink.deleteMany({
        where: {
          OR: [{ sourceDocPath: docPath }, { targetDocPath: docPath }]
        }
      });
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete document links"
      };
    }
  }
  /**
   * Update document path (when document is moved/renamed)
   */
  async updateDocumentPath(oldPath, newPath, newTitle) {
    try {
      const client = this.getClient();
      await Promise.all([
        // Update as source document
        client.documentLink.updateMany({
          where: { sourceDocPath: oldPath },
          data: {
            sourceDocPath: newPath,
            sourceDocTitle: newTitle,
            lastValidated: /* @__PURE__ */ new Date()
          }
        }),
        // Update as target document
        client.documentLink.updateMany({
          where: { targetDocPath: oldPath },
          data: {
            targetDocPath: newPath,
            targetDocTitle: newTitle,
            lastValidated: /* @__PURE__ */ new Date()
          }
        })
      ]);
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update document path"
      };
    }
  }
  /**
   * Mark links as invalid when target document doesn't exist
   */
  async markLinksAsInvalid(targetDocPath) {
    try {
      const client = this.getClient();
      await client.documentLink.updateMany({
        where: { targetDocPath },
        data: {
          isValid: false,
          lastValidated: /* @__PURE__ */ new Date()
        }
      });
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to mark links as invalid"
      };
    }
  }
  /**
   * Mark links as valid when target document exists
   */
  async markLinksAsValid(targetDocPath) {
    try {
      const client = this.getClient();
      await client.documentLink.updateMany({
        where: { targetDocPath },
        data: {
          isValid: true,
          lastValidated: /* @__PURE__ */ new Date()
        }
      });
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to mark links as valid"
      };
    }
  }
  /**
   * Get link statistics for a document
   */
  async getLinkStatistics(docPath) {
    try {
      const client = this.getClient();
      const [backlinks, outlinks] = await Promise.all([
        client.documentLink.findMany({
          where: { targetDocPath: docPath }
        }),
        client.documentLink.findMany({
          where: { sourceDocPath: docPath }
        })
      ]);
      const totalLinks = backlinks.length + outlinks.length;
      const validLinks = [...backlinks, ...outlinks].filter((link) => link.isValid).length;
      const invalidLinks = totalLinks - validLinks;
      const allLinks = [...backlinks, ...outlinks];
      const linkStrengthAvg = allLinks.length > 0 ? allLinks.reduce((sum, link) => sum + link.linkStrength, 0) / allLinks.length : 0;
      return {
        success: true,
        data: {
          totalLinks,
          validLinks,
          invalidLinks,
          backlinkCount: backlinks.length,
          outlinkCount: outlinks.length,
          linkStrengthAvg
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get link statistics"
      };
    }
  }
  /**
   * Search links by text content
   */
  async searchLinks(query, filters) {
    try {
      const client = this.getClient();
      const where = {
        OR: [
          { linkText: { contains: query } },
          { displayText: { contains: query } },
          { contextBefore: { contains: query } },
          { contextAfter: { contains: query } }
        ]
      };
      if (filters) {
        if (filters.sourceDocPath) {
          where.sourceDocPath = filters.sourceDocPath;
        }
        if (filters.targetDocPath) {
          where.targetDocPath = filters.targetDocPath;
        }
        if (filters.linkType) {
          where.linkType = filters.linkType;
        }
        if (filters.isValid !== void 0) {
          where.isValid = filters.isValid;
        }
      }
      const links = await client.documentLink.findMany({
        where,
        orderBy: { linkStrength: "desc" },
        take: 50
        // Limit results
      });
      return {
        success: true,
        data: links
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to search links"
      };
    }
  }
  /**
   * Bulk replace links for a document (used when document content changes)
   */
  async replaceDocumentLinks(sourceDocPath, links) {
    try {
      const client = this.getClient();
      await client.documentLink.deleteMany({
        where: { sourceDocPath }
      });
      const createdLinks = await Promise.all(
        links.map(
          (linkData) => client.documentLink.create({
            data: {
              sourceDocPath: linkData.sourceDocPath,
              sourceDocTitle: linkData.sourceDocTitle,
              targetDocPath: linkData.targetDocPath,
              targetDocTitle: linkData.targetDocTitle,
              linkText: linkData.linkText,
              displayText: linkData.displayText,
              linkType: linkData.linkType || "wikilink",
              startPosition: linkData.startPosition,
              endPosition: linkData.endPosition,
              lineNumber: linkData.lineNumber,
              columnNumber: linkData.columnNumber,
              contextBefore: linkData.contextBefore,
              contextAfter: linkData.contextAfter,
              linkStrength: linkData.linkStrength || 1,
              isValid: true,
              lastValidated: /* @__PURE__ */ new Date()
            }
          })
        )
      );
      return {
        success: true,
        data: createdLinks
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to replace document links"
      };
    }
  }
}
const documentLinkService = new DocumentLinkService();
class ReviewDataAggregator {
  prisma;
  constructor(database) {
    this.prisma = database.getClient();
  }
  /**
   * Parse period string and return date range
   */
  parsePeriod(type, period) {
    let startDate;
    let endDate;
    switch (type) {
      case "daily":
        startDate = new Date(period);
        endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + 1);
        break;
      case "weekly":
        const [year, weekStr] = period.split("-W");
        const week = parseInt(weekStr);
        startDate = this.getDateOfWeek(parseInt(year), week);
        endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + 7);
        break;
      case "monthly":
        const [monthYear, month] = period.split("-");
        startDate = new Date(parseInt(monthYear), parseInt(month) - 1, 1);
        endDate = new Date(parseInt(monthYear), parseInt(month), 1);
        break;
      case "quarterly":
        const [qYear, quarterStr] = period.split("-Q");
        const quarter = parseInt(quarterStr);
        const startMonth = (quarter - 1) * 3;
        startDate = new Date(parseInt(qYear), startMonth, 1);
        endDate = new Date(parseInt(qYear), startMonth + 3, 1);
        break;
      case "yearly":
        const yearNum = parseInt(period);
        startDate = new Date(yearNum, 0, 1);
        endDate = new Date(yearNum + 1, 0, 1);
        break;
      default:
        throw new Error(`Unsupported period type: ${type}`);
    }
    return {
      type,
      period,
      startDate,
      endDate
    };
  }
  /**
   * Get date of the first day of a specific week
   */
  getDateOfWeek(year, week) {
    const simple = new Date(year, 0, 1 + (week - 1) * 7);
    const dow = simple.getDay();
    const ISOweekStart = simple;
    if (dow <= 4) {
      ISOweekStart.setDate(simple.getDate() - simple.getDay() + 1);
    } else {
      ISOweekStart.setDate(simple.getDate() + 8 - simple.getDay());
    }
    return ISOweekStart;
  }
  /**
   * Aggregate project data for the review period
   */
  async aggregateProjectData(period) {
    const { startDate, endDate } = period;
    const allProjects = await this.prisma.project.findMany({
      include: {
        tasks: {
          where: {
            OR: [
              { createdAt: { gte: startDate, lt: endDate } },
              { completedAt: { gte: startDate, lt: endDate } }
            ]
          }
        }
      }
    });
    const completedProjects = allProjects.filter(
      (p) => p.completedAt && p.completedAt >= startDate && p.completedAt < endDate
    );
    const inProgressProjects = allProjects.filter(
      (p) => p.status === "active" && !p.completedAt
    );
    const allTasks = allProjects.flatMap((p) => p.tasks);
    const completedTasks = allTasks.filter(
      (t) => t.completedAt && t.completedAt >= startDate && t.completedAt < endDate
    );
    const completionRate = allTasks.length > 0 ? completedTasks.length / allTasks.length * 100 : 0;
    const recentCompletions = completedProjects.sort((a, b) => (b.completedAt?.getTime() || 0) - (a.completedAt?.getTime() || 0)).slice(0, 5).map((p) => ({
      name: p.name,
      completedAt: p.completedAt
    }));
    return {
      completed: completedProjects.length,
      inProgress: inProgressProjects.length,
      total: allProjects.length,
      completedTasks: completedTasks.length,
      totalTasks: allTasks.length,
      completionRate: Math.round(completionRate),
      recentCompletions
    };
  }
  /**
   * Aggregate area and habit data for the review period
   */
  async aggregateAreaData(period) {
    const { startDate, endDate } = period;
    const areas = await this.prisma.area.findMany({
      include: {
        habits: {
          include: {
            completions: {
              where: {
                date: { gte: startDate, lt: endDate }
              }
            }
          }
        },
        kpis: {
          include: {
            values: {
              where: {
                date: { gte: startDate, lt: endDate }
              },
              orderBy: { date: "desc" }
            }
          }
        }
      }
    });
    let totalHabitsTracked = 0;
    let totalHabitsCompleted = 0;
    const recentHabits = [];
    areas.forEach((area) => {
      area.habits.forEach((habit) => {
        const daysInPeriod = Math.ceil((endDate.getTime() - startDate.getTime()) / (1e3 * 60 * 60 * 24));
        const completedDays = habit.completions.length;
        totalHabitsTracked += daysInPeriod;
        totalHabitsCompleted += completedDays;
        recentHabits.push({
          areaName: area.name,
          habitName: habit.name,
          completedDays,
          totalDays: daysInPeriod
        });
      });
    });
    const habitCompletionRate = totalHabitsTracked > 0 ? totalHabitsCompleted / totalHabitsTracked * 100 : 0;
    const kpiChanges = [];
    areas.forEach((area) => {
      area.kpis.forEach((kpi) => {
        if (kpi.values.length >= 2) {
          const latest = kpi.values[0];
          const previous = kpi.values[kpi.values.length - 1];
          const change = latest.value - previous.value;
          const changePercent = previous.value !== 0 ? change / previous.value * 100 : 0;
          kpiChanges.push({
            areaName: area.name,
            kpiName: kpi.name,
            previousValue: previous.value,
            currentValue: latest.value,
            change,
            changePercent: Math.round(changePercent * 100) / 100
          });
        }
      });
    });
    return {
      total: areas.length,
      habitsTracked: totalHabitsTracked,
      habitsCompleted: totalHabitsCompleted,
      habitCompletionRate: Math.round(habitCompletionRate),
      kpiChanges,
      recentHabits: recentHabits.slice(0, 10)
    };
  }
  /**
   * Aggregate task data for the review period
   */
  async aggregateTaskData(period) {
    const { startDate, endDate } = period;
    const createdTasks = await this.prisma.task.findMany({
      where: {
        createdAt: { gte: startDate, lt: endDate }
      },
      include: {
        project: true
      }
    });
    const completedTasks = await this.prisma.task.findMany({
      where: {
        completedAt: { gte: startDate, lt: endDate }
      },
      include: {
        project: true
      }
    });
    const overdueTasks = await this.prisma.task.findMany({
      where: {
        dueDate: { lt: endDate },
        completedAt: null,
        status: { not: "completed" }
      }
    });
    const completionRate = createdTasks.length > 0 ? completedTasks.length / createdTasks.length * 100 : 0;
    const tasksWithCompletionTime = completedTasks.filter((t) => t.completedAt && t.createdAt);
    const avgCompletionTime = tasksWithCompletionTime.length > 0 ? tasksWithCompletionTime.reduce((sum, task) => {
      const completionTime = task.completedAt.getTime() - task.createdAt.getTime();
      return sum + completionTime;
    }, 0) / tasksWithCompletionTime.length / (1e3 * 60 * 60 * 24) : 0;
    const recentCompletions = completedTasks.sort((a, b) => (b.completedAt?.getTime() || 0) - (a.completedAt?.getTime() || 0)).slice(0, 10).map((task) => ({
      title: task.title,
      projectName: task.project?.name,
      completedAt: task.completedAt
    }));
    return {
      completed: completedTasks.length,
      created: createdTasks.length,
      overdue: overdueTasks.length,
      completionRate: Math.round(completionRate),
      avgCompletionTime: Math.round(avgCompletionTime * 10) / 10,
      recentCompletions
    };
  }
  /**
   * Generate insights based on aggregated data
   */
  generateInsights(data) {
    const achievements = [];
    const improvementAreas = [];
    if (data.projects.completionRate > 80) {
      achievements.push(`Excellent project completion rate of ${data.projects.completionRate}%`);
    }
    if (data.projects.completed > 0) {
      achievements.push(`Completed ${data.projects.completed} project${data.projects.completed > 1 ? "s" : ""}`);
    }
    if (data.areas.habitCompletionRate > 70) {
      achievements.push(`Strong habit consistency at ${data.areas.habitCompletionRate}%`);
    }
    if (data.tasks.completionRate > 75) {
      achievements.push(`High task completion rate of ${data.tasks.completionRate}%`);
    }
    if (data.projects.completionRate < 50) {
      improvementAreas.push("Focus on completing more projects");
    }
    if (data.areas.habitCompletionRate < 50) {
      improvementAreas.push("Improve habit consistency");
    }
    if (data.tasks.overdue > 5) {
      improvementAreas.push("Reduce overdue tasks");
    }
    const mostActiveArea = data.areas.recentHabits.length > 0 ? data.areas.recentHabits.reduce(
      (prev, current) => current.completedDays > prev.completedDays ? current : prev
    ).areaName : void 0;
    const topProject = data.tasks.recentCompletions.length > 0 ? data.tasks.recentCompletions[0].projectName : void 0;
    return {
      mostActiveArea,
      topProject,
      achievements,
      improvementAreas
    };
  }
  /**
   * Aggregate all data for a review period
   */
  async aggregateReviewData(type, period) {
    const reviewPeriod = this.parsePeriod(type, period);
    const [projects, areas, tasks] = await Promise.all([
      this.aggregateProjectData(reviewPeriod),
      this.aggregateAreaData(reviewPeriod),
      this.aggregateTaskData(reviewPeriod)
    ]);
    const insights = this.generateInsights({ projects, areas, tasks });
    return {
      projects,
      areas,
      tasks,
      insights
    };
  }
}
class ReviewAnalyzer {
  prisma;
  constructor(database) {
    this.prisma = database.getClient();
  }
  /**
   * Analyze trends by comparing current period with previous period
   */
  async analyzeTrends(type, currentPeriod) {
    const previousPeriod = this.getPreviousPeriod(type, currentPeriod);
    const currentData = await this.getAggregatedData(type, currentPeriod);
    const previousData = await this.getAggregatedData(type, previousPeriod);
    const calculateTrend = (current, previous) => {
      const change = current - previous;
      const changePercent = previous > 0 ? change / previous * 100 : 0;
      let trend = "stable";
      if (Math.abs(changePercent) > 5) {
        trend = changePercent > 0 ? "up" : "down";
      }
      return { current, previous, trend, change: Math.round(changePercent * 100) / 100 };
    };
    return {
      projectCompletion: calculateTrend(
        currentData.projects?.completionRate || 0,
        previousData.projects?.completionRate || 0
      ),
      taskProductivity: calculateTrend(
        currentData.tasks?.completionRate || 0,
        previousData.tasks?.completionRate || 0
      ),
      habitConsistency: calculateTrend(
        currentData.areas?.habitCompletionRate || 0,
        previousData.areas?.habitCompletionRate || 0
      )
    };
  }
  /**
   * Generate predictions based on historical data
   */
  async generatePredictions(type, currentPeriod) {
    const periods = this.getHistoricalPeriods(type, currentPeriod, 3);
    const historicalData = await Promise.all(
      periods.map((period) => this.getAggregatedData(type, period))
    );
    const avgProjects = historicalData.reduce((sum, data) => sum + (data.projects?.completed || 0), 0) / historicalData.length;
    const avgTasks = historicalData.reduce((sum, data) => sum + (data.tasks?.completed || 0), 0) / historicalData.length;
    const avgHabitRate = historicalData.reduce((sum, data) => sum + (data.areas?.habitCompletionRate || 0), 0) / historicalData.length;
    const riskFactors = [];
    const currentData = historicalData[0];
    if ((currentData.tasks?.overdue || 0) > 5) {
      riskFactors.push("High number of overdue tasks");
    }
    if ((currentData.projects?.completionRate || 0) < 50) {
      riskFactors.push("Low project completion rate");
    }
    if ((currentData.areas?.habitCompletionRate || 0) < 60) {
      riskFactors.push("Declining habit consistency");
    }
    const goalAchievementProbability = Math.min(100, Math.max(
      0,
      avgProjects * 0.3 + avgTasks * 0.4 + avgHabitRate * 0.3
    ));
    const recommendedFocus = [];
    if (avgProjects < 2) recommendedFocus.push("Increase project completion");
    if (avgTasks < 10) recommendedFocus.push("Improve task productivity");
    if (avgHabitRate < 70) recommendedFocus.push("Strengthen habit consistency");
    return {
      nextPeriodProjections: {
        expectedProjects: Math.round(avgProjects),
        expectedTasks: Math.round(avgTasks),
        riskFactors
      },
      goalAchievementProbability: Math.round(goalAchievementProbability),
      recommendedFocus
    };
  }
  /**
   * Generate actionable recommendations
   */
  generateRecommendations(trends, predictions) {
    const immediate = [];
    const shortTerm = [];
    const longTerm = [];
    if (trends.taskProductivity.trend === "down") {
      immediate.push("Review and prioritize your current task list");
      immediate.push("Identify and eliminate time-wasting activities");
    }
    if (trends.habitConsistency.trend === "down") {
      immediate.push("Focus on maintaining your most important habits");
      immediate.push("Reduce habit complexity to improve consistency");
    }
    if (predictions.nextPeriodProjections.riskFactors.length > 0) {
      immediate.push("Address overdue tasks to prevent further delays");
    }
    if (trends.projectCompletion.trend === "down") {
      shortTerm.push("Break down large projects into smaller, manageable tasks");
      shortTerm.push("Set more realistic project timelines");
    }
    if (predictions.goalAchievementProbability < 70) {
      shortTerm.push("Reassess and adjust your goals to be more achievable");
      shortTerm.push("Implement better tracking and accountability systems");
    }
    shortTerm.push("Establish weekly review sessions to stay on track");
    if (trends.projectCompletion.current < 60) {
      longTerm.push("Develop better project planning and estimation skills");
      longTerm.push("Consider reducing the number of concurrent projects");
    }
    if (trends.habitConsistency.current < 70) {
      longTerm.push("Build a more sustainable daily routine");
      longTerm.push("Focus on habit stacking and environmental design");
    }
    longTerm.push("Invest in skills that will improve your overall productivity");
    return { immediate, shortTerm, longTerm };
  }
  /**
   * Evaluate goal progress and achievement
   */
  async evaluateGoalProgress(type, currentPeriod) {
    const data = await this.getAggregatedData(type, currentPeriod);
    const projectScore = Math.min(100, data.projects?.completionRate || 0);
    const habitScore = Math.min(100, data.areas?.habitCompletionRate || 0);
    const kpiScore = this.calculateKpiScore(data.areas?.kpiChanges || []);
    const overallScore = Math.round((projectScore + habitScore + kpiScore) / 3);
    const achievements = [];
    if (projectScore > 80) achievements.push("Excellent project completion rate");
    if (habitScore > 80) achievements.push("Outstanding habit consistency");
    if (kpiScore > 80) achievements.push("Strong KPI performance");
    if ((data.tasks?.completed || 0) > 20) achievements.push("High task completion volume");
    const gaps = [];
    if (projectScore < 50) gaps.push("Project completion needs improvement");
    if (habitScore < 50) gaps.push("Habit consistency requires attention");
    if (kpiScore < 50) gaps.push("KPI performance is below expectations");
    if ((data.tasks?.overdue || 0) > 5) gaps.push("Too many overdue tasks");
    return {
      overallScore,
      categoryScores: {
        projects: Math.round(projectScore),
        habits: Math.round(habitScore),
        kpis: Math.round(kpiScore)
      },
      achievements,
      gaps
    };
  }
  /**
   * Calculate KPI score based on changes
   */
  calculateKpiScore(kpiChanges) {
    if (kpiChanges.length === 0) return 50;
    const positiveChanges = kpiChanges.filter((kpi) => kpi.change > 0).length;
    const totalChanges = kpiChanges.length;
    return positiveChanges / totalChanges * 100;
  }
  /**
   * Get previous period string
   */
  getPreviousPeriod(type, currentPeriod) {
    switch (type) {
      case "daily":
        const date = new Date(currentPeriod);
        date.setDate(date.getDate() - 1);
        return date.toISOString().split("T")[0];
      case "weekly":
        const [year, weekStr] = currentPeriod.split("-W");
        const week = parseInt(weekStr);
        const prevWeek = week > 1 ? week - 1 : 52;
        const prevYear = week > 1 ? year : (parseInt(year) - 1).toString();
        return `${prevYear}-W${prevWeek.toString().padStart(2, "0")}`;
      case "monthly":
        const [monthYear, month] = currentPeriod.split("-");
        const prevMonth = parseInt(month) > 1 ? parseInt(month) - 1 : 12;
        const prevMonthYear = parseInt(month) > 1 ? monthYear : (parseInt(monthYear) - 1).toString();
        return `${prevMonthYear}-${prevMonth.toString().padStart(2, "0")}`;
      case "quarterly":
        const [qYear, quarterStr] = currentPeriod.split("-Q");
        const quarter = parseInt(quarterStr);
        const prevQuarter = quarter > 1 ? quarter - 1 : 4;
        const prevQYear = quarter > 1 ? qYear : (parseInt(qYear) - 1).toString();
        return `${prevQYear}-Q${prevQuarter}`;
      case "yearly":
        return (parseInt(currentPeriod) - 1).toString();
      default:
        return currentPeriod;
    }
  }
  /**
   * Get historical periods
   */
  getHistoricalPeriods(type, currentPeriod, count) {
    const periods = [currentPeriod];
    let period = currentPeriod;
    for (let i = 1; i < count; i++) {
      period = this.getPreviousPeriod(type, period);
      periods.push(period);
    }
    return periods;
  }
  /**
   * Get aggregated data for a specific period (placeholder - would use ReviewDataAggregator)
   */
  async getAggregatedData(type, period) {
    return {
      projects: { completionRate: 75, completed: 2 },
      tasks: { completionRate: 80, completed: 15, overdue: 3 },
      areas: { habitCompletionRate: 70, kpiChanges: [] }
    };
  }
  /**
   * Perform complete analysis for a review
   */
  async analyzeReview(type, period) {
    const [trends, predictions, goalProgress] = await Promise.all([
      this.analyzeTrends(type, period),
      this.generatePredictions(type, period),
      this.evaluateGoalProgress(type, period)
    ]);
    const recommendations = this.generateRecommendations(trends, predictions);
    return {
      trends,
      predictions,
      recommendations,
      goalProgress
    };
  }
}
class KyselyDatabaseExtensions {
  constructor(db2) {
    this.db = db2;
  }
  /**
   * PROJECT KPI OPERATIONS
   */
  async createProjectKPI(data) {
    try {
      const kpi = await this.db.insertInto("ProjectKPI").values({
        id: generateId(),
        name: data.name,
        value: data.value,
        target: data.target || null,
        unit: data.unit || null,
        frequency: data.frequency || null,
        direction: data.direction || "increase",
        projectId: data.projectId,
        updatedAt: getCurrentTimestamp()
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: kpi
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create project KPI"
      };
    }
  }
  async getProjectKPIs(projectId) {
    try {
      const kpis = await this.db.selectFrom("ProjectKPI").selectAll().where("projectId", "=", projectId).orderBy("updatedAt", "desc").execute();
      return {
        success: true,
        data: kpis
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get project KPIs"
      };
    }
  }
  async updateProjectKPI(id, data) {
    try {
      const updateData = {
        updatedAt: getCurrentTimestamp()
      };
      if (data.name !== void 0) updateData.name = data.name;
      if (data.value !== void 0) updateData.value = data.value;
      if (data.target !== void 0) updateData.target = data.target;
      if (data.unit !== void 0) updateData.unit = data.unit;
      if (data.frequency !== void 0) updateData.frequency = data.frequency;
      if (data.direction !== void 0) updateData.direction = data.direction;
      const kpi = await this.db.updateTable("ProjectKPI").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: kpi
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update project KPI"
      };
    }
  }
  async deleteProjectKPI(id) {
    try {
      await this.db.deleteFrom("ProjectKPI").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete project KPI"
      };
    }
  }
  /**
   * KPI RECORD OPERATIONS
   */
  async createKPIRecord(data) {
    try {
      const record = await this.db.insertInto("KPIRecord").values({
        id: generateId(),
        value: data.value,
        note: data.note || null,
        kpiId: data.kpiId,
        recordedAt: data.recordedAt || getCurrentTimestamp()
      }).returningAll().executeTakeFirstOrThrow();
      await this.db.updateTable("ProjectKPI").set({
        value: data.value,
        updatedAt: getCurrentTimestamp()
      }).where("id", "=", data.kpiId).execute();
      return {
        success: true,
        data: record
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create KPI record"
      };
    }
  }
  async getKPIRecords(kpiId, limit) {
    try {
      let query = this.db.selectFrom("KPIRecord").selectAll().where("kpiId", "=", kpiId).orderBy("recordedAt", "desc");
      if (limit) {
        query = query.limit(limit);
      }
      const records = await query.execute();
      return {
        success: true,
        data: records
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get KPI records"
      };
    }
  }
  async updateKPIRecord(id, data) {
    try {
      const updateData = {};
      if (data.value !== void 0) updateData.value = data.value;
      if (data.note !== void 0) updateData.note = data.note;
      if (data.recordedAt !== void 0) updateData.recordedAt = data.recordedAt;
      const record = await this.db.updateTable("KPIRecord").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: record
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update KPI record"
      };
    }
  }
  async deleteKPIRecord(id) {
    try {
      await this.db.deleteFrom("KPIRecord").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete KPI record"
      };
    }
  }
  /**
   * AREA METRIC OPERATIONS
   */
  async createAreaMetric(data) {
    try {
      const metric = await this.db.insertInto("AreaMetric").values({
        id: generateId(),
        name: data.name,
        value: data.value || "",
        target: data.target || null,
        unit: data.unit || null,
        trackingType: data.trackingType || "manual",
        direction: data.direction || "higher_better",
        areaId: data.areaId,
        relatedHabits: data.relatedHabits ? serializeJson(data.relatedHabits) : null,
        updatedAt: getCurrentTimestamp()
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...metric,
          relatedHabits: deserializeJson(metric.relatedHabits)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create area metric"
      };
    }
  }
  async getAreaMetrics(areaId) {
    try {
      const metrics = await this.db.selectFrom("AreaMetric").selectAll().where("areaId", "=", areaId).orderBy("updatedAt", "desc").execute();
      return {
        success: true,
        data: metrics.map((metric) => ({
          ...metric,
          relatedHabits: deserializeJson(metric.relatedHabits)
        }))
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get area metrics"
      };
    }
  }
  async updateAreaMetric(id, data) {
    try {
      const updateData = {
        updatedAt: getCurrentTimestamp()
      };
      if (data.name !== void 0) updateData.name = data.name;
      if (data.value !== void 0) updateData.value = data.value;
      if (data.target !== void 0) updateData.target = data.target;
      if (data.unit !== void 0) updateData.unit = data.unit;
      if (data.trackingType !== void 0) updateData.trackingType = data.trackingType;
      if (data.direction !== void 0) updateData.direction = data.direction;
      if (data.relatedHabits !== void 0) {
        updateData.relatedHabits = data.relatedHabits ? serializeJson(data.relatedHabits) : null;
      }
      const metric = await this.db.updateTable("AreaMetric").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...metric,
          relatedHabits: deserializeJson(metric.relatedHabits)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update area metric"
      };
    }
  }
  async deleteAreaMetric(id) {
    try {
      await this.db.deleteFrom("AreaMetric").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete area metric"
      };
    }
  }
  /**
   * AREA METRIC RECORD OPERATIONS
   */
  async createAreaMetricRecord(data) {
    try {
      const record = await this.db.insertInto("AreaMetricRecord").values({
        id: generateId(),
        value: data.value,
        note: data.note || null,
        metricId: data.metricId,
        recordedAt: data.recordedAt || getCurrentTimestamp(),
        source: data.source || null,
        confidence: data.confidence || null,
        tags: data.tags ? serializeJson(data.tags) : null,
        context: data.context || null
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...record,
          tags: deserializeJson(record.tags)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create area metric record"
      };
    }
  }
  async getAreaMetricRecords(metricId, limit) {
    try {
      let query = this.db.selectFrom("AreaMetricRecord").selectAll().where("metricId", "=", metricId).orderBy("recordedAt", "desc");
      if (limit) {
        query = query.limit(limit);
      }
      const records = await query.execute();
      return {
        success: true,
        data: records.map((record) => ({
          ...record,
          tags: deserializeJson(record.tags)
        }))
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get area metric records"
      };
    }
  }
  async updateAreaMetricRecord(id, data) {
    try {
      const updateData = {};
      if (data.value !== void 0) updateData.value = data.value;
      if (data.note !== void 0) updateData.note = data.note;
      if (data.recordedAt !== void 0) updateData.recordedAt = data.recordedAt;
      if (data.source !== void 0) updateData.source = data.source;
      if (data.confidence !== void 0) updateData.confidence = data.confidence;
      if (data.context !== void 0) updateData.context = data.context;
      if (data.tags !== void 0) {
        updateData.tags = data.tags ? serializeJson(data.tags) : null;
      }
      const record = await this.db.updateTable("AreaMetricRecord").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...record,
          tags: deserializeJson(record.tags)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update area metric record"
      };
    }
  }
  async deleteAreaMetricRecord(id) {
    try {
      await this.db.deleteFrom("AreaMetricRecord").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete area metric record"
      };
    }
  }
  /**
   * HABIT OPERATIONS
   */
  async createHabit(data) {
    try {
      const habit = await this.db.insertInto("Habit").values({
        id: generateId(),
        name: data.name,
        frequency: data.frequency,
        target: data.target,
        areaId: data.areaId,
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp()
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: habit
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create habit"
      };
    }
  }
  async getHabitsByArea(areaId) {
    try {
      const habits = await this.db.selectFrom("Habit").selectAll().where("areaId", "=", areaId).orderBy("updatedAt", "desc").execute();
      return {
        success: true,
        data: habits
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get habits by area"
      };
    }
  }
  async updateHabit(id, data) {
    try {
      const updateData = {
        updatedAt: getCurrentTimestamp()
      };
      if (data.name !== void 0) updateData.name = data.name;
      if (data.frequency !== void 0) updateData.frequency = data.frequency;
      if (data.target !== void 0) updateData.target = data.target;
      const habit = await this.db.updateTable("Habit").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: habit
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update habit"
      };
    }
  }
  async deleteHabit(id) {
    try {
      await this.db.deleteFrom("Habit").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete habit"
      };
    }
  }
}
class KyselyDatabaseExtensions2 {
  constructor(db2) {
    this.db = db2;
  }
  /**
   * HABIT RECORD OPERATIONS
   */
  async createHabitRecord(data) {
    try {
      console.log("Creating habit record with data:", JSON.stringify(data, null, 2));
      const insertData = {
        id: generateId(),
        date: data.date instanceof Date ? data.date.toISOString().split("T")[0] : String(data.date),
        // Convert Date to YYYY-MM-DD string
        completed: booleanToInt(data.completed ?? true),
        value: data.value ? Number(data.value) : null,
        // Ensure number or null
        note: data.note ? String(data.note) : null,
        // Ensure string or null
        habitId: String(data.habitId)
        // Ensure string
      };
      console.log("Inserting habit record with processed data:", JSON.stringify(insertData, null, 2));
      const record = await this.db.insertInto("HabitRecord").values(insertData).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...record,
          completed: intToBoolean(record.completed)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create habit record"
      };
    }
  }
  async updateHabitRecord(id, data) {
    try {
      console.log("Updating habit record:", id, "with data:", JSON.stringify(data, null, 2));
      const updateData = {};
      if (data.completed !== void 0) updateData.completed = booleanToInt(data.completed);
      if (data.value !== void 0) updateData.value = data.value;
      if (data.note !== void 0) updateData.note = data.note;
      if (Object.keys(updateData).length === 0) {
        throw new Error("No fields to update");
      }
      console.log("Update data:", JSON.stringify(updateData, null, 2));
      const record = await this.db.updateTable("HabitRecord").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...record,
          completed: intToBoolean(record.completed)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update habit record"
      };
    }
  }
  async getHabitRecords(habitId) {
    try {
      const records = await this.db.selectFrom("HabitRecord").selectAll().where("habitId", "=", habitId).orderBy("date", "desc").execute();
      return {
        success: true,
        data: records.map((record) => ({
          ...record,
          completed: intToBoolean(record.completed)
        }))
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get habit records"
      };
    }
  }
  async deleteHabitRecord(id) {
    try {
      await this.db.deleteFrom("HabitRecord").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete habit record"
      };
    }
  }
  /**
   * RECURRING TASK OPERATIONS
   */
  async createRecurringTask(data) {
    try {
      console.log("Creating recurring task with data:", JSON.stringify(data, null, 2));
      const insertData = {
        id: generateId(),
        title: String(data.title),
        description: data.description ? String(data.description) : null,
        repeatRule: String(data.repeatRule),
        repeatInterval: data.repeatInterval ? Number(data.repeatInterval) : 1,
        nextDueDate: data.nextDueDate ? String(data.nextDueDate) : null,
        lastCompletedAt: null,
        isActive: booleanToInt(true),
        areaId: String(data.areaId),
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp()
      };
      console.log("Inserting recurring task with processed data:", JSON.stringify(insertData, null, 2));
      const task = await this.db.insertInto("RecurringTask").values(insertData).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...task,
          isActive: intToBoolean(task.isActive)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create recurring task"
      };
    }
  }
  async getRecurringTasks(areaId) {
    try {
      const tasks = await this.db.selectFrom("RecurringTask").selectAll().where("areaId", "=", areaId).where("isActive", "=", booleanToInt(true)).orderBy("updatedAt", "desc").execute();
      return {
        success: true,
        data: tasks.map((task) => ({
          ...task,
          isActive: intToBoolean(task.isActive)
        }))
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get recurring tasks"
      };
    }
  }
  async updateRecurringTask(id, data) {
    try {
      const updateData = {
        updatedAt: getCurrentTimestamp()
      };
      if (data.title !== void 0) updateData.title = data.title;
      if (data.description !== void 0) updateData.description = data.description;
      if (data.repeatRule !== void 0) updateData.repeatRule = data.repeatRule;
      if (data.repeatInterval !== void 0) updateData.repeatInterval = data.repeatInterval;
      if (data.nextDueDate !== void 0) updateData.nextDueDate = data.nextDueDate;
      if (data.lastCompletedAt !== void 0) updateData.lastCompletedAt = data.lastCompletedAt;
      if (data.isActive !== void 0) updateData.isActive = booleanToInt(data.isActive);
      const task = await this.db.updateTable("RecurringTask").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...task,
          isActive: intToBoolean(task.isActive)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update recurring task"
      };
    }
  }
  async deleteRecurringTask(id) {
    try {
      await this.db.deleteFrom("RecurringTask").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete recurring task"
      };
    }
  }
  /**
   * DELIVERABLE OPERATIONS
   */
  async createDeliverable(data) {
    try {
      console.log("Creating deliverable with data:", JSON.stringify(data, null, 2));
      const insertData = {
        id: generateId(),
        title: String(data.title),
        description: data.description ? String(data.description) : null,
        type: data.type ? String(data.type) : "document",
        status: data.status ? String(data.status) : "planned",
        content: data.content ? String(data.content) : null,
        url: data.url ? String(data.url) : null,
        filePath: data.filePath ? String(data.filePath) : null,
        acceptanceCriteria: data.acceptanceCriteria ? serializeJson(data.acceptanceCriteria) : null,
        plannedDate: data.plannedDate ? data.plannedDate instanceof Date ? data.plannedDate.toISOString().split("T")[0] : String(data.plannedDate) : null,
        actualDate: data.actualDate ? data.actualDate instanceof Date ? data.actualDate.toISOString().split("T")[0] : String(data.actualDate) : null,
        projectId: String(data.projectId),
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp()
      };
      console.log("Inserting deliverable with processed data:", JSON.stringify(insertData, null, 2));
      const deliverable = await this.db.insertInto("Deliverable").values(insertData).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...deliverable,
          acceptanceCriteria: deserializeJson(deliverable.acceptanceCriteria)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create deliverable"
      };
    }
  }
  async getProjectDeliverables(projectId) {
    try {
      const deliverables = await this.db.selectFrom("Deliverable").selectAll().where("projectId", "=", projectId).orderBy("updatedAt", "desc").execute();
      return {
        success: true,
        data: deliverables.map((deliverable) => ({
          ...deliverable,
          acceptanceCriteria: deserializeJson(deliverable.acceptanceCriteria)
        }))
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get project deliverables"
      };
    }
  }
  async updateDeliverable(id, data) {
    try {
      const updateData = {
        updatedAt: getCurrentTimestamp()
      };
      if (data.title !== void 0) updateData.title = data.title;
      if (data.description !== void 0) updateData.description = data.description;
      if (data.type !== void 0) updateData.type = data.type;
      if (data.status !== void 0) updateData.status = data.status;
      if (data.content !== void 0) updateData.content = data.content;
      if (data.url !== void 0) updateData.url = data.url;
      if (data.filePath !== void 0) updateData.filePath = data.filePath;
      if (data.plannedDate !== void 0) updateData.plannedDate = data.plannedDate;
      if (data.actualDate !== void 0) updateData.actualDate = data.actualDate;
      if (data.acceptanceCriteria !== void 0) {
        updateData.acceptanceCriteria = data.acceptanceCriteria ? serializeJson(data.acceptanceCriteria) : null;
      }
      const deliverable = await this.db.updateTable("Deliverable").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...deliverable,
          acceptanceCriteria: deserializeJson(deliverable.acceptanceCriteria)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update deliverable"
      };
    }
  }
  async deleteDeliverable(id) {
    try {
      await this.db.deleteFrom("Deliverable").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete deliverable"
      };
    }
  }
}
class KyselyDatabaseExtensions3 {
  constructor(db2) {
    this.db = db2;
  }
  /**
   * REVIEW OPERATIONS
   */
  async createReview(data) {
    try {
      const review = await this.db.insertInto("Review").values({
        id: generateId(),
        type: data.type,
        period: data.period,
        title: data.title || null,
        content: serializeJson(data.content),
        status: data.status || "draft",
        templateId: data.templateId || null,
        completedAt: null,
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp()
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...review,
          content: deserializeJson(review.content)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create review"
      };
    }
  }
  async getReviews(filters = {}) {
    try {
      let query = this.db.selectFrom("Review").selectAll().orderBy("createdAt", "desc");
      if (filters.type) {
        query = query.where("type", "=", filters.type);
      }
      if (filters.period) {
        query = query.where("period", "=", filters.period);
      }
      if (filters.status) {
        query = query.where("status", "=", filters.status);
      }
      if (filters.limit) {
        query = query.limit(filters.limit);
      }
      const reviews = await query.execute();
      return {
        success: true,
        data: reviews.map((review) => ({
          ...review,
          content: deserializeJson(review.content)
        }))
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get reviews"
      };
    }
  }
  async getReviewById(id) {
    try {
      const review = await this.db.selectFrom("Review").selectAll().where("id", "=", id).executeTakeFirst();
      if (!review) {
        throw new Error("Review not found");
      }
      return {
        success: true,
        data: {
          ...review,
          content: deserializeJson(review.content)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get review by id"
      };
    }
  }
  async updateReview(id, data) {
    try {
      const updateData = {
        updatedAt: getCurrentTimestamp()
      };
      if (data.title !== void 0) updateData.title = data.title;
      if (data.content !== void 0) updateData.content = serializeJson(data.content);
      if (data.status !== void 0) updateData.status = data.status;
      if (data.completedAt !== void 0) updateData.completedAt = data.completedAt;
      const review = await this.db.updateTable("Review").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...review,
          content: deserializeJson(review.content)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update review"
      };
    }
  }
  async deleteReview(id) {
    try {
      await this.db.deleteFrom("Review").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete review"
      };
    }
  }
  /**
   * REVIEW TEMPLATE OPERATIONS
   */
  async createReviewTemplate(data) {
    try {
      const template = await this.db.insertInto("ReviewTemplate").values({
        id: generateId(),
        name: data.name,
        description: data.description || null,
        type: data.type,
        structure: serializeJson(data.structure),
        isDefault: booleanToInt(data.isDefault || false),
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp()
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...template,
          structure: deserializeJson(template.structure),
          isDefault: intToBoolean(template.isDefault)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create review template"
      };
    }
  }
  async getReviewTemplates(type) {
    try {
      let query = this.db.selectFrom("ReviewTemplate").selectAll().orderBy("createdAt", "desc");
      if (type) {
        query = query.where("type", "=", type);
      }
      const templates = await query.execute();
      return {
        success: true,
        data: templates.map((template) => ({
          ...template,
          structure: deserializeJson(template.structure),
          isDefault: intToBoolean(template.isDefault)
        }))
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get review templates"
      };
    }
  }
  async getReviewTemplateById(id) {
    try {
      const template = await this.db.selectFrom("ReviewTemplate").selectAll().where("id", "=", id).executeTakeFirst();
      if (!template) {
        throw new Error("Review template not found");
      }
      return {
        success: true,
        data: {
          ...template,
          structure: deserializeJson(template.structure),
          isDefault: intToBoolean(template.isDefault)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get review template by id"
      };
    }
  }
  async updateReviewTemplate(id, data) {
    try {
      const updateData = {
        updatedAt: getCurrentTimestamp()
      };
      if (data.name !== void 0) updateData.name = data.name;
      if (data.description !== void 0) updateData.description = data.description;
      if (data.structure !== void 0) updateData.structure = serializeJson(data.structure);
      if (data.isDefault !== void 0) updateData.isDefault = booleanToInt(data.isDefault);
      const template = await this.db.updateTable("ReviewTemplate").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...template,
          structure: deserializeJson(template.structure),
          isDefault: intToBoolean(template.isDefault)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update review template"
      };
    }
  }
  async deleteReviewTemplate(id) {
    try {
      await this.db.deleteFrom("ReviewTemplate").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete review template"
      };
    }
  }
  /**
   * CHECKLIST OPERATIONS
   */
  async createChecklist(data) {
    try {
      const checklist = await this.db.insertInto("Checklist").values({
        id: generateId(),
        name: data.name,
        template: serializeJson(data.template),
        areaId: data.areaId,
        createdAt: getCurrentTimestamp()
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...checklist,
          template: deserializeJson(checklist.template)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create checklist"
      };
    }
  }
  async getChecklists(areaId) {
    try {
      let query = this.db.selectFrom("Checklist").selectAll().orderBy("createdAt", "desc");
      if (areaId) {
        query = query.where("areaId", "=", areaId);
      }
      const checklists = await query.execute();
      return {
        success: true,
        data: checklists.map((checklist) => ({
          ...checklist,
          template: deserializeJson(checklist.template)
        }))
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get checklists"
      };
    }
  }
  async updateChecklist(id, data) {
    try {
      const updateData = {};
      if (data.name !== void 0) updateData.name = data.name;
      if (data.template !== void 0) updateData.template = serializeJson(data.template);
      const checklist = await this.db.updateTable("Checklist").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...checklist,
          template: deserializeJson(checklist.template)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update checklist"
      };
    }
  }
  async deleteChecklist(id) {
    try {
      await this.db.deleteFrom("Checklist").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete checklist"
      };
    }
  }
  /**
   * CHECKLIST INSTANCE OPERATIONS
   */
  async createChecklistInstance(data) {
    try {
      const instance = await this.db.insertInto("ChecklistInstance").values({
        id: generateId(),
        status: serializeJson(data.status),
        checklistId: data.checklistId,
        completedAt: null,
        createdAt: getCurrentTimestamp()
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...instance,
          status: deserializeJson(instance.status)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create checklist instance"
      };
    }
  }
  async getChecklistInstances(areaId) {
    try {
      let query = this.db.selectFrom("ChecklistInstance").selectAll().orderBy("createdAt", "desc");
      if (areaId) {
        query = this.db.selectFrom("ChecklistInstance").innerJoin("Checklist", "Checklist.id", "ChecklistInstance.checklistId").selectAll("ChecklistInstance").where("Checklist.areaId", "=", areaId).orderBy("ChecklistInstance.createdAt", "desc");
      }
      const instances = await query.execute();
      return {
        success: true,
        data: instances.map((instance) => ({
          ...instance,
          status: deserializeJson(instance.status)
        }))
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get checklist instances"
      };
    }
  }
  async updateChecklistInstance(id, data) {
    try {
      const updateData = {};
      if (data.status !== void 0) updateData.status = serializeJson(data.status);
      if (data.completedAt !== void 0) updateData.completedAt = data.completedAt;
      const instance = await this.db.updateTable("ChecklistInstance").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...instance,
          status: deserializeJson(instance.status)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update checklist instance"
      };
    }
  }
  async deleteChecklistInstance(id) {
    try {
      await this.db.deleteFrom("ChecklistInstance").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete checklist instance"
      };
    }
  }
  /**
   * RESOURCE LINK OPERATIONS
   */
  async getProjectResources(projectId) {
    try {
      const resources = await this.db.selectFrom("ResourceLink").selectAll().where("projectId", "=", projectId).execute();
      return {
        success: true,
        data: resources
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get project resources"
      };
    }
  }
  async linkResourceToProject(data) {
    try {
      const resource = await this.db.insertInto("ResourceLink").values({
        id: generateId(),
        resourcePath: data.resourcePath,
        title: data.title || null,
        projectId: data.projectId,
        areaId: null
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: resource
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to link resource to project"
      };
    }
  }
  async unlinkResourceFromProject(resourceId, projectId) {
    try {
      await this.db.deleteFrom("ResourceLink").where("id", "=", resourceId).where("projectId", "=", projectId).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to unlink resource from project"
      };
    }
  }
  async getAreaResources(areaId) {
    try {
      const resources = await this.db.selectFrom("ResourceLink").selectAll().where("areaId", "=", areaId).execute();
      return {
        success: true,
        data: resources
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get area resources"
      };
    }
  }
  async getResourceReferences(resourcePath) {
    try {
      const [projectResources, areaResources] = await Promise.all([
        this.db.selectFrom("ResourceLink").innerJoin("Project", "Project.id", "ResourceLink.projectId").select(["Project.id", "Project.name", "ResourceLink.title"]).where("ResourceLink.resourcePath", "=", resourcePath).where("ResourceLink.projectId", "is not", null).execute(),
        this.db.selectFrom("ResourceLink").innerJoin("Area", "Area.id", "ResourceLink.areaId").select(["Area.id", "Area.name", "ResourceLink.title"]).where("ResourceLink.resourcePath", "=", resourcePath).where("ResourceLink.areaId", "is not", null).execute()
      ]);
      return {
        success: true,
        data: {
          projects: projectResources,
          areas: areaResources
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get resource references"
      };
    }
  }
  async unlinkResourceFromArea(resourceId, areaId) {
    try {
      await this.db.deleteFrom("ResourceLink").where("id", "=", resourceId).where("areaId", "=", areaId).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to unlink resource from area"
      };
    }
  }
}
class KyselyDatabaseExtensions4 {
  constructor(db2) {
    this.db = db2;
  }
  /**
   * TASK OPERATIONS
   */
  async createTask(data) {
    try {
      return await withTransaction(this.db, async (trx) => {
        const task = await trx.insertInto("Task").values({
          id: generateId(),
          content: data.title,
          // Map title to content
          description: data.description || null,
          priority: data.priority || null,
          dueDate: data.dueDate || null,
          projectId: data.projectId || null,
          areaId: data.areaId || null,
          parentId: data.parentTaskId || null,
          // Map parentTaskId to parentId
          completed: booleanToInt(false),
          completedAt: null,
          position: 0,
          sourceType: null,
          sourceId: null,
          sourceContext: null,
          resourceLinkId: null,
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        }).returningAll().executeTakeFirstOrThrow();
        if (data.tags && data.tags.length > 0) {
          for (const tagName of data.tags) {
            let tag = await trx.selectFrom("Tag").selectAll().where("name", "=", tagName).executeTakeFirst();
            if (!tag) {
              tag = await trx.insertInto("Tag").values({
                id: generateId(),
                name: tagName,
                color: null,
                icon: null
              }).returningAll().executeTakeFirstOrThrow();
            }
            await trx.insertInto("TaskTag").values({
              taskId: task.id,
              tagId: tag.id
            }).execute();
          }
        }
        console.log("Created task from database:", task);
        const mappedTask = {
          ...task,
          completed: intToBoolean(task.completed),
          // Ensure field mapping for frontend compatibility
          deadline: task.dueDate,
          // Map dueDate back to deadline for frontend
          parentId: task.parentId
          // Ensure parentId is correctly mapped
        };
        console.log("Mapped task for frontend:", mappedTask);
        return {
          success: true,
          data: mappedTask
        };
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create task"
      };
    }
  }
  async getTasks(filters = {}) {
    try {
      let query = this.db.selectFrom("Task").selectAll().orderBy("updatedAt", "desc");
      if (filters.projectId) {
        query = query.where("projectId", "=", filters.projectId);
      }
      if (filters.areaId) {
        query = query.where("areaId", "=", filters.areaId);
      }
      if (filters.status) {
        query = query.where("status", "=", filters.status);
      }
      if (filters.priority) {
        query = query.where("priority", "=", filters.priority);
      }
      if (filters.parentTaskId !== void 0) {
        if (filters.parentTaskId === null) {
          query = query.where("parentTaskId", "is", null);
        } else {
          query = query.where("parentTaskId", "=", filters.parentTaskId);
        }
      }
      if (!filters.includeCompleted) {
        query = query.where("completed", "=", booleanToInt(false));
      }
      if (filters.tags && filters.tags.length > 0) {
        query = query.where(
          "id",
          "in",
          (eb) => eb.selectFrom("TaskTag").innerJoin("Tag", "Tag.id", "TaskTag.tagId").select("TaskTag.taskId").where("Tag.name", "in", filters.tags)
        );
      }
      if (filters.limit) {
        query = query.limit(filters.limit);
      }
      if (filters.offset) {
        query = query.offset(filters.offset);
      }
      const tasks = await query.execute();
      const tasksWithTags = await Promise.all(
        tasks.map(async (task) => {
          const tags = await this.db.selectFrom("TaskTag").innerJoin("Tag", "Tag.id", "TaskTag.tagId").select(["Tag.id", "Tag.name", "Tag.color"]).where("TaskTag.taskId", "=", task.id).execute();
          return {
            ...task,
            completed: intToBoolean(task.completed),
            tags
          };
        })
      );
      return {
        success: true,
        data: tasksWithTags
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get tasks"
      };
    }
  }
  async getTaskById(id) {
    try {
      const task = await this.db.selectFrom("Task").selectAll().where("id", "=", id).executeTakeFirst();
      if (!task) {
        throw new Error("Task not found");
      }
      const tags = await this.db.selectFrom("TaskTag").innerJoin("Tag", "Tag.id", "TaskTag.tagId").select(["Tag.id", "Tag.name", "Tag.color"]).where("TaskTag.taskId", "=", id).execute();
      const subtasks = await this.db.selectFrom("Task").selectAll().where("parentId", "=", id).execute();
      return {
        success: true,
        data: {
          ...task,
          completed: intToBoolean(task.completed),
          tags,
          subtasks: subtasks.map((subtask) => ({
            ...subtask,
            completed: intToBoolean(subtask.completed)
          }))
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get task by id"
      };
    }
  }
  async updateTask(id, data) {
    try {
      return await withTransaction(this.db, async (trx) => {
        const updateData = {
          updatedAt: getCurrentTimestamp()
        };
        if (data.title !== void 0) updateData.content = data.title;
        if (data.description !== void 0) updateData.description = data.description;
        if (data.priority !== void 0) updateData.priority = data.priority;
        if (data.dueDate !== void 0) updateData.dueDate = data.dueDate;
        if (data.completed !== void 0) updateData.completed = booleanToInt(data.completed);
        if (data.projectId !== void 0) updateData.projectId = data.projectId;
        if (data.areaId !== void 0) updateData.areaId = data.areaId;
        if (data.parentTaskId !== void 0) updateData.parentId = data.parentTaskId;
        const task = await trx.updateTable("Task").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
        if (data.tags !== void 0) {
          await trx.deleteFrom("TaskTag").where("taskId", "=", id).execute();
          if (data.tags.length > 0) {
            for (const tagName of data.tags) {
              let tag = await trx.selectFrom("TaskTag").selectAll().where("name", "=", tagName).executeTakeFirst();
              if (!tag) {
                tag = await trx.insertInto("TaskTag").values({
                  id: generateId(),
                  name: tagName,
                  color: null
                }).returningAll().executeTakeFirstOrThrow();
              }
              await trx.insertInto("TaskTag").values({
                taskId: id,
                tagId: tag.id
              }).execute();
            }
          }
        }
        const taskWithTags = await this.getTaskById(id);
        return taskWithTags;
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update task"
      };
    }
  }
  async deleteTask(id) {
    try {
      return await withTransaction(this.db, async (trx) => {
        await trx.deleteFrom("TaskTag").where("taskId", "=", id).execute();
        const subtasks = await trx.selectFrom("Task").select("id").where("parentId", "=", id).execute();
        for (const subtask of subtasks) {
          await trx.deleteFrom("TaskTag").where("taskId", "=", subtask.id).execute();
        }
        await trx.deleteFrom("Task").where("parentTaskId", "=", id).execute();
        await trx.deleteFrom("Task").where("id", "=", id).execute();
        return {
          success: true,
          data: void 0
        };
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete task"
      };
    }
  }
  /**
   * TASK TAG OPERATIONS
   */
  async createTaskTag(data) {
    try {
      const existingTag = await this.db.selectFrom("TaskTag").selectAll().where("name", "=", data.name).executeTakeFirst();
      if (existingTag) {
        return {
          success: false,
          error: "Tag with this name already exists"
        };
      }
      const tag = await this.db.insertInto("TaskTag").values({
        id: generateId(),
        name: data.name,
        color: data.color || null
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: tag
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create task tag"
      };
    }
  }
  async getTaskTags() {
    try {
      const tags = await this.db.selectFrom("TaskTag").selectAll().orderBy("name", "asc").execute();
      return {
        success: true,
        data: tags
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get task tags"
      };
    }
  }
  async updateTaskTag(id, data) {
    try {
      const updateData = {};
      if (data.name !== void 0) {
        const existingTag = await this.db.selectFrom("TaskTag").selectAll().where("name", "=", data.name).where("id", "!=", id).executeTakeFirst();
        if (existingTag) {
          return {
            success: false,
            error: "Tag with this name already exists"
          };
        }
        updateData.name = data.name;
      }
      if (data.color !== void 0) updateData.color = data.color;
      const tag = await this.db.updateTable("TaskTag").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: tag
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update task tag"
      };
    }
  }
  async deleteTaskTag(id) {
    try {
      return await withTransaction(this.db, async (trx) => {
        await trx.deleteFrom("TaskTag").where("tagId", "=", id).execute();
        await trx.deleteFrom("Tag").where("id", "=", id).execute();
        return {
          success: true,
          data: void 0
        };
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete task tag"
      };
    }
  }
  /**
   * USER SETTINGS OPERATIONS
   */
  async getUserSettings() {
    try {
      const user = await this.db.selectFrom("User").selectAll().executeTakeFirst();
      if (!user) {
        return {
          success: true,
          data: {
            theme: "system",
            language: "zh-CN",
            autoSave: true,
            notifications: true,
            shortcuts: {}
          }
        };
      }
      return {
        success: true,
        data: {
          theme: user.theme || "system",
          language: user.language || "zh-CN",
          autoSave: intToBoolean(user.autoSave || 1),
          notifications: intToBoolean(user.notifications || 1),
          shortcuts: deserializeJson(user.shortcuts) || {}
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get user settings"
      };
    }
  }
  async updateUserSettings(data) {
    try {
      const existingUser = await this.db.selectFrom("User").selectAll().executeTakeFirst();
      const updateData = {};
      if (data.theme !== void 0) updateData.theme = data.theme;
      if (data.language !== void 0) updateData.language = data.language;
      if (data.autoSave !== void 0) updateData.autoSave = booleanToInt(data.autoSave);
      if (data.notifications !== void 0) updateData.notifications = booleanToInt(data.notifications);
      if (data.shortcuts !== void 0) updateData.shortcuts = serializeJson(data.shortcuts);
      let user;
      if (existingUser) {
        user = await this.db.updateTable("User").set(updateData).where("id", "=", existingUser.id).returningAll().executeTakeFirstOrThrow();
      } else {
        user = await this.db.insertInto("User").values({
          id: generateId(),
          theme: data.theme || "system",
          language: data.language || "zh-CN",
          autoSave: booleanToInt(data.autoSave ?? true),
          notifications: booleanToInt(data.notifications ?? true),
          shortcuts: data.shortcuts ? serializeJson(data.shortcuts) : null
        }).returningAll().executeTakeFirstOrThrow();
      }
      return {
        success: true,
        data: {
          theme: user.theme,
          language: user.language,
          autoSave: intToBoolean(user.autoSave),
          notifications: intToBoolean(user.notifications),
          shortcuts: deserializeJson(user.shortcuts) || {}
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update user settings"
      };
    }
  }
}
class KyselyDatabaseService {
  db = null;
  config = null;
  dataAggregator = null;
  reviewAnalyzer = null;
  extensions = null;
  extensions2 = null;
  extensions3 = null;
  extensions4 = null;
  customWorkspaceDirectory = null;
  /**
   * 设置自定义工作目录
   * 注意：此方法只能在数据库初始化之前调用
   */
  setWorkspaceDirectory(directory) {
    if (this.db) {
      throw new Error("Cannot change workspace directory after database is initialized");
    }
    this.customWorkspaceDirectory = directory;
    console.log("Custom workspace directory set:", directory);
  }
  /**
   * 从持久化存储中读取用户设置
   * 尝试从Electron的localStorage存储位置读取Zustand持久化数据
   */
  loadUserSettingsFromStorage() {
    try {
      const userDataPath = electron.app.getPath("userData");
      console.log("Searching for user settings in:", userDataPath);
      const possiblePaths = [
        // Chrome/Electron的localStorage位置
        path.join(userDataPath, "Local Storage", "leveldb"),
        // 备用位置
        path.join(userDataPath, "IndexedDB"),
        path.join(userDataPath, "Session Storage")
      ];
      for (const storagePath of possiblePaths) {
        if (fs.existsSync(storagePath)) {
          console.log("Found storage directory:", storagePath);
          try {
            const files = fs.readdirSync(storagePath);
            for (const file of files) {
              const filePath = path.join(storagePath, file);
              try {
                const content = fs.readFileSync(filePath, "utf8");
                if (content.includes("user-settings") && content.includes("workspaceDirectory")) {
                  console.log("Found user-settings data in file:", file);
                  const patterns = [
                    /"workspaceDirectory":"([^"]+)"/,
                    /'workspaceDirectory':'([^']+)'/,
                    /workspaceDirectory['":\s]+([^'",\s}]+)/
                  ];
                  for (const pattern of patterns) {
                    const match = content.match(pattern);
                    if (match && match[1]) {
                      const workspaceDir = match[1].replace(/\\\\/g, "\\");
                      console.log("Extracted workspaceDirectory:", workspaceDir);
                      return workspaceDir;
                    }
                  }
                }
              } catch (e) {
                continue;
              }
            }
          } catch (error) {
            console.warn("Failed to read storage directory:", storagePath, error);
          }
        }
      }
      console.log("No user settings found in localStorage");
      return null;
    } catch (error) {
      console.warn("Failed to load user settings from storage:", error);
      return null;
    }
  }
  /**
   * 获取用户设置的工作目录
   */
  getUserWorkspaceDirectory() {
    if (this.customWorkspaceDirectory) {
      console.log("Using custom workspace directory for database:", this.customWorkspaceDirectory);
      return this.customWorkspaceDirectory;
    }
    const savedWorkspaceDir = this.loadUserSettingsFromStorage();
    if (savedWorkspaceDir) {
      console.log("Using saved workspace directory for database:", savedWorkspaceDir);
      return savedWorkspaceDir;
    }
    const defaultPath = electron.app.getPath("userData");
    console.log("Using default userData directory for database:", defaultPath);
    return defaultPath;
  }
  /**
   * Initialize database service with user data directory
   */
  async initialize() {
    try {
      await getKyselyDatabaseInitializer().initialize();
      this.db = getKyselyClient();
      const isDevelopment = process.env.NODE_ENV === "development" || !electron.app.isPackaged;
      let databasePath;
      let userDataPath;
      userDataPath = this.getUserWorkspaceDirectory();
      if (isDevelopment) {
        databasePath = path.join(userDataPath, "PaoLife", "dev.db");
        console.log("Development mode: using user workspace directory for dev.db");
      } else {
        databasePath = path.join(userDataPath, "PaoLife", "paolife.db");
        console.log("Production mode: using user workspace directory for paolife.db");
      }
      this.config = {
        userDataPath,
        databasePath,
        isDevelopment
      };
      this.extensions = new KyselyDatabaseExtensions(this.db);
      this.extensions2 = new KyselyDatabaseExtensions2(this.db);
      this.extensions3 = new KyselyDatabaseExtensions3(this.db);
      this.extensions4 = new KyselyDatabaseExtensions4(this.db);
      this.dataAggregator = new ReviewDataAggregator(this);
      this.reviewAnalyzer = new ReviewAnalyzer(this);
      console.log("⚠️ DocumentLinkService still uses Prisma, skipping initialization");
      console.log("✅ Kysely Database service initialized successfully");
      console.log(`Database path: ${databasePath}`);
      return {
        success: true,
        data: this.config
      };
    } catch (error) {
      console.error("❌ Failed to initialize Kysely database service:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to initialize database"
      };
    }
  }
  /**
   * Close database connection
   */
  async close() {
    await closeKyselyClient();
    this.db = null;
    console.log("Database connection closed");
  }
  /**
   * Test database connection
   */
  async testConnection() {
    try {
      if (!this.db) {
        throw new Error("Database not initialized");
      }
      await this.db.selectFrom("User").select("id").limit(1).execute();
      return {
        success: true,
        data: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Connection test failed"
      };
    }
  }
  /**
   * Get database schema information (for debugging)
   */
  async getDatabaseInfo() {
    try {
      if (!this.db) {
        throw new Error("Database not initialized");
      }
      const status = await getKyselyDatabaseInitializer().getStatus();
      return {
        success: true,
        data: {
          type: "SQLite with Kysely",
          path: this.config?.databasePath,
          initialized: status.initialized,
          tableCount: status.tableCount,
          recordCounts: status.recordCounts,
          integrity: status.integrity
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get database info"
      };
    }
  }
  // Helper method to ensure database is initialized
  ensureInitialized() {
    if (!this.db) {
      throw new Error("Database not initialized");
    }
    return this.db;
  }
  // Helper method to handle database errors
  handleError(error, operation) {
    console.error(`Database operation failed: ${operation}`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : `Failed to ${operation}`
    };
  }
  /**
   * PROJECT OPERATIONS
   */
  async createProject(data) {
    try {
      const db2 = this.ensureInitialized();
      const project = await db2.insertInto("Project").values({
        id: generateId(),
        name: data.name,
        description: data.description || null,
        status: data.status || "Not Started",
        progress: 0,
        goal: data.goal || null,
        deliverable: data.deliverable || null,
        startDate: data.startDate || null,
        deadline: data.deadline || null,
        areaId: data.areaId || null,
        archived: booleanToInt(false),
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp()
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: project
      };
    } catch (error) {
      return this.handleError(error, "create project");
    }
  }
  async getProjects() {
    try {
      const db2 = this.ensureInitialized();
      const projects = await db2.selectFrom("Project").selectAll().where("archived", "=", booleanToInt(false)).orderBy("updatedAt", "desc").execute();
      return {
        success: true,
        data: projects.map((project) => ({
          ...project,
          archived: intToBoolean(project.archived)
        }))
      };
    } catch (error) {
      return this.handleError(error, "get projects");
    }
  }
  async getProjectById(id, includeArchived = false) {
    try {
      const db2 = this.ensureInitialized();
      let query = db2.selectFrom("Project").selectAll().where("id", "=", id);
      if (!includeArchived) {
        query = query.where("archived", "=", booleanToInt(false));
      }
      const project = await query.executeTakeFirst();
      if (!project) {
        throw new Error("Project not found");
      }
      return {
        success: true,
        data: {
          ...project,
          archived: intToBoolean(project.archived)
        }
      };
    } catch (error) {
      return this.handleError(error, "get project by id");
    }
  }
  async updateProject(id, data) {
    try {
      const db2 = this.ensureInitialized();
      const updateData = {
        updatedAt: getCurrentTimestamp()
      };
      if (data.name !== void 0) updateData.name = data.name;
      if (data.description !== void 0) updateData.description = data.description;
      if (data.status !== void 0) updateData.status = data.status;
      if (data.progress !== void 0) updateData.progress = data.progress;
      if (data.goal !== void 0) updateData.goal = data.goal;
      if (data.deliverable !== void 0) updateData.deliverable = data.deliverable;
      if (data.startDate !== void 0) {
        updateData.startDate = data.startDate instanceof Date ? data.startDate.toISOString() : data.startDate;
      }
      if (data.deadline !== void 0) {
        updateData.deadline = data.deadline instanceof Date ? data.deadline.toISOString() : data.deadline;
      }
      if (data.areaId !== void 0) updateData.areaId = data.areaId;
      const project = await db2.updateTable("Project").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...project,
          archived: intToBoolean(project.archived)
        }
      };
    } catch (error) {
      return this.handleError(error, "update project");
    }
  }
  async deleteProject(id) {
    try {
      const db2 = this.ensureInitialized();
      await db2.deleteFrom("Project").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return this.handleError(error, "delete project");
    }
  }
  async getArchivedProjects() {
    try {
      const db2 = this.ensureInitialized();
      const projects = await db2.selectFrom("Project").selectAll().where("archived", "=", booleanToInt(true)).orderBy("updatedAt", "desc").execute();
      return {
        success: true,
        data: projects.map((project) => ({
          ...project,
          archived: intToBoolean(project.archived)
        }))
      };
    } catch (error) {
      return this.handleError(error, "get archived projects");
    }
  }
  async archiveProject(id) {
    try {
      const db2 = this.ensureInitialized();
      await db2.updateTable("Project").set({
        archived: booleanToInt(true),
        updatedAt: getCurrentTimestamp()
      }).where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return this.handleError(error, "archive project");
    }
  }
  async restoreProject(id) {
    try {
      const db2 = this.ensureInitialized();
      await db2.updateTable("Project").set({
        archived: booleanToInt(false),
        updatedAt: getCurrentTimestamp()
      }).where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return this.handleError(error, "restore project");
    }
  }
  /**
   * AREA OPERATIONS
   */
  async createArea(data) {
    try {
      const db2 = this.ensureInitialized();
      const area = await db2.insertInto("Area").values({
        id: generateId(),
        name: data.name,
        description: data.description || null,
        standard: data.standard || null,
        icon: data.icon || null,
        color: data.color || null,
        archived: booleanToInt(false),
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp()
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...area,
          archived: intToBoolean(area.archived)
        }
      };
    } catch (error) {
      return this.handleError(error, "create area");
    }
  }
  async getAreas() {
    try {
      const db2 = this.ensureInitialized();
      const areas = await db2.selectFrom("Area").selectAll().where("archived", "=", booleanToInt(false)).orderBy("updatedAt", "desc").execute();
      return {
        success: true,
        data: areas.map((area) => ({
          ...area,
          archived: intToBoolean(area.archived)
        }))
      };
    } catch (error) {
      return this.handleError(error, "get areas");
    }
  }
  async getAreaById(id, includeArchived = false) {
    try {
      const db2 = this.ensureInitialized();
      let query = db2.selectFrom("Area").selectAll().where("id", "=", id);
      if (!includeArchived) {
        query = query.where("archived", "=", booleanToInt(false));
      }
      const area = await query.executeTakeFirst();
      if (!area) {
        throw new Error("Area not found");
      }
      const [projects, habits, metrics] = await Promise.all([
        db2.selectFrom("Project").selectAll().where("areaId", "=", id).where("archived", "=", booleanToInt(false)).execute(),
        db2.selectFrom("Habit").selectAll().where("areaId", "=", id).execute(),
        db2.selectFrom("AreaMetric").selectAll().where("areaId", "=", id).execute()
      ]);
      return {
        success: true,
        data: {
          ...area,
          archived: intToBoolean(area.archived),
          projects: projects.map((p) => ({ ...p, archived: intToBoolean(p.archived) })),
          habits,
          metrics
        }
      };
    } catch (error) {
      return this.handleError(error, "get area by id");
    }
  }
  async updateArea(id, data) {
    try {
      const db2 = this.ensureInitialized();
      const updateData = {
        updatedAt: getCurrentTimestamp()
      };
      if (data.name !== void 0) updateData.name = data.name;
      if (data.description !== void 0) updateData.description = data.description;
      if (data.standard !== void 0) updateData.standard = data.standard;
      if (data.icon !== void 0) updateData.icon = data.icon;
      if (data.color !== void 0) updateData.color = data.color;
      const area = await db2.updateTable("Area").set(updateData).where("id", "=", id).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: {
          ...area,
          archived: intToBoolean(area.archived)
        }
      };
    } catch (error) {
      return this.handleError(error, "update area");
    }
  }
  async deleteArea(id) {
    try {
      const db2 = this.ensureInitialized();
      await db2.deleteFrom("Area").where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return this.handleError(error, "delete area");
    }
  }
  async getArchivedAreas() {
    try {
      const db2 = this.ensureInitialized();
      const areas = await db2.selectFrom("Area").selectAll().where("archived", "=", booleanToInt(true)).orderBy("updatedAt", "desc").execute();
      return {
        success: true,
        data: areas.map((area) => ({
          ...area,
          archived: intToBoolean(area.archived)
        }))
      };
    } catch (error) {
      return this.handleError(error, "get archived areas");
    }
  }
  async archiveArea(id) {
    try {
      const db2 = this.ensureInitialized();
      await db2.updateTable("Area").set({
        archived: booleanToInt(true),
        updatedAt: getCurrentTimestamp()
      }).where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return this.handleError(error, "archive area");
    }
  }
  async restoreArea(id) {
    try {
      const db2 = this.ensureInitialized();
      await db2.updateTable("Area").set({
        archived: booleanToInt(false),
        updatedAt: getCurrentTimestamp()
      }).where("id", "=", id).execute();
      return {
        success: true,
        data: void 0
      };
    } catch (error) {
      return this.handleError(error, "restore area");
    }
  }
  /**
   * PROJECT KPI OPERATIONS (delegated to extensions)
   */
  async createProjectKPI(data) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.createProjectKPI(data);
  }
  async getProjectKPIs(projectId) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.getProjectKPIs(projectId);
  }
  async updateProjectKPI(id, data) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.updateProjectKPI(id, data);
  }
  async deleteProjectKPI(id) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.deleteProjectKPI(id);
  }
  /**
   * KPI RECORD OPERATIONS (delegated to extensions)
   */
  async createKPIRecord(data) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.createKPIRecord(data);
  }
  async getKPIRecords(kpiId, limit) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.getKPIRecords(kpiId, limit);
  }
  async updateKPIRecord(id, data) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.updateKPIRecord(id, data);
  }
  async deleteKPIRecord(id) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.deleteKPIRecord(id);
  }
  /**
   * AREA METRIC OPERATIONS (delegated to extensions)
   */
  async createAreaMetric(data) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.createAreaMetric(data);
  }
  async getAreaMetrics(areaId) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.getAreaMetrics(areaId);
  }
  async updateAreaMetric(id, data) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.updateAreaMetric(id, data);
  }
  async deleteAreaMetric(id) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.deleteAreaMetric(id);
  }
  /**
   * AREA METRIC RECORD OPERATIONS (delegated to extensions)
   */
  async createAreaMetricRecord(data) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.createAreaMetricRecord(data);
  }
  async getAreaMetricRecords(metricId, limit) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.getAreaMetricRecords(metricId, limit);
  }
  async updateAreaMetricRecord(id, data) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.updateAreaMetricRecord(id, data);
  }
  async deleteAreaMetricRecord(id) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.deleteAreaMetricRecord(id);
  }
  /**
   * HABIT OPERATIONS (delegated to extensions)
   */
  async createHabit(data) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.createHabit(data);
  }
  async getHabitsByArea(areaId) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.getHabitsByArea(areaId);
  }
  async updateHabit(id, data) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.updateHabit(id, data);
  }
  async deleteHabit(id) {
    if (!this.extensions) throw new Error("Extensions not initialized");
    return this.extensions.deleteHabit(id);
  }
  /**
   * HABIT RECORD OPERATIONS (delegated to extensions2)
   */
  async createHabitRecord(data) {
    if (!this.extensions2) throw new Error("Extensions2 not initialized");
    return this.extensions2.createHabitRecord(data);
  }
  async updateHabitRecord(id, data) {
    if (!this.extensions2) throw new Error("Extensions2 not initialized");
    return this.extensions2.updateHabitRecord(id, data);
  }
  async getHabitRecords(habitId) {
    if (!this.extensions2) throw new Error("Extensions2 not initialized");
    return this.extensions2.getHabitRecords(habitId);
  }
  async deleteHabitRecord(id) {
    if (!this.extensions2) throw new Error("Extensions2 not initialized");
    return this.extensions2.deleteHabitRecord(id);
  }
  /**
   * RECURRING TASK OPERATIONS (delegated to extensions2)
   */
  async createRecurringTask(data) {
    if (!this.extensions2) throw new Error("Extensions2 not initialized");
    return this.extensions2.createRecurringTask(data);
  }
  async getRecurringTasks(areaId) {
    if (!this.extensions2) throw new Error("Extensions2 not initialized");
    return this.extensions2.getRecurringTasks(areaId);
  }
  async updateRecurringTask(id, data) {
    if (!this.extensions2) throw new Error("Extensions2 not initialized");
    return this.extensions2.updateRecurringTask(id, data);
  }
  async deleteRecurringTask(id) {
    if (!this.extensions2) throw new Error("Extensions2 not initialized");
    return this.extensions2.deleteRecurringTask(id);
  }
  /**
   * DELIVERABLE OPERATIONS (delegated to extensions2)
   */
  async createDeliverable(data) {
    if (!this.extensions2) throw new Error("Extensions2 not initialized");
    return this.extensions2.createDeliverable(data);
  }
  async getProjectDeliverables(projectId) {
    if (!this.extensions2) throw new Error("Extensions2 not initialized");
    return this.extensions2.getProjectDeliverables(projectId);
  }
  async updateDeliverable(id, data) {
    if (!this.extensions2) throw new Error("Extensions2 not initialized");
    return this.extensions2.updateDeliverable(id, data);
  }
  async deleteDeliverable(id) {
    if (!this.extensions2) throw new Error("Extensions2 not initialized");
    return this.extensions2.deleteDeliverable(id);
  }
  // TODO: Add remaining operations: Task, Review, Checklist, ResourceLink, etc.
  /**
   * COMPATIBILITY METHODS FOR GRADUAL MIGRATION
   * These methods provide compatibility with the existing IPC layer
   */
  // Getter for backward compatibility
  get db() {
    return this.ensureInitialized();
  }
  get config() {
    return this.config;
  }
  // Prisma-style client getter for compatibility
  getClient() {
    return {
      // Provide a minimal compatibility layer
      // This will be removed once all IPC methods are updated
      $disconnect: () => this.close()
    };
  }
  // Document link service getter
  getDocumentLinkService() {
    return documentLinkService;
  }
  // Review operations (delegated to extensions3)
  async createReview(data) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.createReview(data);
  }
  async getReviews(filters) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.getReviews(filters);
  }
  async getReviewById(id) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.getReviewById(id);
  }
  async updateReview(data) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.updateReview(data.id, data);
  }
  async deleteReview(id) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.deleteReview(id);
  }
  async createReviewTemplate(data) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.createReviewTemplate(data);
  }
  async getReviewTemplates(type) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.getReviewTemplates(type);
  }
  async getReviewTemplateById(id) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.getReviewTemplateById(id);
  }
  async updateReviewTemplate(data) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.updateReviewTemplate(data.id, data);
  }
  async deleteReviewTemplate(id) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.deleteReviewTemplate(id);
  }
  async getReviewAggregatedData(type, period) {
    return { success: false, error: "Not yet implemented in Kysely version" };
  }
  async getReviewAnalysis(type, period) {
    return { success: false, error: "Not yet implemented in Kysely version" };
  }
  // Resource operations (delegated to extensions3)
  async getProjectResources(projectId) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.getProjectResources(projectId);
  }
  async linkResourceToProject(data) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.linkResourceToProject(data);
  }
  async unlinkResourceFromProject(resourceId, projectId) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.unlinkResourceFromProject(resourceId, projectId);
  }
  async getAreaResources(areaId) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.getAreaResources(areaId);
  }
  async createResource(data) {
    try {
      const db2 = this.ensureInitialized();
      const existingLink = await db2.selectFrom("ResourceLink").selectAll().where("resourcePath", "=", data.resourcePath).where((eb) => {
        if (data.projectId) {
          return eb("projectId", "=", data.projectId);
        } else if (data.areaId) {
          return eb("areaId", "=", data.areaId);
        }
        return eb("projectId", "is", null).and("areaId", "is", null);
      }).executeTakeFirst();
      if (existingLink) {
        return {
          success: true,
          data: existingLink
        };
      }
      const resourceLink = await db2.insertInto("ResourceLink").values({
        id: generateId(),
        resourcePath: data.resourcePath,
        title: data.title || null,
        projectId: data.projectId || null,
        areaId: data.areaId || null
      }).returningAll().executeTakeFirstOrThrow();
      return {
        success: true,
        data: resourceLink
      };
    } catch (error) {
      return this.handleError(error, "create resource");
    }
  }
  async getResources(filters) {
    try {
      const db2 = this.ensureInitialized();
      let query = db2.selectFrom("ResourceLink").selectAll();
      if (filters?.projectId) {
        query = query.where("projectId", "=", filters.projectId);
      }
      if (filters?.areaId) {
        query = query.where("areaId", "=", filters.areaId);
      }
      const resources = await query.execute();
      return {
        success: true,
        data: resources
      };
    } catch (error) {
      return this.handleError(error, "get resources");
    }
  }
  async getResourceReferences(resourcePath) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.getResourceReferences(resourcePath);
  }
  async unlinkResourceFromArea(resourceId, areaId) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.unlinkResourceFromArea(resourceId, areaId);
  }
  // Checklist operations (delegated to extensions3)
  async createChecklist(data) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.createChecklist(data);
  }
  async getChecklists(areaId) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.getChecklists(areaId);
  }
  async updateChecklist(id, data) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.updateChecklist(id, data);
  }
  async deleteChecklist(id) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.deleteChecklist(id);
  }
  async createChecklistInstance(data) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.createChecklistInstance(data);
  }
  async getChecklistInstances(areaId) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.getChecklistInstances(areaId);
  }
  async updateChecklistInstance(id, data) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.updateChecklistInstance(id, data);
  }
  async deleteChecklistInstance(id) {
    if (!this.extensions3) throw new Error("Extensions3 not initialized");
    return this.extensions3.deleteChecklistInstance(id);
  }
  /**
   * TASK OPERATIONS (delegated to extensions4)
   */
  async createTask(data) {
    if (!this.extensions4) throw new Error("Extensions4 not initialized");
    return this.extensions4.createTask(data);
  }
  async getTasks(filters = {}) {
    if (!this.extensions4) throw new Error("Extensions4 not initialized");
    return this.extensions4.getTasks(filters);
  }
  async getTaskById(id) {
    if (!this.extensions4) throw new Error("Extensions4 not initialized");
    return this.extensions4.getTaskById(id);
  }
  async updateTask(id, data) {
    if (!this.extensions4) throw new Error("Extensions4 not initialized");
    return this.extensions4.updateTask(id, data);
  }
  async deleteTask(id) {
    if (!this.extensions4) throw new Error("Extensions4 not initialized");
    return this.extensions4.deleteTask(id);
  }
  /**
   * TASK TAG OPERATIONS (delegated to extensions4)
   */
  async createTaskTag(data) {
    if (!this.extensions4) throw new Error("Extensions4 not initialized");
    return this.extensions4.createTaskTag(data);
  }
  async getTaskTags() {
    if (!this.extensions4) throw new Error("Extensions4 not initialized");
    return this.extensions4.getTaskTags();
  }
  async updateTaskTag(id, data) {
    if (!this.extensions4) throw new Error("Extensions4 not initialized");
    return this.extensions4.updateTaskTag(id, data);
  }
  async deleteTaskTag(id) {
    if (!this.extensions4) throw new Error("Extensions4 not initialized");
    return this.extensions4.deleteTaskTag(id);
  }
  /**
   * USER SETTINGS OPERATIONS (delegated to extensions4)
   */
  async getUserSettings() {
    if (!this.extensions4) throw new Error("Extensions4 not initialized");
    return this.extensions4.getUserSettings();
  }
  async updateUserSettings(data) {
    if (!this.extensions4) throw new Error("Extensions4 not initialized");
    return this.extensions4.updateUserSettings(data);
  }
}
const kyselyDatabaseService = new KyselyDatabaseService();
class FileSystemError extends Error {
  constructor(message, code, path2, operation) {
    super(message);
    this.code = code;
    this.path = path2;
    this.operation = operation;
    this.name = "FileSystemError";
  }
}
const FILE_SYSTEM_ERRORS = {
  FILE_NOT_FOUND: "FILE_NOT_FOUND",
  PERMISSION_DENIED: "PERMISSION_DENIED",
  FILE_LOCKED: "FILE_LOCKED",
  INVALID_PATH: "INVALID_PATH",
  FILE_TOO_LARGE: "FILE_TOO_LARGE",
  UNSUPPORTED_FORMAT: "UNSUPPORTED_FORMAT",
  DISK_FULL: "DISK_FULL",
  NETWORK_ERROR: "NETWORK_ERROR"
};
class FileSystemService {
  config = null;
  locks = /* @__PURE__ */ new Map();
  cache = /* @__PURE__ */ new Map();
  maxCacheSize = 100;
  lockTimeout = 3e4;
  // 30 seconds
  /**
   * Initialize file system service
   */
  async initialize(customWorkspaceDir) {
    try {
      let resourcesPath;
      let userDataPath;
      if (customWorkspaceDir) {
        userDataPath = customWorkspaceDir;
        resourcesPath = path.join(customWorkspaceDir, "PaoLife");
      } else {
        userDataPath = electron.app.getPath("userData");
        resourcesPath = path.join(userDataPath, "resources");
      }
      this.config = {
        resourcesPath,
        userDataPath,
        watchPaths: [resourcesPath],
        allowedExtensions: [".md", ".txt", ".json"],
        maxFileSize: 10 * 1024 * 1024
        // 10MB
      };
      await this.ensureDirectoryExists(resourcesPath);
      console.log(`File system initialized at: ${resourcesPath}`);
      return {
        success: true,
        data: this.config
      };
    } catch (error) {
      console.error("Failed to initialize file system:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }
  /**
   * Get file system configuration
   */
  getConfig() {
    if (!this.config) {
      throw new FileSystemError("File system not initialized", FILE_SYSTEM_ERRORS.PERMISSION_DENIED);
    }
    return this.config;
  }
  /**
   * Ensure directory exists
   */
  async ensureDirectoryExists(dirPath) {
    try {
      await fs$1.access(dirPath);
    } catch {
      await fs$1.mkdir(dirPath, { recursive: true });
    }
  }
  /**
   * Validate file path
   */
  validatePath(filePath) {
    if (!this.config) {
      throw new FileSystemError("File system not initialized", FILE_SYSTEM_ERRORS.PERMISSION_DENIED);
    }
    const resolvedPath = path.resolve(filePath);
    const resourcesPath = path.resolve(this.config.resourcesPath);
    if (!resolvedPath.startsWith(resourcesPath)) {
      throw new FileSystemError(
        "Path outside allowed directory",
        FILE_SYSTEM_ERRORS.PERMISSION_DENIED,
        filePath
      );
    }
    const ext = path.extname(filePath).toLowerCase();
    if (ext && !this.config.allowedExtensions.includes(ext)) {
      throw new FileSystemError(
        `Unsupported file extension: ${ext}`,
        FILE_SYSTEM_ERRORS.UNSUPPORTED_FORMAT,
        filePath
      );
    }
  }
  /**
   * Acquire file lock
   */
  async acquireLock(filePath) {
    const existingLock = this.locks.get(filePath);
    if (existingLock) {
      const now = Date.now();
      if (now - existingLock.timestamp.getTime() < this.lockTimeout) {
        throw new FileSystemError(
          "File is locked by another process",
          FILE_SYSTEM_ERRORS.FILE_LOCKED,
          filePath
        );
      } else {
        this.locks.delete(filePath);
      }
    }
    const lockId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const lock = {
      path: filePath,
      lockId,
      timestamp: /* @__PURE__ */ new Date(),
      processId: process.pid
    };
    this.locks.set(filePath, lock);
    return lockId;
  }
  /**
   * Release file lock
   */
  releaseLock(filePath, lockId) {
    const lock = this.locks.get(filePath);
    if (lock && lock.lockId === lockId) {
      this.locks.delete(filePath);
    }
  }
  /**
   * Get file from cache
   */
  getFromCache(filePath) {
    const cached = this.cache.get(filePath);
    if (cached) {
      cached.hits++;
      cached.lastAccessed = /* @__PURE__ */ new Date();
      return cached;
    }
    return null;
  }
  /**
   * Add file to cache
   */
  addToCache(filePath, content, lastModified, size) {
    if (this.cache.size >= this.maxCacheSize) {
      const oldestKey = Array.from(this.cache.entries()).sort(
        ([, a], [, b]) => a.lastAccessed.getTime() - b.lastAccessed.getTime()
      )[0][0];
      this.cache.delete(oldestKey);
    }
    const cacheEntry = {
      path: filePath,
      content,
      lastModified,
      size,
      hits: 1,
      lastAccessed: /* @__PURE__ */ new Date()
    };
    this.cache.set(filePath, cacheEntry);
  }
  /**
   * Get file information
   */
  async getFileInfo(filePath) {
    try {
      this.validatePath(filePath);
      const stats = await fs$1.stat(filePath);
      const fileInfo = {
        path: filePath,
        name: path.basename(filePath),
        size: stats.size,
        isDirectory: stats.isDirectory(),
        isFile: stats.isFile(),
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        extension: path.extname(filePath)
      };
      return {
        success: true,
        data: fileInfo
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get file info"
      };
    }
  }
  /**
   * Check if file exists
   */
  async fileExists(filePath) {
    try {
      this.validatePath(filePath);
      await fs$1.access(filePath);
      return {
        success: true,
        data: true
      };
    } catch {
      return {
        success: true,
        data: false
      };
    }
  }
  /**
   * Read file content
   */
  async readFile(filePath, options = {}) {
    let lockId = null;
    try {
      this.validatePath(filePath);
      const stats = await fs$1.stat(filePath);
      const cached = this.getFromCache(filePath);
      if (cached && cached.lastModified.getTime() === stats.mtime.getTime()) {
        return {
          success: true,
          data: {
            path: filePath,
            content: cached.content,
            encoding: options.encoding || "utf8",
            lastModified: cached.lastModified
          }
        };
      }
      lockId = await this.acquireLock(filePath);
      const encoding = options.encoding || "utf8";
      const content = await fs$1.readFile(filePath, encoding);
      this.addToCache(filePath, content, stats.mtime, stats.size);
      const fileContent = {
        path: filePath,
        content,
        encoding,
        lastModified: stats.mtime
      };
      return {
        success: true,
        data: fileContent
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to read file"
      };
    } finally {
      if (lockId) {
        this.releaseLock(filePath, lockId);
      }
    }
  }
  /**
   * Write file content
   */
  async writeFile(filePath, content, options = {}) {
    let lockId = null;
    try {
      this.validatePath(filePath);
      let writeContent;
      let contentSize;
      if (Array.isArray(content)) {
        writeContent = Buffer.from(content);
        contentSize = writeContent.length;
      } else {
        writeContent = content;
        contentSize = Buffer.byteLength(content, options.encoding || "utf8");
      }
      if (contentSize > this.config.maxFileSize) {
        throw new FileSystemError(
          `File too large: ${contentSize} bytes`,
          FILE_SYSTEM_ERRORS.FILE_TOO_LARGE,
          filePath
        );
      }
      if (options.createDirs) {
        await this.ensureDirectoryExists(path.dirname(filePath));
      }
      if (options.backup && await this.fileExists(filePath)) {
        const backupPath = `${filePath}.backup.${Date.now()}`;
        await fs$1.copyFile(filePath, backupPath);
      }
      lockId = await this.acquireLock(filePath);
      if (Buffer.isBuffer(writeContent)) {
        await fs$1.writeFile(filePath, writeContent, {
          flag: options.flag || "w",
          mode: options.mode
        });
      } else {
        const encoding = options.encoding || "utf8";
        await fs$1.writeFile(filePath, writeContent, {
          encoding,
          flag: options.flag || "w",
          mode: options.mode
        });
      }
      const stats = await fs$1.stat(filePath);
      if (typeof content === "string") {
        this.addToCache(filePath, content, stats.mtime, stats.size);
      }
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to write file"
      };
    } finally {
      if (lockId) {
        this.releaseLock(filePath, lockId);
      }
    }
  }
  /**
   * Delete file
   */
  async deleteFile(filePath) {
    let lockId = null;
    try {
      this.validatePath(filePath);
      lockId = await this.acquireLock(filePath);
      await fs$1.unlink(filePath);
      this.cache.delete(filePath);
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete file"
      };
    } finally {
      if (lockId) {
        this.releaseLock(filePath, lockId);
      }
    }
  }
  /**
   * Move/rename file
   */
  async moveFile(sourcePath, targetPath, options = {}) {
    let sourceLockId = null;
    let targetLockId = null;
    try {
      this.validatePath(sourcePath);
      this.validatePath(targetPath);
      if (!options.overwrite && await this.fileExists(targetPath)) {
        throw new FileSystemError(
          "Target file already exists",
          FILE_SYSTEM_ERRORS.PERMISSION_DENIED,
          targetPath
        );
      }
      if (options.createDirs) {
        await this.ensureDirectoryExists(path.dirname(targetPath));
      }
      sourceLockId = await this.acquireLock(sourcePath);
      targetLockId = await this.acquireLock(targetPath);
      await fs$1.rename(sourcePath, targetPath);
      const cached = this.cache.get(sourcePath);
      if (cached) {
        this.cache.delete(sourcePath);
        this.cache.set(targetPath, { ...cached, path: targetPath });
      }
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to move file"
      };
    } finally {
      if (sourceLockId) {
        this.releaseLock(sourcePath, sourceLockId);
      }
      if (targetLockId) {
        this.releaseLock(targetPath, targetLockId);
      }
    }
  }
  /**
   * Copy file
   */
  async copyFile(sourcePath, targetPath, options = {}) {
    let targetLockId = null;
    try {
      this.validatePath(sourcePath);
      this.validatePath(targetPath);
      if (!options.overwrite && await this.fileExists(targetPath)) {
        throw new FileSystemError(
          "Target file already exists",
          FILE_SYSTEM_ERRORS.PERMISSION_DENIED,
          targetPath
        );
      }
      if (options.createDirs) {
        await this.ensureDirectoryExists(path.dirname(targetPath));
      }
      targetLockId = await this.acquireLock(targetPath);
      await fs$1.copyFile(sourcePath, targetPath);
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to copy file"
      };
    } finally {
      if (targetLockId) {
        this.releaseLock(targetPath, targetLockId);
      }
    }
  }
  /**
   * List directory contents
   */
  async listDirectory(dirPath) {
    try {
      this.validatePath(dirPath);
      const entries = await fs$1.readdir(dirPath, { withFileTypes: true });
      const fileInfos = [];
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        const stats = await fs$1.stat(fullPath);
        fileInfos.push({
          path: fullPath,
          name: entry.name,
          size: stats.size,
          isDirectory: entry.isDirectory(),
          isFile: entry.isFile(),
          createdAt: stats.birthtime,
          modifiedAt: stats.mtime,
          extension: entry.isFile() ? path.extname(entry.name) : void 0
        });
      }
      return {
        success: true,
        data: fileInfos
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to list directory"
      };
    }
  }
  /**
   * Create directory
   */
  async createDirectory(dirPath) {
    try {
      this.validatePath(dirPath);
      await fs$1.mkdir(dirPath, { recursive: true });
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create directory"
      };
    }
  }
  /**
   * Delete directory and all its contents
   */
  async deleteDirectory(dirPath) {
    try {
      this.validatePath(dirPath);
      const exists = await fs$1.access(dirPath).then(() => true).catch(() => false);
      if (!exists) {
        return {
          success: false,
          error: "Directory does not exist"
        };
      }
      const stats = await fs$1.stat(dirPath);
      if (!stats.isDirectory()) {
        return {
          success: false,
          error: "Path is not a directory"
        };
      }
      await fs$1.rm(dirPath, { recursive: true, force: true });
      for (const [cachedPath] of this.cache) {
        if (cachedPath.startsWith(dirPath)) {
          this.cache.delete(cachedPath);
        }
      }
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete directory"
      };
    }
  }
  /**
   * Rename file or directory
   */
  async rename(oldPath, newPath) {
    try {
      this.validatePath(oldPath);
      this.validatePath(newPath);
      const exists = await fs$1.access(oldPath).then(() => true).catch(() => false);
      if (!exists) {
        return {
          success: false,
          error: "Source file or directory does not exist"
        };
      }
      const newExists = await fs$1.access(newPath).then(() => true).catch(() => false);
      if (newExists) {
        return {
          success: false,
          error: "Target file or directory already exists"
        };
      }
      await fs$1.rename(oldPath, newPath);
      this.cache.delete(oldPath);
      this.cache.delete(newPath);
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to rename file or directory"
      };
    }
  }
  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
  }
  /**
   * Get cache statistics
   */
  getCacheStats() {
    const entries = Array.from(this.cache.values()).map((cache) => ({
      path: cache.path,
      hits: cache.hits,
      size: cache.size
    }));
    const totalHits = entries.reduce((sum, entry) => sum + entry.hits, 0);
    return {
      size: this.cache.size,
      totalHits,
      entries
    };
  }
  /**
   * Clean up expired locks
   */
  cleanupLocks() {
    const now = Date.now();
    for (const [path2, lock] of this.locks.entries()) {
      if (now - lock.timestamp.getTime() > this.lockTimeout) {
        this.locks.delete(path2);
      }
    }
  }
}
const fileSystemService = new FileSystemService();
class FileWatcherService extends events.EventEmitter {
  watchers = /* @__PURE__ */ new Map();
  isInitialized = false;
  /**
   * Initialize file watcher service
   */
  async initialize() {
    try {
      if (this.isInitialized) {
        return { success: true };
      }
      const config = fileSystemService.getConfig();
      await this.watchDirectory(config.resourcesPath, {
        ignored: /(^|[\/\\])\../,
        // ignore dotfiles
        persistent: true,
        ignoreInitial: false,
        followSymlinks: false,
        cwd: config.resourcesPath,
        disableGlobbing: false,
        usePolling: false,
        interval: 100,
        binaryInterval: 300,
        alwaysStat: false,
        depth: 99,
        awaitWriteFinish: {
          stabilityThreshold: 2e3,
          pollInterval: 100
        }
      });
      this.isInitialized = true;
      console.log("File watcher service initialized");
      return { success: true };
    } catch (error) {
      console.error("Failed to initialize file watcher:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }
  /**
   * Watch a directory for changes
   */
  async watchDirectory(dirPath, options = {}) {
    try {
      if (this.watchers.has(dirPath)) {
        return { success: true };
      }
      const watcher = chokidar.watch(dirPath, {
        ignored: /(^|[\/\\])\../,
        // ignore dotfiles
        persistent: true,
        ignoreInitial: true,
        followSymlinks: false,
        usePolling: false,
        interval: 100,
        binaryInterval: 300,
        alwaysStat: true,
        depth: 99,
        awaitWriteFinish: {
          stabilityThreshold: 2e3,
          pollInterval: 100
        },
        ...options
      });
      watcher.on("add", (filePath, stats) => {
        this.handleFileEvent("add", filePath, stats);
      }).on("change", (filePath, stats) => {
        this.handleFileEvent("change", filePath, stats);
      }).on("unlink", (filePath) => {
        this.handleFileEvent("unlink", filePath);
      }).on("addDir", (dirPath2, stats) => {
        this.handleFileEvent("addDir", dirPath2, stats);
      }).on("unlinkDir", (dirPath2) => {
        this.handleFileEvent("unlinkDir", dirPath2);
      }).on("error", (error) => {
        console.error("File watcher error:", error);
        this.emit("error", error);
      }).on("ready", () => {
        console.log(`File watcher ready for: ${dirPath}`);
        this.emit("ready", dirPath);
      });
      this.watchers.set(dirPath, watcher);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to watch directory"
      };
    }
  }
  /**
   * Stop watching a directory
   */
  async unwatchDirectory(dirPath) {
    try {
      const watcher = this.watchers.get(dirPath);
      if (watcher) {
        await watcher.close();
        this.watchers.delete(dirPath);
        console.log(`Stopped watching: ${dirPath}`);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to unwatch directory"
      };
    }
  }
  /**
   * Handle file system events
   */
  handleFileEvent(eventType, filePath, stats) {
    try {
      const fileInfo = stats ? {
        path: filePath,
        name: path.basename(filePath),
        size: stats.size || 0,
        isDirectory: stats.isDirectory() || false,
        isFile: stats.isFile() || false,
        createdAt: stats.birthtime || /* @__PURE__ */ new Date(),
        modifiedAt: stats.mtime || /* @__PURE__ */ new Date(),
        extension: stats.isFile() ? path.extname(filePath) : void 0
      } : void 0;
      const watchEvent = {
        type: eventType,
        path: filePath,
        stats: fileInfo,
        timestamp: /* @__PURE__ */ new Date()
      };
      this.emit("watch-event", watchEvent);
      this.processFileSystemEvent(watchEvent);
    } catch (error) {
      console.error("Error handling file event:", error);
    }
  }
  /**
   * Process file system event and emit appropriate events
   */
  processFileSystemEvent(watchEvent) {
    try {
      let eventType;
      switch (watchEvent.type) {
        case "add":
          eventType = "file-created";
          break;
        case "change":
          eventType = "file-changed";
          break;
        case "unlink":
          eventType = "file-deleted";
          break;
        case "addDir":
        case "unlinkDir":
          eventType = "directory-changed";
          break;
        default:
          return;
      }
      const fileSystemEvent = {
        type: eventType,
        path: watchEvent.path,
        timestamp: watchEvent.timestamp
      };
      if (watchEvent.stats?.isFile && path.extname(watchEvent.path) === ".md") {
        const resourceId = this.extractResourceId(watchEvent.path);
        if (resourceId) {
          fileSystemEvent.resourceId = resourceId;
        }
      }
      this.emit("file-system-event", fileSystemEvent);
      this.emit(eventType, fileSystemEvent);
    } catch (error) {
      console.error("Error processing file system event:", error);
    }
  }
  /**
   * Extract resource ID from file path
   */
  extractResourceId(filePath) {
    try {
      const basename = path.basename(filePath, ".md");
      const uuidPattern = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i;
      const match = basename.match(uuidPattern);
      if (match) {
        return match[0];
      }
      return basename;
    } catch {
      return null;
    }
  }
  /**
   * Get watched directories
   */
  getWatchedDirectories() {
    return Array.from(this.watchers.keys());
  }
  /**
   * Check if directory is being watched
   */
  isWatching(dirPath) {
    return this.watchers.has(dirPath);
  }
  /**
   * Get watcher statistics
   */
  getWatcherStats() {
    return {
      totalWatchers: this.watchers.size,
      watchedPaths: Array.from(this.watchers.keys())
    };
  }
  /**
   * Close all watchers
   */
  async close() {
    const closePromises = Array.from(this.watchers.values()).map((watcher) => watcher.close());
    await Promise.all(closePromises);
    this.watchers.clear();
    this.isInitialized = false;
    console.log("File watcher service closed");
  }
}
const fileWatcherService = new FileWatcherService();
const IPC_CHANNELS = {
  // Database operations
  DB_INITIALIZE: "db:initialize",
  DB_CREATE_PROJECT: "db:create-project",
  DB_GET_PROJECTS: "db:get-projects",
  DB_GET_PROJECT_BY_ID: "db:get-project-by-id",
  DB_UPDATE_PROJECT: "db:update-project",
  DB_DELETE_PROJECT: "db:delete-project",
  DB_ARCHIVE_PROJECT: "db:archive-project",
  DB_CREATE_AREA: "db:create-area",
  DB_GET_AREAS: "db:get-areas",
  DB_UPDATE_AREA: "db:update-area",
  DB_DELETE_AREA: "db:delete-area",
  DB_GET_ARCHIVED_PROJECTS: "db:get-archived-projects",
  DB_GET_ARCHIVED_AREAS: "db:get-archived-areas",
  DB_RESTORE_PROJECT: "db:restore-project",
  DB_CREATE_TASK: "db:create-task",
  DB_GET_TASKS: "db:get-tasks",
  DB_UPDATE_TASK: "db:update-task",
  DB_CREATE_RESOURCE: "db:create-resource",
  DB_GET_RESOURCES: "db:get-resources",
  DB_GET_PROJECT_RESOURCES: "db:get-project-resources",
  DB_LINK_RESOURCE_TO_PROJECT: "db:link-resource-to-project",
  DB_UNLINK_RESOURCE_FROM_PROJECT: "db:unlink-resource-from-project",
  DB_GET_AREA_RESOURCES: "db:get-area-resources",
  // ProjectKPI operations
  DB_CREATE_PROJECT_KPI: "db:create-project-kpi",
  DB_GET_PROJECT_KPIS: "db:get-project-kpis",
  DB_UPDATE_PROJECT_KPI: "db:update-project-kpi",
  DB_DELETE_PROJECT_KPI: "db:delete-project-kpi",
  // KPI Record operations
  DB_CREATE_KPI_RECORD: "db:create-kpi-record",
  DB_GET_KPI_RECORDS: "db:get-kpi-records",
  DB_UPDATE_KPI_RECORD: "db:update-kpi-record",
  DB_DELETE_KPI_RECORD: "db:delete-kpi-record",
  // Area Metric operations
  DB_CREATE_AREA_METRIC: "db:create-area-metric",
  DB_GET_AREA_METRICS: "db:get-area-metrics",
  DB_UPDATE_AREA_METRIC: "db:update-area-metric",
  DB_DELETE_AREA_METRIC: "db:delete-area-metric",
  // Area Metric Record operations
  DB_CREATE_AREA_METRIC_RECORD: "db:create-area-metric-record",
  DB_GET_AREA_METRIC_RECORDS: "db:get-area-metric-records",
  DB_UPDATE_AREA_METRIC_RECORD: "db:update-area-metric-record",
  DB_DELETE_AREA_METRIC_RECORD: "db:delete-area-metric-record",
  // Habit operations
  DB_GET_HABITS_BY_AREA: "db:get-habits-by-area",
  // Deliverable operations
  DB_CREATE_DELIVERABLE: "db:create-deliverable",
  DB_GET_PROJECT_DELIVERABLES: "db:get-project-deliverables",
  DB_UPDATE_DELIVERABLE: "db:update-deliverable",
  DB_DELETE_DELIVERABLE: "db:delete-deliverable",
  // Habit operations
  DB_CREATE_HABIT: "db:create-habit",
  DB_UPDATE_HABIT: "db:update-habit",
  DB_DELETE_HABIT: "db:delete-habit",
  // Habit record operations
  DB_CREATE_HABIT_RECORD: "db:create-habit-record",
  DB_UPDATE_HABIT_RECORD: "db:update-habit-record",
  DB_GET_HABIT_RECORDS: "db:get-habit-records",
  DB_DELETE_HABIT_RECORD: "db:delete-habit-record",
  // {{ AURA-X: Add - 定期维护任务IPC通道. Approval: 寸止(ID:1738157400). }}
  // Recurring task operations
  DB_CREATE_RECURRING_TASK: "db:create-recurring-task",
  DB_GET_RECURRING_TASKS: "db:get-recurring-tasks",
  DB_UPDATE_RECURRING_TASK: "db:update-recurring-task",
  DB_DELETE_RECURRING_TASK: "db:delete-recurring-task",
  // Checklist operations
  DB_CREATE_CHECKLIST: "db:create-checklist",
  DB_GET_CHECKLISTS: "db:get-checklists",
  DB_UPDATE_CHECKLIST: "db:update-checklist",
  DB_DELETE_CHECKLIST: "db:delete-checklist",
  // Checklist Instance operations
  DB_CREATE_CHECKLIST_INSTANCE: "db:create-checklist-instance",
  DB_GET_CHECKLIST_INSTANCES: "db:get-checklist-instances",
  DB_UPDATE_CHECKLIST_INSTANCE: "db:update-checklist-instance",
  DB_DELETE_CHECKLIST_INSTANCE: "db:delete-checklist-instance",
  // File system operations
  FS_INITIALIZE: "fs:initialize",
  FS_REINITIALIZE: "fs:reinitialize",
  FS_READ_FILE: "fs:read-file",
  FS_WRITE_FILE: "fs:write-file",
  FS_DELETE_FILE: "fs:delete-file",
  FS_MOVE_FILE: "fs:move-file",
  FS_COPY_FILE: "fs:copy-file",
  FS_FILE_EXISTS: "fs:file-exists",
  FS_GET_FILE_INFO: "fs:get-file-info",
  FS_LIST_DIRECTORY: "fs:list-directory",
  FS_CREATE_DIRECTORY: "fs:create-directory",
  FS_RENAME: "fs:rename",
  FS_DELETE_DIRECTORY: "fs:delete-directory",
  FS_WATCH_DIRECTORY: "fs:watch-directory",
  FS_UNWATCH_DIRECTORY: "fs:unwatch-directory",
  // Review operations
  DB_CREATE_REVIEW: "db:create-review",
  DB_GET_REVIEWS: "db:get-reviews",
  DB_UPDATE_REVIEW: "db:update-review",
  DB_DELETE_REVIEW: "db:delete-review",
  // Review Template operations
  DB_CREATE_REVIEW_TEMPLATE: "db:create-review-template",
  DB_GET_REVIEW_TEMPLATES: "db:get-review-templates",
  DB_UPDATE_REVIEW_TEMPLATE: "db:update-review-template",
  DB_DELETE_REVIEW_TEMPLATE: "db:delete-review-template",
  // Settings operations
  SETTINGS_GET_USER_SETTINGS: "settings:get-user-settings",
  SETTINGS_UPDATE_USER_SETTINGS: "settings:update-user-settings",
  SETTINGS_GET_DATABASE_INFO: "settings:get-database-info",
  SETTINGS_SELECT_RESOURCE_PATH: "settings:select-resource-path",
  SETTINGS_UPDATE_RESOURCE_PATH: "settings:update-resource-path",
  // Application operations
  APP_GET_VERSION: "app:get-version",
  APP_GET_PATH: "app:get-path",
  APP_SHOW_MESSAGE_BOX: "app:show-message-box",
  APP_SHOW_ERROR_BOX: "app:show-error-box",
  APP_SHOW_OPEN_DIALOG: "app:show-open-dialog",
  APP_SHOW_SAVE_DIALOG: "app:show-save-dialog",
  // Window operations
  WINDOW_MINIMIZE: "window:minimize",
  WINDOW_MAXIMIZE: "window:maximize",
  WINDOW_CLOSE: "window:close",
  WINDOW_TOGGLE_DEVTOOLS: "window:toggle-devtools",
  WINDOW_IS_MAXIMIZED: "window:is-maximized",
  // Global shortcuts
  GLOBAL_SHORTCUT_REGISTER: "global-shortcut:register",
  GLOBAL_SHORTCUT_UNREGISTER: "global-shortcut:unregister",
  GLOBAL_SHORTCUT_UNREGISTER_ALL: "global-shortcut:unregister-all",
  // App control
  APP_FORCE_QUIT: "app-force-quit",
  APP_CANCEL_QUIT: "app-cancel-quit",
  // Database configuration
  DB_SET_WORKSPACE_DIRECTORY: "db:set-workspace-directory",
  DB_INITIALIZE_WITH_WORKSPACE: "db:initialize-with-workspace"
};
class IpcHandler {
  isInitialized = false;
  /**
   * Initialize IPC handlers
   */
  initialize() {
    if (this.isInitialized) {
      return;
    }
    this.setupDatabaseHandlers();
    this.setupFileSystemHandlers();
    this.setupAppHandlers();
    this.setupWindowHandlers();
    this.setupEventForwarding();
    this.isInitialized = true;
    console.log("IPC handlers initialized");
  }
  /**
   * Setup database operation handlers
   */
  setupDatabaseHandlers() {
    electron.ipcMain.handle(IPC_CHANNELS.DB_INITIALIZE, async () => {
      try {
        return await kyselyDatabaseService.initialize();
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Database initialization failed"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_STATUS, async () => {
      try {
        return await kyselyDatabaseService.getStatus();
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get database status"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECTS, async () => {
      try {
        return await kyselyDatabaseService.getProjects();
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get projects"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_BY_ID, async (_, id, includeArchived) => {
      try {
        return await kyselyDatabaseService.getProjectById(id, includeArchived);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get project"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_ARCHIVE_PROJECT, async (_, id) => {
      try {
        return await kyselyDatabaseService.archiveProject(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to archive project"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_ARCHIVED_PROJECTS, async (_) => {
      try {
        return await kyselyDatabaseService.getArchivedProjects();
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get archived projects"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_ARCHIVED_AREAS, async (_) => {
      try {
        return await kyselyDatabaseService.getArchivedAreas();
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get archived areas"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_RESTORE_PROJECT, async (_, id) => {
      try {
        return await kyselyDatabaseService.restoreProject(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to restore project"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_PROJECT, async (_, data) => {
      try {
        return await kyselyDatabaseService.createProject(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create project"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_PROJECT, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateProject(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update project"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_PROJECT, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteProject(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete project"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_AREAS, async () => {
      try {
        return await kyselyDatabaseService.getAreas();
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get areas"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_AREA, async (_, data) => {
      try {
        return await kyselyDatabaseService.createArea(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create area"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_AREA, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateArea(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update area"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_AREA, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteArea(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete area"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_TASKS, async (_, filters) => {
      try {
        return await kyselyDatabaseService.getTasks(filters);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get tasks"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_TASK, async (_, data) => {
      try {
        return await kyselyDatabaseService.createTask(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create task"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_TASK, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateTask(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update task"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_HABITS_BY_AREA, async (_, areaId) => {
      try {
        return await kyselyDatabaseService.getHabitsByArea(areaId);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get habits"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_HABIT, async (_, data) => {
      try {
        return await kyselyDatabaseService.createHabit(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create habit"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_HABIT, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateHabit(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update habit"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_HABIT, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteHabit(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete habit"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_HABIT_RECORDS, async (_, habitId) => {
      try {
        return await kyselyDatabaseService.getHabitRecords(habitId);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get habit records"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_HABIT_RECORD, async (_, data) => {
      try {
        return await kyselyDatabaseService.createHabitRecord(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create habit record"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_HABIT_RECORD, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateHabitRecord(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update habit record"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_HABIT_RECORD, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteHabitRecord(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete habit record"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_RECURRING_TASKS, async (_, areaId) => {
      try {
        return await kyselyDatabaseService.getRecurringTasks(areaId);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get recurring tasks"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_RECURRING_TASK, async (_, data) => {
      try {
        return await kyselyDatabaseService.createRecurringTask(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create recurring task"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_RECURRING_TASK, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateRecurringTask(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update recurring task"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_RECURRING_TASK, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteRecurringTask(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete recurring task"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_DELIVERABLES, async (_, projectId) => {
      try {
        return await kyselyDatabaseService.getProjectDeliverables(projectId);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get deliverables"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_DELIVERABLE, async (_, data) => {
      try {
        return await kyselyDatabaseService.createDeliverable(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create deliverable"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_DELIVERABLE, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateDeliverable(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update deliverable"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_DELIVERABLE, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteDeliverable(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete deliverable"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_REVIEWS, async (_, data) => {
      try {
        return await kyselyDatabaseService.getReviews(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get reviews"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_REVIEW, async (_, data) => {
      try {
        return await kyselyDatabaseService.createReview(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create review"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_REVIEW, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateReview(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update review"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_REVIEW, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteReview(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete review"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_REVIEW_TEMPLATES, async () => {
      try {
        return await kyselyDatabaseService.getReviewTemplates();
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get review templates"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_REVIEW_TEMPLATE, async (_, data) => {
      try {
        return await kyselyDatabaseService.createReviewTemplate(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create review template"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_REVIEW_TEMPLATE, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateReviewTemplate(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update review template"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_REVIEW_TEMPLATE, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteReviewTemplate(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete review template"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.SETTINGS_GET_USER_SETTINGS, async () => {
      try {
        return await kyselyDatabaseService.getUserSettings();
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get user settings"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.SETTINGS_UPDATE_USER_SETTINGS, async (_, settings) => {
      try {
        return await kyselyDatabaseService.updateUserSettings(settings);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update user settings"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.SETTINGS_GET_DATABASE_INFO, async () => {
      try {
        return await kyselyDatabaseService.getDatabaseInfo();
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get database info"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.SETTINGS_SELECT_RESOURCE_PATH, async () => {
      try {
        const result = await electron.dialog.showOpenDialog({
          properties: ["openDirectory"],
          title: "Select Resource Directory"
        });
        if (result.canceled || !result.filePaths.length) {
          return { success: false, canceled: true };
        }
        return { success: true, path: result.filePaths[0] };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to select resource path"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.SETTINGS_UPDATE_RESOURCE_PATH, async (_, path2) => {
      try {
        console.log("Updating resource path to:", path2);
        return { success: true, path: path2 };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update resource path"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_AREA_METRIC, async (_, data) => {
      try {
        return await kyselyDatabaseService.createAreaMetric(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create area metric"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_AREA_METRICS, async (_, areaId) => {
      try {
        return await kyselyDatabaseService.getAreaMetrics(areaId);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get area metrics"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_AREA_METRIC, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateAreaMetric(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update area metric"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_AREA_METRIC, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteAreaMetric(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete area metric"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_AREA_METRIC_RECORD, async (_, data) => {
      try {
        return await kyselyDatabaseService.createAreaMetricRecord(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create area metric record"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_AREA_METRIC_RECORDS, async (_, metricId, limit) => {
      try {
        return await kyselyDatabaseService.getAreaMetricRecords(metricId, limit);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get area metric records"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_AREA_METRIC_RECORD, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateAreaMetricRecord(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update area metric record"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_AREA_METRIC_RECORD, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteAreaMetricRecord(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete area metric record"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_AREA_RESOURCES, async (_, areaId) => {
      try {
        return await kyselyDatabaseService.getAreaResources(areaId);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get area resources"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_PROJECT_KPI, async (_, data) => {
      try {
        return await kyselyDatabaseService.createProjectKPI(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create project KPI"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_KPIS, async (_, projectId) => {
      try {
        return await kyselyDatabaseService.getProjectKPIs(projectId);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get project KPIs"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_PROJECT_KPI, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateProjectKPI(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update project KPI"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_PROJECT_KPI, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteProjectKPI(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete project KPI"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_KPI_RECORD, async (_, data) => {
      try {
        return await kyselyDatabaseService.createKPIRecord(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create KPI record"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_KPI_RECORDS, async (_, kpiId, limit) => {
      try {
        return await kyselyDatabaseService.getKPIRecords(kpiId, limit);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get KPI records"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_KPI_RECORD, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateKPIRecord(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update KPI record"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_KPI_RECORD, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteKPIRecord(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete KPI record"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_CHECKLIST, async (_, data) => {
      try {
        return await kyselyDatabaseService.createChecklist(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create checklist"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_CHECKLISTS, async (_, areaId) => {
      try {
        return await kyselyDatabaseService.getChecklists(areaId);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get checklists"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_CHECKLIST, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateChecklist(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update checklist"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_CHECKLIST, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteChecklist(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete checklist"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_CHECKLIST_INSTANCE, async (_, data) => {
      try {
        return await kyselyDatabaseService.createChecklistInstance(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create checklist instance"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_CHECKLIST_INSTANCES, async (_, areaId) => {
      try {
        return await kyselyDatabaseService.getChecklistInstances(areaId);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get checklist instances"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UPDATE_CHECKLIST_INSTANCE, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateChecklistInstance(data.id, data.updates);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to update checklist instance"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_DELETE_CHECKLIST_INSTANCE, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteChecklistInstance(id);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete checklist instance"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_CREATE_RESOURCE, async (_, data) => {
      try {
        return await kyselyDatabaseService.createResource(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create resource"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_RESOURCES, async (_, filters) => {
      try {
        return await kyselyDatabaseService.getResources(filters);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get resources"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_RESOURCES, async (_, projectId) => {
      try {
        return await kyselyDatabaseService.getProjectResources(projectId);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get project resources"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_LINK_RESOURCE_TO_PROJECT, async (_, data) => {
      try {
        return await kyselyDatabaseService.linkResourceToProject(data);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to link resource to project"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_UNLINK_RESOURCE_FROM_PROJECT, async (_, resourceId, projectId) => {
      try {
        return await kyselyDatabaseService.unlinkResourceFromProject(resourceId, projectId);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to unlink resource from project"
        };
      }
    });
  }
  /**
   * Setup file system operation handlers
   */
  setupFileSystemHandlers() {
    electron.ipcMain.handle(IPC_CHANNELS.FS_INITIALIZE, async () => {
      try {
        return await fileSystemService.initialize();
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "File system initialization failed"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_REINITIALIZE, async (_, workspaceDirectory) => {
      try {
        return await fileSystemService.initialize(workspaceDirectory);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "File system re-initialization failed"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_READ_FILE, async (_, data) => {
      try {
        return await fileSystemService.readFile(data.path, { encoding: data.encoding });
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to read file"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_WRITE_FILE, async (_, data) => {
      try {
        return await fileSystemService.writeFile(data.path, data.content, {
          encoding: data.encoding,
          createDirs: data.createDirs,
          backup: data.backup
        });
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to write file"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_DELETE_FILE, async (_, path2) => {
      try {
        return await fileSystemService.deleteFile(path2);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete file"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_MOVE_FILE, async (_, data) => {
      try {
        return await fileSystemService.moveFile(data.sourcePath, data.targetPath, {
          overwrite: data.overwrite,
          createDirs: data.createDirs
        });
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to move file"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_COPY_FILE, async (_, data) => {
      try {
        return await fileSystemService.copyFile(data.sourcePath, data.targetPath, {
          overwrite: data.overwrite,
          createDirs: data.createDirs
        });
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to copy file"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_FILE_EXISTS, async (_, path2) => {
      try {
        return await fileSystemService.fileExists(path2);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to check file existence"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_GET_FILE_INFO, async (_, path2) => {
      try {
        return await fileSystemService.getFileInfo(path2);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get file info"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_LIST_DIRECTORY, async (_, path2) => {
      try {
        return await fileSystemService.listDirectory(path2);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to list directory"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_CREATE_DIRECTORY, async (_, path2) => {
      try {
        return await fileSystemService.createDirectory(path2);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to create directory"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_DELETE_DIRECTORY, async (_, path2) => {
      try {
        return await fileSystemService.deleteDirectory(path2);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to delete directory"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_RENAME, async (_, oldPath, newPath) => {
      try {
        return await fileSystemService.rename(oldPath, newPath);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to rename file or directory"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_WATCH_DIRECTORY, async (_, path2) => {
      try {
        return await fileWatcherService.watchDirectory(path2);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to watch directory"
        };
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.FS_UNWATCH_DIRECTORY, async (_, path2) => {
      try {
        return await fileWatcherService.unwatchDirectory(path2);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to unwatch directory"
        };
      }
    });
  }
  /**
   * Setup application operation handlers
   */
  setupAppHandlers() {
    electron.ipcMain.handle(IPC_CHANNELS.APP_GET_VERSION, async () => {
      return electron.app.getVersion();
    });
    electron.ipcMain.handle(IPC_CHANNELS.APP_GET_PATH, async (_, name) => {
      return electron.app.getPath(name);
    });
    electron.ipcMain.handle(IPC_CHANNELS.APP_SHOW_MESSAGE_BOX, async (_, options) => {
      const focusedWindow = electron.BrowserWindow.getFocusedWindow();
      if (focusedWindow) {
        return await electron.dialog.showMessageBox(focusedWindow, options);
      } else {
        return await electron.dialog.showMessageBox(options);
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.APP_SHOW_ERROR_BOX, async (_, title, content) => {
      electron.dialog.showErrorBox(title, content);
    });
    electron.ipcMain.handle(IPC_CHANNELS.APP_SHOW_OPEN_DIALOG, async (_, options) => {
      const focusedWindow = electron.BrowserWindow.getFocusedWindow();
      if (focusedWindow) {
        return await electron.dialog.showOpenDialog(focusedWindow, options);
      } else {
        return await electron.dialog.showOpenDialog(options);
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.APP_SHOW_SAVE_DIALOG, async (_, options) => {
      const focusedWindow = electron.BrowserWindow.getFocusedWindow();
      if (focusedWindow) {
        return await electron.dialog.showSaveDialog(focusedWindow, options);
      } else {
        return await electron.dialog.showSaveDialog(options);
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.GLOBAL_SHORTCUT_REGISTER, async (_, accelerator, action) => {
      try {
        return registerGlobalShortcut(accelerator, action);
      } catch (error) {
        console.error("Error in global shortcut register handler:", error);
        return false;
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.GLOBAL_SHORTCUT_UNREGISTER, async (_, accelerator) => {
      try {
        return unregisterGlobalShortcut(accelerator);
      } catch (error) {
        console.error("Error in global shortcut unregister handler:", error);
        return false;
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.GLOBAL_SHORTCUT_UNREGISTER_ALL, async () => {
      try {
        unregisterAllGlobalShortcuts();
      } catch (error) {
        console.error("Error in global shortcut unregister all handler:", error);
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.APP_FORCE_QUIT, async () => {
      console.log("Force quit requested");
      try {
        await this.performAppCleanup();
        electron.BrowserWindow.getAllWindows().forEach((window) => {
          if (!window.isDestroyed()) {
            window.destroy();
          }
        });
        electron.app.quit();
        setTimeout(() => {
          console.log("Force exiting process...");
          process.exit(0);
        }, 2e3);
      } catch (error) {
        console.error("Error during force quit:", error);
        process.exit(1);
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.APP_CANCEL_QUIT, () => {
      console.log("Quit cancelled");
      return true;
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_SET_WORKSPACE_DIRECTORY, async (_, directory) => {
      console.log("⚠️ DB_SET_WORKSPACE_DIRECTORY is deprecated, use DB_INITIALIZE_WITH_WORKSPACE instead");
      return { success: false, error: "This method is deprecated" };
    });
    electron.ipcMain.handle(IPC_CHANNELS.DB_INITIALIZE_WITH_WORKSPACE, async (_, directory) => {
      console.log("🆕 Initializing database for first time with workspace directory:", directory);
      try {
        await kyselyDatabaseService.setWorkspaceDirectory(directory);
        setWorkspaceDirectory(directory);
        const dbResult = await kyselyDatabaseService.initialize();
        if (!dbResult.success) {
          throw new Error(dbResult.error);
        }
        console.log("✅ Database initialized successfully for first time startup");
        return { success: true, message: "Database initialized successfully" };
      } catch (error) {
        console.error("❌ Failed to initialize database for first time startup:", error);
        return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
      }
    });
  }
  /**
   * Setup window operation handlers
   */
  setupWindowHandlers() {
    electron.ipcMain.handle(IPC_CHANNELS.WINDOW_MINIMIZE, async () => {
      const focusedWindow = electron.BrowserWindow.getFocusedWindow();
      if (focusedWindow) {
        focusedWindow.minimize();
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.WINDOW_MAXIMIZE, async () => {
      const focusedWindow = electron.BrowserWindow.getFocusedWindow();
      if (focusedWindow) {
        if (focusedWindow.isMaximized()) {
          focusedWindow.unmaximize();
        } else {
          focusedWindow.maximize();
        }
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.WINDOW_CLOSE, async () => {
      const focusedWindow = electron.BrowserWindow.getFocusedWindow();
      if (focusedWindow) {
        focusedWindow.close();
      }
    });
    electron.ipcMain.handle(IPC_CHANNELS.WINDOW_IS_MAXIMIZED, async () => {
      const focusedWindow = electron.BrowserWindow.getFocusedWindow();
      if (focusedWindow) {
        return focusedWindow.isMaximized();
      }
      return false;
    });
    electron.ipcMain.handle(IPC_CHANNELS.WINDOW_TOGGLE_DEVTOOLS, async () => {
      const focusedWindow = electron.BrowserWindow.getFocusedWindow();
      if (focusedWindow) {
        focusedWindow.webContents.toggleDevTools();
      }
    });
  }
  /**
   * Setup event forwarding
   */
  setupEventForwarding() {
    fileWatcherService.on("file-system-event", (event) => {
      electron.BrowserWindow.getAllWindows().forEach((window) => {
        window.webContents.send("file-system-event", event);
      });
    });
  }
  /**
   * Perform application cleanup
   */
  async performAppCleanup() {
    try {
      console.log("Starting application cleanup...");
      console.log("Application cleanup completed");
    } catch (error) {
      console.error("Error during application cleanup:", error);
    }
  }
  /**
   * Remove all IPC handlers
   */
  cleanup() {
    if (!this.isInitialized) {
      return;
    }
    electron.ipcMain.removeAllListeners();
    this.performAppCleanup();
    this.isInitialized = false;
    console.log("IPC handlers cleaned up");
  }
}
const ipcHandler = new IpcHandler();
electron.protocol.registerSchemesAsPrivileged([
  {
    scheme: "vditor",
    privileges: {
      standard: true,
      secure: true,
      supportFetchAPI: true,
      corsEnabled: true
    }
  },
  {
    scheme: "paolife-assets",
    privileges: {
      standard: true,
      secure: true,
      supportFetchAPI: true,
      corsEnabled: true
    }
  }
]);
let mainWindow = null;
let inspirationWindow = null;
const registeredShortcuts = /* @__PURE__ */ new Map();
async function checkIfFirstTimeStartup() {
  try {
    const userDataPath = electron.app.getPath("userData");
    console.log("Checking for first time startup in:", userDataPath);
    const localStoragePath = path.join(userDataPath, "Local Storage", "leveldb");
    if (!fs.existsSync(localStoragePath)) {
      console.log("localStorage directory not found - first time startup");
      return true;
    }
    try {
      const files = fs.readdirSync(localStoragePath);
      for (const file of files) {
        const filePath = path.join(localStoragePath, file);
        try {
          const content = fs.readFileSync(filePath, "utf8");
          if (content.includes("user-settings")) {
            console.log("Found existing user settings - not first time startup");
            return false;
          }
        } catch (e) {
          continue;
        }
      }
    } catch (error) {
      console.warn("Failed to read localStorage directory:", error);
    }
    console.log("No user settings found - first time startup");
    return true;
  } catch (error) {
    console.warn("Error checking first time startup:", error);
    return true;
  }
}
async function loadUserWorkspaceDirectory() {
  try {
    const userDataPath = electron.app.getPath("userData");
    console.log("Loading user workspace directory from:", userDataPath);
    const localStoragePath = path.join(userDataPath, "Local Storage", "leveldb");
    if (fs.existsSync(localStoragePath)) {
      const files = fs.readdirSync(localStoragePath);
      for (const file of files) {
        const filePath = path.join(localStoragePath, file);
        try {
          const content = fs.readFileSync(filePath, "utf8");
          const hasUserSettings = content.includes("workspaceDirectory") || content.includes('"settings"') || content.includes("userSettingsStore");
          if (hasUserSettings) {
            console.log("Found potential user settings in file:", file);
            console.log("Content preview (first 1000 chars):", content.substring(0, 1e3));
            const patterns = [
              /"workspaceDirectory":"([^"]+)"/,
              /'workspaceDirectory':'([^']+)'/,
              /workspaceDirectory%22%3A%22([^%]+)%22/,
              /workspaceDirectory['":\s]+([^'",\s}]+)/,
              /"settings"[^}]*"workspaceDirectory":"([^"]+)"/,
              // 添加更多可能的模式
              /workspaceDirectory[^"]*"([^"]+)"/,
              /workspaceDirectory[^']*'([^']+)'/,
              // 处理可能的编码格式
              /workspaceDirectory.*?([A-Z]:\\[^"'\s,}]+)/i
            ];
            for (let i = 0; i < patterns.length; i++) {
              const pattern = patterns[i];
              console.log(`Trying pattern ${i + 1}:`, pattern.toString());
              const match = content.match(pattern);
              if (match && match[1]) {
                let workspaceDir = match[1];
                console.log("Raw match found:", workspaceDir);
                try {
                  workspaceDir = decodeURIComponent(workspaceDir);
                  console.log("After URL decode:", workspaceDir);
                } catch (e) {
                  console.log("URL decode failed, using raw value");
                }
                workspaceDir = workspaceDir.replace(/\\\\/g, "\\");
                console.log("Final processed value:", workspaceDir);
                if (workspaceDir.length > 3 && (workspaceDir.includes("\\") || workspaceDir.includes("/"))) {
                  console.log("✅ Successfully loaded user workspace directory:", workspaceDir);
                  return workspaceDir;
                } else {
                  console.log("Path looks invalid, continuing search...");
                }
              } else {
                console.log("No match for this pattern");
              }
            }
            console.log("No valid workspaceDirectory found despite having user settings");
          }
        } catch (e) {
          continue;
        }
      }
    }
    console.error("❌ No user workspace directory found in localStorage");
    return null;
  } catch (error) {
    console.error("❌ Error loading user workspace directory:", error);
    return null;
  }
}
function preCreateInspirationWindow(shouldShow = false) {
  if (inspirationWindow && !inspirationWindow.isDestroyed()) {
    if (shouldShow) {
      if (inspirationWindow.isMinimized()) {
        inspirationWindow.restore();
      }
      inspirationWindow.show();
      inspirationWindow.setOpacity(1);
      inspirationWindow.focus();
    }
    return;
  }
  const inspirationOptions = {
    width: 520,
    height: 280,
    // 增加高度以容纳标签下拉框
    frame: false,
    show: false,
    // Don't show initially
    alwaysOnTop: true,
    skipTaskbar: true,
    resizable: false,
    transparent: true,
    hasShadow: false,
    // 完全禁用窗口阴影
    backgroundColor: "#00000000",
    // Fully transparent
    opacity: 0,
    // Start with invisible
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      webSecurity: !utils.is.dev,
      allowRunningInsecureContent: utils.is.dev
    }
  };
  inspirationWindow = new electron.BrowserWindow(inspirationOptions);
  inspirationWindow.center();
  if (utils.is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    inspirationWindow.loadURL(`${process.env["ELECTRON_RENDERER_URL"]}#/inspiration-capture`);
  } else {
    inspirationWindow.loadFile(path.join(__dirname, "../renderer/index.html"), {
      hash: "inspiration-capture"
    });
  }
  if (shouldShow) {
    inspirationWindow.once("ready-to-show", () => {
      inspirationWindow?.show();
      inspirationWindow?.setOpacity(1);
      inspirationWindow?.focus();
    });
  }
  inspirationWindow.on("closed", () => {
    inspirationWindow = null;
  });
  inspirationWindow.on("blur", () => {
    if (inspirationWindow) {
      inspirationWindow.setOpacity(0);
      setTimeout(() => {
        if (inspirationWindow && !inspirationWindow.isDestroyed()) {
          inspirationWindow.hide();
        }
      }, 100);
    }
  });
}
function createInspirationWindow() {
  preCreateInspirationWindow(true);
}
function handleGlobalShortcut(action) {
  console.log("Global shortcut triggered:", action);
  switch (action) {
    case "inbox-quick-capture":
      createInspirationWindow();
      break;
    case "inbox-navigate":
      if (mainWindow) {
        if (mainWindow.isMinimized()) {
          mainWindow.restore();
        }
        mainWindow.show();
        mainWindow.focus();
        mainWindow.webContents.send("global-navigate", "/inbox");
      }
      break;
    default:
      console.log("Unknown global shortcut action:", action);
  }
}
function registerGlobalShortcut(accelerator, action) {
  try {
    if (registeredShortcuts.has(accelerator)) {
      electron.globalShortcut.unregister(accelerator);
    }
    const ret = electron.globalShortcut.register(accelerator, () => {
      handleGlobalShortcut(action);
    });
    if (ret) {
      registeredShortcuts.set(accelerator, action);
      console.log(`Global shortcut registered: ${accelerator} -> ${action}`);
    } else {
      console.log(`Failed to register global shortcut: ${accelerator}`);
    }
    return ret;
  } catch (error) {
    console.error("Error registering global shortcut:", error);
    return false;
  }
}
function unregisterGlobalShortcut(accelerator) {
  try {
    electron.globalShortcut.unregister(accelerator);
    registeredShortcuts.delete(accelerator);
    console.log(`Global shortcut unregistered: ${accelerator}`);
  } catch (error) {
    console.error("Error unregistering global shortcut:", error);
  }
}
function unregisterAllGlobalShortcuts() {
  try {
    electron.globalShortcut.unregisterAll();
    registeredShortcuts.clear();
    console.log("All global shortcuts unregistered");
  } catch (error) {
    console.error("Error unregistering all global shortcuts:", error);
  }
}
function createWindow() {
  mainWindow = new electron.BrowserWindow({
    width: 1920,
    height: 1080,
    frame: false,
    show: false,
    autoHideMenuBar: true,
    icon,
    // 为所有平台设置图标
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      // 开发模式下允许访问外部资源
      webSecurity: !utils.is.dev,
      allowRunningInsecureContent: utils.is.dev,
      // 允许data: URI字体
      additionalArguments: ["--disable-web-security", "--allow-running-insecure-content"]
    }
  });
  mainWindow.on("ready-to-show", () => {
    if (mainWindow) {
      mainWindow.maximize();
      mainWindow.show();
    }
  });
  mainWindow.on("close", async (event) => {
    if (mainWindow) {
      event.preventDefault();
      mainWindow.webContents.send("app-close-requested");
    }
  });
  mainWindow.on("closed", () => {
    mainWindow = null;
  });
  mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  if (utils.is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    if (process.env.TEST_VDITOR_PROTOCOL) {
      mainWindow.loadFile(path.join(__dirname, "../../test-vditor-protocol.html"));
    } else {
      mainWindow.loadFile(path.join(__dirname, "../renderer/index.html"));
    }
  }
}
electron.app.whenReady().then(async () => {
  utils.electronApp.setAppUserModelId("com.electron");
  electron.protocol.handle("vditor", async (request) => {
    const url = request.url.substring(9);
    const cleanUrl = url.startsWith("dist/") ? url.substring(5) : url;
    const resourcePath = path.join(__dirname, "../../resources/dist", cleanUrl);
    try {
      const { readFile } = await import("fs/promises");
      const data = await readFile(resourcePath);
      let contentType = "text/plain";
      if (url.endsWith(".css")) {
        contentType = "text/css";
      } else if (url.endsWith(".js")) {
        contentType = "application/javascript";
      } else if (url.endsWith(".json")) {
        contentType = "application/json";
      } else if (url.endsWith(".png")) {
        contentType = "image/png";
      } else if (url.endsWith(".svg")) {
        contentType = "image/svg+xml";
      }
      return new Response(data, {
        headers: { "Content-Type": contentType }
      });
    } catch (error) {
      console.error(
        "Failed to load vditor resource:",
        resourcePath,
        "Original URL:",
        request.url,
        error
      );
      return new Response("Not Found", { status: 404 });
    }
  });
  electron.protocol.handle("paolife-assets", async (request) => {
    const url = request.url.substring(17);
    const userDataPath = electron.app.getPath("userData");
    const resourcePath = path.join(userDataPath, url);
    try {
      const { readFile } = await import("fs/promises");
      const data = await readFile(resourcePath);
      let contentType = "application/octet-stream";
      if (url.endsWith(".png")) {
        contentType = "image/png";
      } else if (url.endsWith(".jpg") || url.endsWith(".jpeg")) {
        contentType = "image/jpeg";
      } else if (url.endsWith(".gif")) {
        contentType = "image/gif";
      } else if (url.endsWith(".svg")) {
        contentType = "image/svg+xml";
      } else if (url.endsWith(".webp")) {
        contentType = "image/webp";
      }
      return new Response(data, {
        headers: { "Content-Type": contentType }
      });
    } catch (error) {
      console.error("Failed to load asset:", resourcePath, "Original URL:", request.url, error);
      return new Response("Not Found", { status: 404 });
    }
  });
  const isFirstTime = await checkIfFirstTimeStartup();
  if (isFirstTime) {
    console.log("🆕 First time startup detected - delaying database initialization until user sets workspace directory");
  } else {
    console.log("🔄 Existing user detected - initializing database with saved settings");
    const userWorkspaceDir = await loadUserWorkspaceDirectory();
    if (!userWorkspaceDir) {
      console.error("❌ Failed to load user workspace directory for existing user");
      const { dialog } = await import("electron");
      await dialog.showErrorBox(
        "Configuration Error",
        "Failed to load your workspace directory settings. The application cannot start without this configuration.\n\nPlease contact support or reinstall the application."
      );
      electron.app.quit();
      return;
    }
    console.log("✅ User workspace directory loaded:", userWorkspaceDir);
    kyselyDatabaseService.setWorkspaceDirectory(userWorkspaceDir);
    const dbResult = await kyselyDatabaseService.initialize();
    if (!dbResult.success) {
      console.error("❌ Failed to initialize database with user workspace directory:", dbResult.error);
      const { dialog } = await import("electron");
      await dialog.showErrorBox(
        "Database Error",
        `Failed to initialize database at your workspace directory:
${userWorkspaceDir}

Error: ${dbResult.error}

Please check the directory permissions and try again.`
      );
      electron.app.quit();
      return;
    }
  }
  const fsResult = await fileSystemService.initialize();
  if (!fsResult.success) {
    console.error("Failed to initialize file system:", fsResult.error);
    electron.app.quit();
    return;
  }
  const watcherResult = await fileWatcherService.initialize();
  if (!watcherResult.success) {
    console.error("Failed to initialize file watcher:", watcherResult.error);
  } else {
    fileWatcherService.on("file-system-event", (event) => {
      console.log("File system event:", event);
    });
    fileWatcherService.on("error", (error) => {
      console.error("File watcher error:", error);
    });
  }
  ipcHandler.initialize();
  setInterval(() => {
    fileSystemService.cleanupLocks();
  }, 6e4);
  electron.app.on("browser-window-created", (_, window) => {
    utils.optimizer.watchWindowShortcuts(window);
    window.webContents.on("before-input-event", (event, input) => {
      if (input.key === "F12") {
        window.webContents.toggleDevTools();
      }
      if (input.control && input.shift && input.key === "I") {
        window.webContents.toggleDevTools();
      }
    });
  });
  electron.ipcMain.on("ping", () => console.log("pong"));
  createWindow();
  preCreateInspirationWindow(false);
  electron.app.on("activate", function() {
    if (electron.BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});
electron.app.on("window-all-closed", async () => {
  console.log("All windows closed, starting cleanup...");
  try {
    electron.globalShortcut.unregisterAll();
    console.log("Global shortcuts unregistered");
    ipcHandler.cleanup();
    console.log("IPC handlers cleaned up");
    await fileWatcherService.close();
    console.log("File watcher closed");
    await kyselyDatabaseService.close();
    console.log("Database connection closed");
    if (process.platform !== "darwin") {
      console.log("Quitting application...");
      electron.app.quit();
      setTimeout(() => {
        console.log("Force exiting...");
        process.exit(0);
      }, 2e3);
    }
  } catch (error) {
    console.error("Error during cleanup:", error);
    process.exit(1);
  }
});
exports.registerGlobalShortcut = registerGlobalShortcut;
exports.unregisterAllGlobalShortcuts = unregisterAllGlobalShortcuts;
exports.unregisterGlobalShortcut = unregisterGlobalShortcut;
