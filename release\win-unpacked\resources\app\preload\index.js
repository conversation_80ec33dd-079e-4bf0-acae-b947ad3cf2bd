"use strict";
const electron = require("electron");
const preload = require("@electron-toolkit/preload");
const IPC_CHANNELS = {
  // Database operations
  DB_INITIALIZE: "db:initialize",
  DB_TEST_CONNECTION: "db:test-connection",
  DB_CREATE_PROJECT: "db:create-project",
  DB_GET_PROJECTS: "db:get-projects",
  DB_GET_PROJECT_BY_ID: "db:get-project-by-id",
  DB_UPDATE_PROJECT: "db:update-project",
  DB_DELETE_PROJECT: "db:delete-project",
  DB_ARCHIVE_PROJECT: "db:archive-project",
  DB_CREATE_AREA: "db:create-area",
  DB_GET_AREAS: "db:get-areas",
  DB_GET_AREA_BY_ID: "db:get-area-by-id",
  DB_UPDATE_AREA: "db:update-area",
  DB_DELETE_AREA: "db:delete-area",
  DB_ARCHIVE_AREA: "db:archive-area",
  DB_GET_ARCHIVED_PROJECTS: "db:get-archived-projects",
  DB_GET_ARCHIVED_AREAS: "db:get-archived-areas",
  DB_RESTORE_PROJECT: "db:restore-project",
  DB_RESTORE_AREA: "db:restore-area",
  DB_CREATE_TASK: "db:create-task",
  DB_GET_TASKS: "db:get-tasks",
  DB_UPDATE_TASK: "db:update-task",
  DB_CREATE_RESOURCE: "db:create-resource",
  DB_GET_RESOURCES: "db:get-resources",
  DB_GET_PROJECT_RESOURCES: "db:get-project-resources",
  DB_LINK_RESOURCE_TO_PROJECT: "db:link-resource-to-project",
  DB_UNLINK_RESOURCE_FROM_PROJECT: "db:unlink-resource-from-project",
  DB_GET_AREA_RESOURCES: "db:get-area-resources",
  DB_UNLINK_RESOURCE_FROM_AREA: "db:unlink-resource-from-area",
  DB_GET_RESOURCE_REFERENCES: "db:get-resource-references",
  // ProjectKPI operations
  DB_CREATE_PROJECT_KPI: "db:create-project-kpi",
  DB_GET_PROJECT_KPIS: "db:get-project-kpis",
  DB_UPDATE_PROJECT_KPI: "db:update-project-kpi",
  DB_DELETE_PROJECT_KPI: "db:delete-project-kpi",
  // KPI Record operations
  DB_CREATE_KPI_RECORD: "db:create-kpi-record",
  DB_GET_KPI_RECORDS: "db:get-kpi-records",
  DB_UPDATE_KPI_RECORD: "db:update-kpi-record",
  DB_DELETE_KPI_RECORD: "db:delete-kpi-record",
  // Area Metric operations
  DB_CREATE_AREA_METRIC: "db:create-area-metric",
  DB_GET_AREA_METRICS: "db:get-area-metrics",
  DB_UPDATE_AREA_METRIC: "db:update-area-metric",
  DB_DELETE_AREA_METRIC: "db:delete-area-metric",
  // Area Metric Record operations
  DB_CREATE_AREA_METRIC_RECORD: "db:create-area-metric-record",
  DB_GET_AREA_METRIC_RECORDS: "db:get-area-metric-records",
  DB_UPDATE_AREA_METRIC_RECORD: "db:update-area-metric-record",
  DB_DELETE_AREA_METRIC_RECORD: "db:delete-area-metric-record",
  // Habit operations
  DB_GET_HABITS_BY_AREA: "db:get-habits-by-area",
  // Deliverable operations
  DB_CREATE_DELIVERABLE: "db:create-deliverable",
  DB_GET_PROJECT_DELIVERABLES: "db:get-project-deliverables",
  DB_UPDATE_DELIVERABLE: "db:update-deliverable",
  DB_DELETE_DELIVERABLE: "db:delete-deliverable",
  // Habit operations
  DB_CREATE_HABIT: "db:create-habit",
  DB_UPDATE_HABIT: "db:update-habit",
  DB_DELETE_HABIT: "db:delete-habit",
  // Habit record operations
  DB_CREATE_HABIT_RECORD: "db:create-habit-record",
  DB_UPDATE_HABIT_RECORD: "db:update-habit-record",
  DB_GET_HABIT_RECORDS: "db:get-habit-records",
  DB_DELETE_HABIT_RECORD: "db:delete-habit-record",
  // {{ AURA-X: Add - 定期维护任务IPC通道. Approval: 寸止(ID:1738157400). }}
  // Recurring task operations
  DB_CREATE_RECURRING_TASK: "db:create-recurring-task",
  DB_GET_RECURRING_TASKS: "db:get-recurring-tasks",
  DB_UPDATE_RECURRING_TASK: "db:update-recurring-task",
  DB_DELETE_RECURRING_TASK: "db:delete-recurring-task",
  // Document Link operations
  DB_CREATE_DOCUMENT_LINK: "db:create-document-link",
  DB_GET_DOCUMENT_LINKS: "db:get-document-links",
  DB_GET_BACKLINKS: "db:get-backlinks",
  DB_GET_OUTLINKS: "db:get-outlinks",
  DB_UPDATE_DOCUMENT_LINK: "db:update-document-link",
  DB_DELETE_DOCUMENT_LINK: "db:delete-document-link",
  DB_DELETE_DOCUMENT_LINKS: "db:delete-document-links",
  DB_UPDATE_DOCUMENT_PATH: "db:update-document-path",
  DB_MARK_LINKS_INVALID: "db:mark-links-invalid",
  DB_MARK_LINKS_VALID: "db:mark-links-valid",
  DB_GET_LINK_STATISTICS: "db:get-link-statistics",
  DB_SEARCH_LINKS: "db:search-links",
  DB_REPLACE_DOCUMENT_LINKS: "db:replace-document-links",
  // Checklist operations
  DB_CREATE_CHECKLIST: "db:create-checklist",
  DB_GET_CHECKLISTS: "db:get-checklists",
  DB_UPDATE_CHECKLIST: "db:update-checklist",
  DB_DELETE_CHECKLIST: "db:delete-checklist",
  // Checklist Instance operations
  DB_CREATE_CHECKLIST_INSTANCE: "db:create-checklist-instance",
  DB_GET_CHECKLIST_INSTANCES: "db:get-checklist-instances",
  DB_UPDATE_CHECKLIST_INSTANCE: "db:update-checklist-instance",
  DB_DELETE_CHECKLIST_INSTANCE: "db:delete-checklist-instance",
  // File system operations
  FS_INITIALIZE: "fs:initialize",
  FS_REINITIALIZE: "fs:reinitialize",
  FS_READ_FILE: "fs:read-file",
  FS_WRITE_FILE: "fs:write-file",
  FS_DELETE_FILE: "fs:delete-file",
  FS_MOVE_FILE: "fs:move-file",
  FS_COPY_FILE: "fs:copy-file",
  FS_FILE_EXISTS: "fs:file-exists",
  FS_GET_FILE_INFO: "fs:get-file-info",
  FS_LIST_DIRECTORY: "fs:list-directory",
  FS_CREATE_DIRECTORY: "fs:create-directory",
  FS_RENAME: "fs:rename",
  FS_DELETE_DIRECTORY: "fs:delete-directory",
  FS_WATCH_DIRECTORY: "fs:watch-directory",
  FS_UNWATCH_DIRECTORY: "fs:unwatch-directory",
  // File system events (from main to renderer)
  FS_EVENT: "fs:event",
  // Review operations
  DB_CREATE_REVIEW: "db:create-review",
  DB_GET_REVIEWS: "db:get-reviews",
  DB_GET_REVIEW_BY_ID: "db:get-review-by-id",
  DB_UPDATE_REVIEW: "db:update-review",
  DB_DELETE_REVIEW: "db:delete-review",
  // Review Template operations
  DB_CREATE_REVIEW_TEMPLATE: "db:create-review-template",
  DB_GET_REVIEW_TEMPLATES: "db:get-review-templates",
  DB_GET_REVIEW_TEMPLATE_BY_ID: "db:get-review-template-by-id",
  DB_UPDATE_REVIEW_TEMPLATE: "db:update-review-template",
  DB_DELETE_REVIEW_TEMPLATE: "db:delete-review-template",
  // Review Data Aggregation
  DB_GET_REVIEW_AGGREGATED_DATA: "db:get-review-aggregated-data",
  // Review Analysis
  DB_GET_REVIEW_ANALYSIS: "db:get-review-analysis",
  // Settings operations
  SETTINGS_GET_USER_SETTINGS: "settings:get-user-settings",
  SETTINGS_UPDATE_USER_SETTINGS: "settings:update-user-settings",
  SETTINGS_RESET_USER_SETTINGS: "settings:reset-user-settings",
  SETTINGS_EXPORT_DATA: "settings:export-data",
  SETTINGS_IMPORT_DATA: "settings:import-data",
  SETTINGS_CREATE_BACKUP: "settings:create-backup",
  SETTINGS_GET_DATABASE_INFO: "settings:get-database-info",
  SETTINGS_SELECT_RESOURCE_PATH: "settings:select-resource-path",
  SETTINGS_UPDATE_RESOURCE_PATH: "settings:update-resource-path",
  // Application operations
  APP_GET_VERSION: "app:get-version",
  APP_GET_PATH: "app:get-path",
  APP_SHOW_MESSAGE_BOX: "app:show-message-box",
  APP_SHOW_ERROR_BOX: "app:show-error-box",
  APP_SHOW_OPEN_DIALOG: "app:show-open-dialog",
  APP_SHOW_SAVE_DIALOG: "app:show-save-dialog",
  // Window operations
  WINDOW_MINIMIZE: "window:minimize",
  WINDOW_MAXIMIZE: "window:maximize",
  WINDOW_CLOSE: "window:close",
  WINDOW_TOGGLE_DEVTOOLS: "window:toggle-devtools",
  WINDOW_IS_MAXIMIZED: "window:is-maximized",
  // Global shortcuts
  GLOBAL_SHORTCUT_REGISTER: "global-shortcut:register",
  GLOBAL_SHORTCUT_UNREGISTER: "global-shortcut:unregister",
  GLOBAL_SHORTCUT_UNREGISTER_ALL: "global-shortcut:unregister-all",
  // App control
  APP_FORCE_QUIT: "app-force-quit",
  APP_CANCEL_QUIT: "app-cancel-quit",
  // Database configuration
  DB_SET_WORKSPACE_DIRECTORY: "db:set-workspace-directory",
  DB_INITIALIZE_WITH_WORKSPACE: "db:initialize-with-workspace"
};
const api = {
  // Database API
  database: {
    initialize: () => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_INITIALIZE),
    testConnection: () => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_TEST_CONNECTION),
    getStatus: () => electron.ipcRenderer.invoke("db-get-status"),
    createProject: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_PROJECT, data),
    getProjects: () => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_PROJECTS),
    getProjectById: (id, includeArchived) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_PROJECT_BY_ID, id, includeArchived),
    updateProject: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_PROJECT, data),
    deleteProject: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_PROJECT, id),
    archiveProject: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_ARCHIVE_PROJECT, id),
    createArea: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_AREA, data),
    getAreas: () => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_AREAS),
    getAreaById: (id, includeArchived) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_AREA_BY_ID, id, includeArchived),
    updateArea: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_AREA, data),
    deleteArea: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_AREA, id),
    archiveArea: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_ARCHIVE_AREA, id),
    getArchivedProjects: () => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_ARCHIVED_PROJECTS),
    getArchivedAreas: () => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_ARCHIVED_AREAS),
    restoreProject: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_RESTORE_PROJECT, id),
    restoreArea: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_RESTORE_AREA, id),
    createTask: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_TASK, data),
    getTasks: (filters) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_TASKS, filters),
    updateTask: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_TASK, data),
    createResource: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_RESOURCE, data),
    getResources: (filters) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_RESOURCES, filters),
    // ProjectKPI operations
    createProjectKPI: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_PROJECT_KPI, data),
    getProjectKPIs: (projectId) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_PROJECT_KPIS, projectId),
    updateProjectKPI: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_PROJECT_KPI, data),
    deleteProjectKPI: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_PROJECT_KPI, id),
    // KPI Record operations
    createKPIRecord: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_KPI_RECORD, data),
    getKPIRecords: (kpiId, limit) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_KPI_RECORDS, kpiId, limit),
    updateKPIRecord: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_KPI_RECORD, data),
    deleteKPIRecord: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_KPI_RECORD, id),
    // Area Metric operations
    createAreaMetric: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_AREA_METRIC, data),
    getAreaMetrics: (areaId) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_AREA_METRICS, areaId),
    updateAreaMetric: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_AREA_METRIC, data),
    deleteAreaMetric: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_AREA_METRIC, id),
    // Area Metric Record operations
    createAreaMetricRecord: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_AREA_METRIC_RECORD, data),
    getAreaMetricRecords: (metricId, limit) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_AREA_METRIC_RECORDS, metricId, limit),
    updateAreaMetricRecord: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_AREA_METRIC_RECORD, data),
    deleteAreaMetricRecord: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_AREA_METRIC_RECORD, id),
    // Habit operations
    getHabitsByArea: (areaId) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_HABITS_BY_AREA, areaId),
    // Deliverable operations
    createDeliverable: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_DELIVERABLE, data),
    getProjectDeliverables: (projectId) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_PROJECT_DELIVERABLES, projectId),
    updateDeliverable: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_DELIVERABLE, data),
    deleteDeliverable: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_DELIVERABLE, id),
    // Habit operations
    createHabit: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_HABIT, data),
    updateHabit: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_HABIT, data),
    deleteHabit: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_HABIT, id),
    // Habit record operations
    createHabitRecord: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_HABIT_RECORD, data),
    updateHabitRecord: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_HABIT_RECORD, data),
    getHabitRecords: (habitId) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_HABIT_RECORDS, habitId),
    deleteHabitRecord: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_HABIT_RECORD, id),
    // {{ AURA-X: Add - 定期维护任务preload API. Approval: 寸止(ID:1738157400). }}
    // Recurring task operations
    createRecurringTask: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_RECURRING_TASK, data),
    getRecurringTasks: (areaId) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_RECURRING_TASKS, areaId),
    updateRecurringTask: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_RECURRING_TASK, data),
    deleteRecurringTask: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_RECURRING_TASK, id),
    // Resource linking operations
    getProjectResources: (projectId) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_PROJECT_RESOURCES, projectId),
    linkResourceToProject: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_LINK_RESOURCE_TO_PROJECT, data),
    unlinkResourceFromProject: (resourceId, projectId) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UNLINK_RESOURCE_FROM_PROJECT, resourceId, projectId),
    getAreaResources: (areaId) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_AREA_RESOURCES, areaId),
    unlinkResourceFromArea: (resourceId, areaId) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UNLINK_RESOURCE_FROM_AREA, resourceId, areaId),
    getResourceReferences: (resourcePath) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_RESOURCE_REFERENCES, resourcePath),
    // Document Link operations
    createDocumentLink: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_DOCUMENT_LINK, data),
    getDocumentLinks: (docPath) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_DOCUMENT_LINKS, docPath),
    getBacklinks: (docPath) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_BACKLINKS, docPath),
    getOutlinks: (docPath) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_OUTLINKS, docPath),
    updateDocumentLink: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_DOCUMENT_LINK, data),
    deleteDocumentLink: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_DOCUMENT_LINK, id),
    deleteDocumentLinks: (docPath) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_DOCUMENT_LINKS, docPath),
    updateDocumentPath: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_DOCUMENT_PATH, data),
    markLinksAsInvalid: (targetDocPath) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_MARK_LINKS_INVALID, targetDocPath),
    markLinksAsValid: (targetDocPath) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_MARK_LINKS_VALID, targetDocPath),
    getLinkStatistics: (docPath) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_LINK_STATISTICS, docPath),
    searchLinks: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_SEARCH_LINKS, data),
    replaceDocumentLinks: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_REPLACE_DOCUMENT_LINKS, data),
    // Checklist operations
    createChecklist: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_CHECKLIST, data),
    getChecklists: (areaId) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_CHECKLISTS, areaId),
    updateChecklist: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_CHECKLIST, data),
    deleteChecklist: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_CHECKLIST, id),
    // Checklist Instance operations
    createChecklistInstance: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_CHECKLIST_INSTANCE, data),
    getChecklistInstances: (areaId) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_CHECKLIST_INSTANCES, areaId),
    updateChecklistInstance: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_CHECKLIST_INSTANCE, data),
    deleteChecklistInstance: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_CHECKLIST_INSTANCE, id),
    // Review operations
    createReview: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_REVIEW, data),
    getReviews: (filters) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_REVIEWS, filters),
    getReviewById: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_REVIEW_BY_ID, id),
    updateReview: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_REVIEW, data),
    deleteReview: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_REVIEW, id),
    // Review Template operations
    createReviewTemplate: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_REVIEW_TEMPLATE, data),
    getReviewTemplates: (type) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_REVIEW_TEMPLATES, type),
    getReviewTemplateById: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_REVIEW_TEMPLATE_BY_ID, id),
    updateReviewTemplate: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_REVIEW_TEMPLATE, data),
    deleteReviewTemplate: (id) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_REVIEW_TEMPLATE, id),
    // Review Data Aggregation
    getReviewAggregatedData: (type, period) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_REVIEW_AGGREGATED_DATA, type, period),
    // Review Analysis
    getReviewAnalysis: (type, period) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_GET_REVIEW_ANALYSIS, type, period)
  },
  // File System API
  fileSystem: {
    initialize: () => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_INITIALIZE),
    reinitialize: (workspaceDirectory) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_REINITIALIZE, workspaceDirectory),
    readFile: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_READ_FILE, data),
    writeFile: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_WRITE_FILE, data),
    deleteFile: (path) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_DELETE_FILE, path),
    moveFile: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_MOVE_FILE, data),
    copyFile: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_COPY_FILE, data),
    fileExists: (path) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_FILE_EXISTS, path),
    getFileInfo: (path) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_GET_FILE_INFO, path),
    listDirectory: (path) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_LIST_DIRECTORY, path),
    createDirectory: (path) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_CREATE_DIRECTORY, path),
    deleteDirectory: (path) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_DELETE_DIRECTORY, path),
    rename: (oldPath, newPath) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_RENAME, oldPath, newPath),
    watchDirectory: (path) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_WATCH_DIRECTORY, path),
    unwatchDirectory: (path) => electron.ipcRenderer.invoke(IPC_CHANNELS.FS_UNWATCH_DIRECTORY, path)
  },
  // Application API
  app: {
    getVersion: () => electron.ipcRenderer.invoke(IPC_CHANNELS.APP_GET_VERSION),
    getPath: (name) => electron.ipcRenderer.invoke(IPC_CHANNELS.APP_GET_PATH, name),
    showMessageBox: (options) => electron.ipcRenderer.invoke(IPC_CHANNELS.APP_SHOW_MESSAGE_BOX, options),
    showErrorBox: (title, content) => electron.ipcRenderer.invoke(IPC_CHANNELS.APP_SHOW_ERROR_BOX, title, content),
    showOpenDialog: (options) => electron.ipcRenderer.invoke(IPC_CHANNELS.APP_SHOW_OPEN_DIALOG, options),
    showSaveDialog: (options) => electron.ipcRenderer.invoke(IPC_CHANNELS.APP_SHOW_SAVE_DIALOG, options)
  },
  // Settings API
  settings: {
    getUserSettings: () => electron.ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_GET_USER_SETTINGS),
    updateUserSettings: (settings) => electron.ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_UPDATE_USER_SETTINGS, settings),
    resetUserSettings: () => electron.ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_RESET_USER_SETTINGS),
    exportData: () => electron.ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_EXPORT_DATA),
    importData: (data) => electron.ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_IMPORT_DATA, data),
    createBackup: () => electron.ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_CREATE_BACKUP),
    getDatabaseInfo: () => electron.ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_GET_DATABASE_INFO),
    selectResourcePath: () => electron.ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_SELECT_RESOURCE_PATH),
    updateResourcePath: (path) => electron.ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_UPDATE_RESOURCE_PATH, path)
  },
  // Window API
  window: {
    minimize: () => electron.ipcRenderer.invoke(IPC_CHANNELS.WINDOW_MINIMIZE),
    maximize: () => electron.ipcRenderer.invoke(IPC_CHANNELS.WINDOW_MAXIMIZE),
    close: () => electron.ipcRenderer.invoke(IPC_CHANNELS.WINDOW_CLOSE),
    toggleDevTools: () => electron.ipcRenderer.invoke(IPC_CHANNELS.WINDOW_TOGGLE_DEVTOOLS),
    isMaximized: () => electron.ipcRenderer.invoke(IPC_CHANNELS.WINDOW_IS_MAXIMIZED),
    registerGlobalShortcut: (accelerator, action) => electron.ipcRenderer.invoke(IPC_CHANNELS.GLOBAL_SHORTCUT_REGISTER, accelerator, action),
    unregisterGlobalShortcut: (accelerator) => electron.ipcRenderer.invoke(IPC_CHANNELS.GLOBAL_SHORTCUT_UNREGISTER, accelerator),
    unregisterAllGlobalShortcuts: () => electron.ipcRenderer.invoke(IPC_CHANNELS.GLOBAL_SHORTCUT_UNREGISTER_ALL),
    forceQuit: () => electron.ipcRenderer.invoke(IPC_CHANNELS.APP_FORCE_QUIT),
    cancelQuit: () => electron.ipcRenderer.invoke(IPC_CHANNELS.APP_CANCEL_QUIT),
    setWorkspaceDirectory: (directory) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_SET_WORKSPACE_DIRECTORY, directory),
    initializeDatabaseWithWorkspace: (directory) => electron.ipcRenderer.invoke(IPC_CHANNELS.DB_INITIALIZE_WITH_WORKSPACE, directory)
  },
  // Event listeners
  onFileSystemEvent: (callback) => {
    const unsubscribe = () => electron.ipcRenderer.removeAllListeners(IPC_CHANNELS.FS_EVENT);
    electron.ipcRenderer.on(IPC_CHANNELS.FS_EVENT, (_, event) => callback(event));
    return unsubscribe;
  },
  onDatabaseEvent: (callback) => {
    const unsubscribe = () => electron.ipcRenderer.removeAllListeners("database-event");
    electron.ipcRenderer.on("database-event", (_, event) => callback(event));
    return unsubscribe;
  },
  // IPC Renderer for global shortcuts and events
  ipcRenderer: {
    on: (channel, listener) => {
      electron.ipcRenderer.on(channel, listener);
    },
    removeAllListeners: (channel) => {
      electron.ipcRenderer.removeAllListeners(channel);
    },
    removeListener: (channel, listener) => {
      electron.ipcRenderer.removeListener(channel, listener);
    }
  }
};
if (process.contextIsolated) {
  try {
    electron.contextBridge.exposeInMainWorld("electron", preload.electronAPI);
    electron.contextBridge.exposeInMainWorld("electronAPI", api);
  } catch (error) {
    console.error(error);
  }
} else {
  window.electron = preload.electronAPI;
  window.electronAPI = api;
}
