// Shared types between main and renderer processes

export interface DatabaseConfig {
  databasePath: string
  userDataPath: string
  isDevelopment: boolean
}

export interface DatabaseResult<T = any> {
  success: boolean
  data?: T
  error?: string
}

// Re-export database types for convenience
export type {
  User,
  Project,
  ProjectKPI,
  KPIRecord,
  Area,
  AreaMetric,
  AreaMetricRecord,
  Habit,
  HabitRecord,
  Checklist,
  ChecklistInstance,
  Task,
  TaskTag,
  TaskTagRelation,
  ResourceLink,
  Review,
  ReviewTemplate,
  DocumentLink,
  Deliverable,
  RecurringTask
} from './database-types'
