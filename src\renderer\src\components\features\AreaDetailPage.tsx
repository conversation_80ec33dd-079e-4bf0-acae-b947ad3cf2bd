import { useState, useMemo, useEffect, useRef } from 'react'
import { useParams, Link, useNavigate, useLocation } from 'react-router-dom'
import { Card, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Textarea } from '../ui/textarea'
import { Tooltip, TooltipTrigger, TooltipContent } from '../ui/tooltip'
import { ArrowLeft, Edit, Archive, Trash2 } from 'lucide-react'
import { cn } from '../../lib/utils'
import { useAreaStore } from '../../store/areaStore'
import { useProjectStore } from '../../store/projectStore'
import { useTaskStore } from '../../store/taskStore'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'
import { useConfirmDialog } from '../shared/ConfirmDialog'
import { databaseApi } from '../../lib/api'
import CreateAreaDialog from './CreateAreaDialog'
import CreateHabitDialog from './CreateHabitDialog'
import CreateProjectDialog from './CreateProjectDialog'

import ChecklistTemplateManager from './ChecklistTemplateManager'
import CreateRecurringTaskDialog from './CreateRecurringTaskDialog'
// 新的紧凑组件
import MiniHabitTracker from './MiniHabitTracker'
import CompactProjectList from './CompactProjectList'
import { UnifiedResources } from './ProjectResources'
import CompactChecklistTemplates from './CompactChecklistTemplates'
import RecurringMaintenanceTasks, { RecurringMaintenanceTasksRef } from './RecurringMaintenanceTasks'
import ActiveChecklists from './ActiveChecklists'
import AreaKPIManagement from './AreaKPIManagement'
import AreaOverviewStats from './AreaOverviewStats'
import AreaStatsChips from './AreaStatsChips'
import type { Area, Project } from '../../../../shared/types'
import type { KPIStatistics } from '../../../../shared/types/kpi'
// {{ AURA-X: Add - 导入KPI管理器用于获取统计数据. Approval: 寸止(ID:1738157400). }}
import { createAreaMetricManager } from '../../lib/kpiApiAdapters'

export function AreaDetailPage() {
  const { areaId } = useParams<{ areaId: string }>()
  const navigate = useNavigate()
  const location = useLocation()
  const isArchived = location.state?.archived || false
  const {
    areas,
    updateArea,
    deleteArea,
    archiveArea,
    // {{ AURA-X: Add - 集成习惯管理功能. Approval: 寸止(ID:1738157400). }}
    habits,
    addHabit,
    updateHabit,
    deleteHabit: deleteHabitFromStore,
    habitRecords: rawHabitRecords,
    toggleHabitRecord,
    fetchHabitsForArea
  } = useAreaStore()
  const { projects, addProject } = useProjectStore()
  // {{ AURA-X: Add - 获取任务数据用于显示关联任务. Approval: 寸止(ID:1738157400). }}
  const { tasks } = useTaskStore()
  const { addNotification } = useUIStore()
  const { t } = useLanguage()
  const { confirm, ConfirmDialog: ConfirmDialogComponent } = useConfirmDialog()

  // {{ AURA-X: Add - 单独获取领域数据状态，支持归档领域. Approval: 寸止(ID:归档修复). }}
  const [areaData, setAreaData] = useState<any>(null)
  const [isLoadingArea, setIsLoadingArea] = useState(false)

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isCreateHabitDialogOpen, setIsCreateHabitDialogOpen] = useState(false)
  const [editingHabit, setEditingHabit] = useState<any>(null)
  const [isCreateProjectDialogOpen, setIsCreateProjectDialogOpen] = useState(false)
  const [isStandardEditing, setIsStandardEditing] = useState(false)
  const [standardText, setStandardText] = useState('')
  const [isTemplateManagerOpen, setIsTemplateManagerOpen] = useState(false)
  const [isCreateRecurringTaskDialogOpen, setIsCreateRecurringTaskDialogOpen] = useState(false)
  const [recurringTasksRefreshKey, setRecurringTasksRefreshKey] = useState(0)
  const recurringTasksRef = useRef<RecurringMaintenanceTasksRef>(null)
  // {{ AURA-X: Add - KPI统计数据状态. Approval: 寸止(ID:1738157400). }}
  const [kpiStatistics, setKpiStatistics] = useState<KPIStatistics | null>(null)

  // {{ AURA-X: Add - 获取领域数据，支持归档领域. Approval: 寸止(ID:归档修复). }}
  useEffect(() => {
    const fetchAreaData = async () => {
      if (!areaId) return

      setIsLoadingArea(true)
      try {
        // 如果是归档状态，直接通过API获取领域数据（包括归档领域）
        if (isArchived) {
          const result = await databaseApi.getAreaById(areaId, true)
          if (result.success && result.data) {
            setAreaData(result.data)
          } else {
            addNotification({
              type: 'error',
              title: t('pages.areas.detail.notFound'),
              message: t('pages.areas.detail.notFoundDescription')
            })
            navigate('/archive')
          }
        } else {
          // 非归档状态，从store获取数据
          const area = areas.find(a => a.id === areaId)
          if (area) {
            setAreaData(area)
          } else {
            // 如果store中没有，尝试通过API获取
            const result = await databaseApi.getAreaById(areaId, false)
            if (result.success && result.data) {
              setAreaData(result.data)
            } else {
              addNotification({
                type: 'error',
                title: t('pages.areas.detail.notFound'),
                message: t('pages.areas.detail.notFoundDescription')
              })
              navigate('/areas')
            }
          }
        }
      } catch (error) {
        console.error('Failed to fetch area data:', error)
        addNotification({
          type: 'error',
          title: t('pages.areas.detail.loadingFailed'),
          message: t('pages.areas.detail.loadingFailedMessage')
        })
      } finally {
        setIsLoadingArea(false)
      }
    }

    fetchAreaData()
  }, [areaId, isArchived, areas])

  const area = areaData || areas.find((a) => a.id === areaId)
  const relatedProjects = projects.filter(
    (project) => project.areaId === areaId && !project.archived
  )

  // {{ AURA-X: Add - 计算与该领域关联的任务. Approval: 寸止(ID:1738157400). }}
  const areaTasks = tasks.filter(task => task.areaId === areaId && !task.completed)

  // 安全地处理 habitRecords
  const habitRecords = useMemo(() => {
    try {
      return rawHabitRecords.map((record) => {
        try {
          // 验证每个记录的日期
          if (record.date) {
            if (typeof record.date === 'string') {
              new Date(record.date).toISOString()
            } else {
              new Date(record.date).toISOString()
            }
          }
          return record
        } catch {
          // 返回一个带有有效日期的记录
          return {
            ...record,
            date: new Date().toISOString()
          }
        }
      })
    } catch {
      return []
    }
  }, [rawHabitRecords])



  // {{ AURA-X: Add - 初始化领域标准文本. Approval: 寸止(ID:1738157400). }}
  useEffect(() => {
    if (area && area.standard !== undefined) {
      setStandardText(area.standard || '')
    }
  }, [area])

  // {{ AURA-X: Add - 页面加载时滚动到顶部. Approval: 寸止(ID:1738157400). }}
  useEffect(() => {
    const scrollToTop = () => {
      // 找到真正的滚动容器 - Layout 中的主内容滚动容器
      const scrollContainer = document.querySelector('main .overflow-y-auto')
      if (scrollContainer) {
        scrollContainer.scrollTop = 0
      }

      // 备用方案：重置其他可能的滚动容器
      window.scrollTo({ top: 0, left: 0, behavior: 'instant' })
      document.documentElement.scrollTop = 0
      document.body.scrollTop = 0
    }

    // 立即执行
    scrollToTop()

    // 延迟执行，确保 DOM 完全更新
    const timer1 = setTimeout(scrollToTop, 0)
    const timer2 = setTimeout(scrollToTop, 100)

    return () => {
      clearTimeout(timer1)
      clearTimeout(timer2)
    }
  }, [areaId])

  // {{ AURA-X: Add - 加载KPI统计数据. Approval: 寸止(ID:1738157400). }}
  useEffect(() => {
    const loadKPIStatistics = async () => {
      if (!areaId) return

      try {
        const metricManager = createAreaMetricManager()
        const stats = await metricManager.getStatistics(areaId)
        setKpiStatistics(stats)
      } catch (error) {
        console.error('Failed to load KPI statistics:', error)
        // 如果加载失败，设置为null，组件会使用清单数据作为后备
        setKpiStatistics(null)
      }
    }

    loadKPIStatistics()
  }, [areaId])

  // {{ AURA-X: Move - 将 areaHabits/adaptedHabits 提前到 stats 之前，避免“Cannot access before initialization”. Confirmed via 寸止 }}
  // Calculate area statistics
  const areaHabits = habits.filter((habit) => habit.areaId === areaId)

  // Convert database habits to HabitItem format
  const adaptedHabits = areaHabits.map((habit) => ({
    ...habit,
    color: '#3b82f6',
    frequency: habit.frequency as 'daily' | 'weekly' | 'monthly',
    records: habitRecords
      .filter((record) => record.habitId === habit.id)
      .map((record) => ({
        date: (record.date instanceof Date ? record.date : new Date(record.date)).toISOString().split('T')[0],
        completed: record.completed,
        value: undefined,
        note: undefined
      })),
    createdAt: (habit.createdAt instanceof Date ? habit.createdAt : new Date(habit.createdAt)).toISOString(),
    updatedAt: (habit as any).updatedAt
      ? ((habit as any).updatedAt instanceof Date ? (habit as any).updatedAt : new Date((habit as any).updatedAt)).toISOString()
      : new Date().toISOString()
  }))

  // {{ AURA-X: Move - 将 stats 计算前置到所有 early return 之前，确保 Hooks 顺序稳定。Confirmed via 寸止 }}
  const stats = useMemo(() => {
    const totalProjects = relatedProjects.length
    const completedProjects = relatedProjects.filter((p) => p.status === 'Completed').length
    const activeHabits = areaHabits.length
    const { checklistInstances, checklists } = useTaskStore.getState()
    const areaInstances = checklistInstances.filter((instance) => {
      const template = checklists.find((c) => c.id === instance.checklistId)
      return template && (template as any).areaId === areaId
    })
    const completedInstances = areaInstances.filter((instance) => instance.completedAt).length
    const checklistProgress = areaInstances.length > 0 ? Math.round((completedInstances / areaInstances.length) * 100) : 0

    return {
      totalProjects,
      completedProjects,
      activeHabits,
      checklistProgress,
      completedChecklist: completedInstances,
      totalChecklist: areaInstances.length
    }
  }, [relatedProjects, areaHabits.length, areaId])

  // {{ AURA-X: Move - 将统计相关的 useMemo 提前到所有 early return 之前，避免 Hooks 顺序变化。Confirmed via 寸止 }}
  const habitCompletionToday = useMemo(() => {
    if (adaptedHabits.length === 0) return 0

    const today = new Date().toISOString().split('T')[0]
    return adaptedHabits.filter(habit =>
      habit.records && habit.records.some(record => record.date === today && record.completed)
    ).length
  }, [adaptedHabits])

  const healthScore = useMemo(() => {
    let score = 0
    let factors = 0

    // 项目完成率 (30%)
    if (stats.totalProjects > 0) {
      score += (stats.completedProjects / stats.totalProjects) * 30
      factors += 30
    }

    // 习惯完成率 (40%)
    if (adaptedHabits.length > 0) {
      score += (habitCompletionToday / adaptedHabits.length) * 40
      factors += 40
    }

    // 清单完成率 (30%)
    if (stats.totalChecklist > 0) {
      score += (stats.checklistProgress / 100) * 30
      factors += 30
    }

    return factors > 0 ? Math.round(score * 100 / factors) : 0
  }, [stats, adaptedHabits, habitCompletionToday])

  // {{ AURA-X: Add - 加载状态检查. Approval: 寸止(ID:归档修复). }}
  if (isLoadingArea) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-2 text-muted-foreground">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            <span>{t('pages.areas.detail.loading')}</span>
          </div>
        </div>
      </div>
    )
  }

  if (!area) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-4xl mb-4">🏠</div>
          <h2 className="text-xl font-semibold mb-2">{t('pages.areas.detail.notFound')}</h2>
          <p className="text-muted-foreground mb-4">
            {t('pages.areas.detail.notFoundDescription')}
          </p>
          <Button asChild>
            <Link to="/areas">{t('pages.areas.detail.backToAreasButton')}</Link>
          </Button>
        </div>
      </div>
    )
  }

  // {{ AURA-X: Note - areaHabits/adaptedHabits 已上移至 early return 之前，避免重复与初始化顺序问题。Confirmed via 寸止 }}

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'Needs Attention':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'On Hold':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'Review Required':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 统一编辑入口：调用 store.updateArea（已内置持久化），编辑后进行一次轻量回读
  const handleEditArea = async (areaData: Omit<Area, 'id' | 'createdAt' | 'updatedAt'>) => {
    updateArea(area.id, {
      ...areaData,
      updatedAt: new Date()
    })
    try {
      const res = await databaseApi.getAreaById(area.id, false)
      if (res.success && res.data) {
        useAreaStore.getState().setAreas(
          useAreaStore.getState().areas.map(a => a.id === area.id ? { ...a, ...res.data } : a)
        )
      }
    } catch {}
    setIsEditDialogOpen(false)
  }

  const handleDeleteArea = async () => {
    const confirmed = await confirm({
      title: t('pages.areas.detail.deleteConfirmTitle'),
      description: t('pages.areas.detail.deleteConfirmMessage', { name: area.name }),
      variant: 'destructive',
      confirmText: t('common.delete'),
      cancelText: t('common.cancel')
    })

    if (confirmed) {
      deleteArea(area.id)
      navigate('/areas')
    }
  }

  const handleArchiveArea = async () => {
    // {{ AURA-X: Fix - 修复确认对话框的使用方式，使用Promise而不是回调. Approval: 寸止(ID:1738157400). }}
    const confirmed = await confirm({
      title: t('pages.areas.detail.archiveConfirmTitle'),
      description: t('pages.areas.detail.archiveConfirmMessage', { name: area.name }),
      variant: 'warning',
      confirmText: t('pages.areas.detail.archiveArea'),
      cancelText: t('common.cancel')
    })

    if (confirmed) {
      try {
        await archiveArea(area.id)
        // {{ AURA-X: Add - 添加归档成功通知. Approval: 寸止(ID:1738157400). }}
        addNotification({
          type: 'success',
          title: t('pages.areas.detail.archiveSuccessTitle'),
          message: t('pages.areas.detail.archiveSuccessMessage', { name: area.name })
        })
        navigate('/areas')
      } catch (error) {
        addNotification({
          type: 'error',
          title: t('pages.areas.detail.archiveFailedTitle'),
          message: t('pages.areas.detail.archiveFailedMessage')
        })
      }
    }
  }

  // {{ AURA-X: Modify - 修复项目创建的数据持久化问题，直接在创建时设置areaId. Approval: 寸止(ID:1738157400). }}
  const handleCreateProject = async (projectData: any) => {
    try {
      // 调用数据库API创建项目，直接包含areaId
      const result = await databaseApi.createProject({
        name: projectData.name,
        description: projectData.description || undefined,
        goal: projectData.goal || undefined,
        areaId: areaId || undefined  // 直接在创建时设置领域关联
      })

      if (!result.success) {
        throw new Error(result.error || t('pages.areas.detail.projectCreateFailedMessage'))
      }

      // 如果有其他字段需要更新，进行更新操作
      let finalProjectData = result.data
      if (projectData.status || projectData.progress || projectData.deliverable || projectData.startDate || projectData.deadline) {
        const updateResult = await databaseApi.updateProject({
          id: result.data.id,
          updates: {
            status: projectData.status || undefined,
            progress: projectData.progress || undefined,
            deliverable: projectData.deliverable || undefined,
            startDate: projectData.startDate || undefined,
            deadline: projectData.deadline || undefined
          }
        })

        if (updateResult.success) {
          finalProjectData = updateResult.data
        } else {
          console.warn('Failed to update additional project fields:', updateResult.error)
        }
      }

      // 使用数据库返回的数据创建完整的项目对象
      const newProject: Project = {
        id: finalProjectData.id,
        name: finalProjectData.name,
        description: finalProjectData.description,
        goal: finalProjectData.goal,
        deliverable: finalProjectData.deliverable || projectData.deliverable,
        status: finalProjectData.status || projectData.status || 'Not Started',
        progress: finalProjectData.progress || projectData.progress || 0,
        startDate: finalProjectData.startDate
          ? new Date(finalProjectData.startDate)
          : projectData.startDate || null,
        deadline: finalProjectData.deadline
          ? new Date(finalProjectData.deadline)
          : projectData.deadline || null,
        areaId: finalProjectData.areaId || areaId || null, // 确保关联到当前领域
        archived: finalProjectData.archived || false,
        createdAt: new Date(finalProjectData.createdAt),
        updatedAt: new Date(finalProjectData.updatedAt)
      }

      // 添加到store
      addProject(newProject)
      setIsCreateProjectDialogOpen(false)

      // 显示成功通知
      addNotification({
        type: 'success',
        title: t('pages.areas.detail.projectCreateSuccessTitle'),
        message: t('pages.areas.detail.projectCreateSuccessMessage', { name: projectData.name })
      })
    } catch (error) {
      console.error('Failed to create project:', error)
      addNotification({
        type: 'error',
        title: t('pages.areas.detail.projectCreateFailedTitle'),
        message: error instanceof Error ? error.message : t('common.unknown')
      })
    }
  }

  // {{ AURA-X: Add - 添加领域标准保存功能. Approval: 寸止(ID:1738157400). }}
  const handleSaveStandard = () => {
    updateArea(area.id, { standard: standardText.trim() })
    setIsStandardEditing(false)
  }

  // {{ AURA-X: Modify - 使用数据库API创建习惯. Approval: 寸止(ID:1738157400). }}
  const handleCreateHabit = async (habitData: any) => {
    try {
      const result = await databaseApi.createHabit({
        name: habitData.name,
        areaId: area.id,
        description: habitData.description,
        frequency: habitData.frequency,
        target: habitData.target
      })

      if (result.success) {
        // 重新加载习惯数据
        await fetchHabitsForArea(area.id)
      } else {
        console.error('Failed to create habit:', result.error)
      }
    } catch (error) {
      console.error('Error creating habit:', error)
    }
  }

  const handleEditHabit = async (habitData: any) => {
    if (editingHabit) {
      updateHabit(editingHabit.id, {
        ...habitData,
        updatedAt: new Date()
      })
      setEditingHabit(null)
    }
  }

  const handleDeleteHabit = async (habitId: string) => {
    const confirmed = await confirm({
      title: t('pages.areas.detail.habitDeleteConfirmTitle'),
      description: t('pages.areas.detail.habitDeleteConfirmMessage'),
      variant: 'destructive',
      confirmText: t('common.delete'),
      cancelText: t('common.cancel')
    })

    if (confirmed) {
      try {
        await deleteHabitFromStore(habitId)
      } catch (error) {
        console.error('Failed to delete habit:', error)
      }
    }
  }

  // {{ AURA-X: Modify - 使用全局状态管理习惯记录. Approval: 寸止(ID:1738157400). }}
  const handleToggleHabit = async (habitId: string, date: string, completed: boolean) => {
    const dateObj = new Date(date)
    await toggleHabitRecord(habitId, dateObj)
  }

  // {{ AURA-X: Remove - 移除不再使用的清单处理函数. Approval: 寸止(ID:1738157400). }}

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 整合信息卡片 */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              {/* 返回（仅移动端显示） */}
              <Button variant="ghost" size="sm" asChild className="mb-3 -ml-2 md:hidden" aria-label={t('pages.areas.detail.backToAreas')}>
                <Link to="/areas" className="text-muted-foreground hover:text-foreground">
                  <ArrowLeft className="h-4 w-4" />
                  <span className="sr-only">{t('pages.areas.detail.returnToAreasList')}</span>
                </Link>
              </Button>
              {/* 面包屑（桌面端显示，避免与全局重复可按需隐藏） */}
              {/* 桌面端不再显示卡片内面包屑，避免重复 */}

              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-2xl mb-2">{area.name}</CardTitle>

                  {/* 指标芯片行（填充顶部空白） */}
                  <AreaStatsChips
                    stats={{
                      ...stats,
                      // 进行中的清单数量
                      activeChecklistCount: (() => {
                        const { checklists, checklistInstances } = useTaskStore.getState()
                        const areaTemplates = checklists.filter(c => (c as any).areaId === areaId)
                        return checklistInstances.filter(i => areaTemplates.find(t => t.id === i.checklistId) && !i.completedAt).length
                      })()
                    }}
                    habitCompletionToday={habitCompletionToday}
                    kpiStats={kpiStatistics || undefined}
                    // 定期任务统计
                    recurring={recurringTasksRef.current?.getCounts?.()}
                    healthScore={healthScore}
                    className="mb-4"
                  />

                  {/* 领域标准 */}
                  <div className="mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="text-xs">{t('pages.areas.detail.areaStandard')}</Badge>
                      {!isArchived && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setIsStandardEditing(!isStandardEditing)}
                          className="h-6 px-2 text-xs"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          {isStandardEditing ? t('common.cancel') : t('common.edit')}
                        </Button>
                      )}
                    </div>

                    {isStandardEditing ? (
                      <div className="space-y-2">
                        <Textarea
                          value={standardText}
                          onChange={(e) => setStandardText(e.target.value)}
                          placeholder={t('pages.areas.detail.standardPlaceholder')}
                          rows={2}
                          className="resize-none text-sm"
                        />
                        <div className="flex gap-2">
                          <Button size="sm" onClick={handleSaveStandard}>{t('pages.areas.detail.save')}</Button>
                          <Button variant="outline" size="sm" onClick={() => setIsStandardEditing(false)}>
                            {t('pages.areas.detail.cancel')}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">
                        {area.standard || t('pages.areas.detail.noStandardSet')}
                      </p>
                    )}
                  </div>
                </div>

                {/* 健康度已在芯片中展示，此处收敛为空白或保留右侧间距 */}
                <div className="ml-4 hidden md:block" />
              </div>
            </div>

            {/* 工具栏：桌面端显示图标按钮+更多；移动端合并为一个更多菜单 */}
            {!isArchived && (
              <div className="flex items-center gap-2">
                {/* 编辑 */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" aria-label={t('pages.areas.detail.editArea')} onClick={() => setIsEditDialogOpen(true)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">{t('pages.areas.detail.editArea')}</TooltipContent>
                </Tooltip>

                {/* 归档 */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" aria-label={t('pages.areas.detail.archiveArea')} onClick={handleArchiveArea}>
                      <Archive className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">{t('pages.areas.detail.archiveArea')}</TooltipContent>
                </Tooltip>

                {/* 删除 */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700" aria-label={t('pages.areas.detail.deleteArea')} onClick={handleDeleteArea}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">{t('pages.areas.detail.deleteArea')}</TooltipContent>
                </Tooltip>
              </div>
            )}
          </div>

          {/* 简洁模式：移除大块概览，已由芯片行承担可视化 */}

          {/* 关键信息 */}
          <div className="flex items-center gap-6 text-sm text-muted-foreground mt-4 pt-4 border-t">
            <div className="flex items-center gap-2">
              <span>{t('pages.areas.detail.reviewFrequency')}</span>
              <Badge variant="secondary" className="text-xs">
                {area.reviewFrequency ?
                  (area.reviewFrequency === 'Weekly' ? t('pages.areas.detail.weekly') :
                   area.reviewFrequency === 'Monthly' ? t('pages.areas.detail.monthly') :
                   area.reviewFrequency === 'Quarterly' ? t('pages.areas.detail.quarterly') : area.reviewFrequency)
                  : t('pages.areas.detail.weekly')}
              </Badge>
            </div>
            <div>{t('pages.areas.detail.createdAt')} {new Date(area.createdAt).toLocaleDateString()}</div>
            <div>{t('pages.areas.detail.updatedAt')} {new Date(area.updatedAt).toLocaleDateString()}</div>
            <Badge
              variant="outline"
              className={cn('text-xs', getStatusColor(area.status || 'Active'))}
            >
              {area.status === 'Active' ? t('pages.areas.detail.statusActive') :
               area.status === 'Needs Attention' ? t('pages.areas.detail.statusNeedsAttention') :
               area.status === 'On Hold' ? t('pages.areas.detail.statusOnHold') :
               area.status === 'Review Required' ? t('pages.areas.detail.statusReviewRequired') : area.status || t('pages.areas.detail.statusActive')}
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* 归档横幅提示 */}
      {isArchived && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <Archive className="w-5 h-5 text-amber-600" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-amber-800">
                {t('pages.areas.detail.archivedBannerTitle')}
              </h3>
              <p className="text-sm text-amber-700 mt-1">
                {t('pages.areas.detail.archivedBannerMessage')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 两栏布局 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧执行区 (2/3宽度) */}
        <div className="lg:col-span-2 space-y-6">
          {/* 习惯追踪器 */}
          <MiniHabitTracker
            areaId={areaId!}
            onAddHabit={() => setIsCreateHabitDialogOpen(true)}
          />

          {/* 关键指标 */}
          <AreaKPIManagement areaId={areaId!} />

          {/* 定期维护任务 */}
          <RecurringMaintenanceTasks
            ref={recurringTasksRef}
            areaId={areaId!}
            onAddTask={() => setIsCreateRecurringTaskDialogOpen(true)}
            refreshKey={recurringTasksRefreshKey}
          />

          {/* 进行中的清单 */}
          <ActiveChecklists areaId={areaId!} />
        </div>

        {/* 右侧规划与资源区 (1/3宽度) */}
        <div className="space-y-6">
          {/* 关联项目 */}
          <CompactProjectList
            areaId={areaId!}
            onCreateProject={() => setIsCreateProjectDialogOpen(true)}
          />

          {/* 关联资源 */}
          <UnifiedResources
            areaId={areaId!}
            compact={true}
            className="h-fit"
          />

          {/* 清单模板库 */}
          <CompactChecklistTemplates
            areaId={areaId!}
            onCreateTemplate={() => setIsTemplateManagerOpen(true)}
          />
        </div>
      </div>

      {/* Edit Area Dialog */}
      <CreateAreaDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        onSubmit={handleEditArea}
        initialData={area}
      />

      {/* Create/Edit Habit Dialog */}
      <CreateHabitDialog
        isOpen={isCreateHabitDialogOpen || !!editingHabit}
        onClose={() => {
          setIsCreateHabitDialogOpen(false)
          setEditingHabit(null)
        }}
        onSubmit={editingHabit ? handleEditHabit : handleCreateHabit}
        initialData={editingHabit || undefined}
        areaId={area.id}
      />

      {/* {{ AURA-X: Add - 添加创建项目对话框. Approval: 寸止(ID:1738157400). }} */}
      {/* Create Project Dialog */}
      <CreateProjectDialog
        isOpen={isCreateProjectDialogOpen}
        onClose={() => setIsCreateProjectDialogOpen(false)}
        onSubmit={handleCreateProject}
        initialData={{ areaId: areaId }}
      />



      {/* Template Manager Dialog */}
      {isTemplateManagerOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-4 border-b flex items-center justify-between">
              <h2 className="text-lg font-semibold">{t('pages.areas.detail.checklistTemplateManagement')}</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsTemplateManagerOpen(false)}
              >
                ✕
              </Button>
            </div>
            <div className="p-4 overflow-y-auto">
              <ChecklistTemplateManager areaId={areaId!} />
            </div>
          </div>
        </div>
      )}

      {/* Create Recurring Task Dialog */}
      <CreateRecurringTaskDialog
        isOpen={isCreateRecurringTaskDialogOpen}
        onClose={() => setIsCreateRecurringTaskDialogOpen(false)}
        areaId={areaId!}
        onTaskCreated={(task) => {
          console.log('Recurring task created:', task)
          // 直接添加任务到列表
          recurringTasksRef.current?.addTask(task)
        }}
      />

      {/* Confirm Dialog */}
      <ConfirmDialogComponent />
    </div>
  )
}

export default AreaDetailPage
