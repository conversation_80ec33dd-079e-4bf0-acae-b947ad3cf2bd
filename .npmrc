electron_mirror=https://npmmirror.com/mirrors/electron/
electron_builder_binaries_mirror=https://npmmirror.com/mirrors/electron-builder-binaries/
shamefully-hoist=true

# pnpm specific settings
auto-install-peers=true
strict-peer-dependencies=false
prefer-frozen-lockfile=true

# Electron download settings
ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/
ELECTRON_CUSTOM_DIR={{ version }}
ELECTRON_CACHE=./cache/electron

# Additional mirrors for better connectivity
ELECTRON_BUILDER_BINARIES_MIRROR=https://npmmirror.com/mirrors/electron-builder-binaries/
SASS_BINARY_SITE=https://npmmirror.com/mirrors/node-sass/
PHANTOMJS_CDNURL=https://npmmirror.com/mirrors/phantomjs/
CHROMEDRIVER_CDNURL=https://npmmirror.com/mirrors/chromedriver/
