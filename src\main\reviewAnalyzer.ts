import kyselyDatabaseService from './kyselyDatabase'
import type { PrismaClient } from '@prisma/client'

interface AnalysisResult {
  trends: {
    projectCompletion: {
      current: number
      previous: number
      trend: 'up' | 'down' | 'stable'
      change: number
    }
    taskProductivity: {
      current: number
      previous: number
      trend: 'up' | 'down' | 'stable'
      change: number
    }
    habitConsistency: {
      current: number
      previous: number
      trend: 'up' | 'down' | 'stable'
      change: number
    }
  }
  predictions: {
    nextPeriodProjections: {
      expectedProjects: number
      expectedTasks: number
      riskFactors: string[]
    }
    goalAchievementProbability: number
    recommendedFocus: string[]
  }
  recommendations: {
    immediate: string[]
    shortTerm: string[]
    longTerm: string[]
  }
  goalProgress: {
    overallScore: number
    categoryScores: {
      projects: number
      habits: number
      kpis: number
    }
    achievements: string[]
    gaps: string[]
  }
}

export class ReviewAnalyzer {
  private prisma: PrismaClient

  constructor(database: any) {
    this.prisma = database.getClient()
  }

  /**
   * Analyze trends by comparing current period with previous period
   */
  private async analyzeTrends(type: string, currentPeriod: string): Promise<AnalysisResult['trends']> {
    const previousPeriod = this.getPreviousPeriod(type, currentPeriod)
    
    // Get current period data
    const currentData = await this.getAggregatedData(type, currentPeriod)
    const previousData = await this.getAggregatedData(type, previousPeriod)

    const calculateTrend = (current: number, previous: number) => {
      const change = current - previous
      const changePercent = previous > 0 ? (change / previous) * 100 : 0
      
      let trend: 'up' | 'down' | 'stable' = 'stable'
      if (Math.abs(changePercent) > 5) {
        trend = changePercent > 0 ? 'up' : 'down'
      }
      
      return { current, previous, trend, change: Math.round(changePercent * 100) / 100 }
    }

    return {
      projectCompletion: calculateTrend(
        currentData.projects?.completionRate || 0,
        previousData.projects?.completionRate || 0
      ),
      taskProductivity: calculateTrend(
        currentData.tasks?.completionRate || 0,
        previousData.tasks?.completionRate || 0
      ),
      habitConsistency: calculateTrend(
        currentData.areas?.habitCompletionRate || 0,
        previousData.areas?.habitCompletionRate || 0
      )
    }
  }

  /**
   * Generate predictions based on historical data
   */
  private async generatePredictions(type: string, currentPeriod: string): Promise<AnalysisResult['predictions']> {
    // Get historical data for the last 3 periods
    const periods = this.getHistoricalPeriods(type, currentPeriod, 3)
    const historicalData = await Promise.all(
      periods.map(period => this.getAggregatedData(type, period))
    )

    // Calculate averages
    const avgProjects = historicalData.reduce((sum, data) => sum + (data.projects?.completed || 0), 0) / historicalData.length
    const avgTasks = historicalData.reduce((sum, data) => sum + (data.tasks?.completed || 0), 0) / historicalData.length
    const avgHabitRate = historicalData.reduce((sum, data) => sum + (data.areas?.habitCompletionRate || 0), 0) / historicalData.length

    // Identify risk factors
    const riskFactors: string[] = []
    const currentData = historicalData[0] // Most recent data

    if ((currentData.tasks?.overdue || 0) > 5) {
      riskFactors.push('High number of overdue tasks')
    }
    if ((currentData.projects?.completionRate || 0) < 50) {
      riskFactors.push('Low project completion rate')
    }
    if ((currentData.areas?.habitCompletionRate || 0) < 60) {
      riskFactors.push('Declining habit consistency')
    }

    // Calculate goal achievement probability
    const goalAchievementProbability = Math.min(100, Math.max(0, 
      (avgProjects * 0.3 + avgTasks * 0.4 + avgHabitRate * 0.3)
    ))

    // Recommend focus areas
    const recommendedFocus: string[] = []
    if (avgProjects < 2) recommendedFocus.push('Increase project completion')
    if (avgTasks < 10) recommendedFocus.push('Improve task productivity')
    if (avgHabitRate < 70) recommendedFocus.push('Strengthen habit consistency')

    return {
      nextPeriodProjections: {
        expectedProjects: Math.round(avgProjects),
        expectedTasks: Math.round(avgTasks),
        riskFactors
      },
      goalAchievementProbability: Math.round(goalAchievementProbability),
      recommendedFocus
    }
  }

  /**
   * Generate actionable recommendations
   */
  private generateRecommendations(trends: AnalysisResult['trends'], predictions: AnalysisResult['predictions']): AnalysisResult['recommendations'] {
    const immediate: string[] = []
    const shortTerm: string[] = []
    const longTerm: string[] = []

    // Immediate recommendations (this week)
    if (trends.taskProductivity.trend === 'down') {
      immediate.push('Review and prioritize your current task list')
      immediate.push('Identify and eliminate time-wasting activities')
    }
    if (trends.habitConsistency.trend === 'down') {
      immediate.push('Focus on maintaining your most important habits')
      immediate.push('Reduce habit complexity to improve consistency')
    }
    if (predictions.nextPeriodProjections.riskFactors.length > 0) {
      immediate.push('Address overdue tasks to prevent further delays')
    }

    // Short-term recommendations (this month)
    if (trends.projectCompletion.trend === 'down') {
      shortTerm.push('Break down large projects into smaller, manageable tasks')
      shortTerm.push('Set more realistic project timelines')
    }
    if (predictions.goalAchievementProbability < 70) {
      shortTerm.push('Reassess and adjust your goals to be more achievable')
      shortTerm.push('Implement better tracking and accountability systems')
    }
    shortTerm.push('Establish weekly review sessions to stay on track')

    // Long-term recommendations (next quarter)
    if (trends.projectCompletion.current < 60) {
      longTerm.push('Develop better project planning and estimation skills')
      longTerm.push('Consider reducing the number of concurrent projects')
    }
    if (trends.habitConsistency.current < 70) {
      longTerm.push('Build a more sustainable daily routine')
      longTerm.push('Focus on habit stacking and environmental design')
    }
    longTerm.push('Invest in skills that will improve your overall productivity')

    return { immediate, shortTerm, longTerm }
  }

  /**
   * Evaluate goal progress and achievement
   */
  private async evaluateGoalProgress(type: string, currentPeriod: string): Promise<AnalysisResult['goalProgress']> {
    const data = await this.getAggregatedData(type, currentPeriod)

    // Calculate category scores
    const projectScore = Math.min(100, (data.projects?.completionRate || 0))
    const habitScore = Math.min(100, (data.areas?.habitCompletionRate || 0))
    const kpiScore = this.calculateKpiScore(data.areas?.kpiChanges || [])

    const overallScore = Math.round((projectScore + habitScore + kpiScore) / 3)

    // Identify achievements
    const achievements: string[] = []
    if (projectScore > 80) achievements.push('Excellent project completion rate')
    if (habitScore > 80) achievements.push('Outstanding habit consistency')
    if (kpiScore > 80) achievements.push('Strong KPI performance')
    if ((data.tasks?.completed || 0) > 20) achievements.push('High task completion volume')

    // Identify gaps
    const gaps: string[] = []
    if (projectScore < 50) gaps.push('Project completion needs improvement')
    if (habitScore < 50) gaps.push('Habit consistency requires attention')
    if (kpiScore < 50) gaps.push('KPI performance is below expectations')
    if ((data.tasks?.overdue || 0) > 5) gaps.push('Too many overdue tasks')

    return {
      overallScore,
      categoryScores: {
        projects: Math.round(projectScore),
        habits: Math.round(habitScore),
        kpis: Math.round(kpiScore)
      },
      achievements,
      gaps
    }
  }

  /**
   * Calculate KPI score based on changes
   */
  private calculateKpiScore(kpiChanges: any[]): number {
    if (kpiChanges.length === 0) return 50 // Neutral score if no KPIs

    const positiveChanges = kpiChanges.filter(kpi => kpi.change > 0).length
    const totalChanges = kpiChanges.length
    
    return (positiveChanges / totalChanges) * 100
  }

  /**
   * Get previous period string
   */
  private getPreviousPeriod(type: string, currentPeriod: string): string {
    switch (type) {
      case 'daily':
        const date = new Date(currentPeriod)
        date.setDate(date.getDate() - 1)
        return date.toISOString().split('T')[0]
      
      case 'weekly':
        const [year, weekStr] = currentPeriod.split('-W')
        const week = parseInt(weekStr)
        const prevWeek = week > 1 ? week - 1 : 52
        const prevYear = week > 1 ? year : (parseInt(year) - 1).toString()
        return `${prevYear}-W${prevWeek.toString().padStart(2, '0')}`
      
      case 'monthly':
        const [monthYear, month] = currentPeriod.split('-')
        const prevMonth = parseInt(month) > 1 ? parseInt(month) - 1 : 12
        const prevMonthYear = parseInt(month) > 1 ? monthYear : (parseInt(monthYear) - 1).toString()
        return `${prevMonthYear}-${prevMonth.toString().padStart(2, '0')}`
      
      case 'quarterly':
        const [qYear, quarterStr] = currentPeriod.split('-Q')
        const quarter = parseInt(quarterStr)
        const prevQuarter = quarter > 1 ? quarter - 1 : 4
        const prevQYear = quarter > 1 ? qYear : (parseInt(qYear) - 1).toString()
        return `${prevQYear}-Q${prevQuarter}`
      
      case 'yearly':
        return (parseInt(currentPeriod) - 1).toString()
      
      default:
        return currentPeriod
    }
  }

  /**
   * Get historical periods
   */
  private getHistoricalPeriods(type: string, currentPeriod: string, count: number): string[] {
    const periods = [currentPeriod]
    let period = currentPeriod
    
    for (let i = 1; i < count; i++) {
      period = this.getPreviousPeriod(type, period)
      periods.push(period)
    }
    
    return periods
  }

  /**
   * Get aggregated data for a specific period (placeholder - would use ReviewDataAggregator)
   */
  private async getAggregatedData(type: string, period: string): Promise<any> {
    // This would typically use the ReviewDataAggregator
    // For now, return mock data structure
    return {
      projects: { completionRate: 75, completed: 2 },
      tasks: { completionRate: 80, completed: 15, overdue: 3 },
      areas: { habitCompletionRate: 70, kpiChanges: [] }
    }
  }

  /**
   * Perform complete analysis for a review
   */
  async analyzeReview(type: string, period: string): Promise<AnalysisResult> {
    const [trends, predictions, goalProgress] = await Promise.all([
      this.analyzeTrends(type, period),
      this.generatePredictions(type, period),
      this.evaluateGoalProgress(type, period)
    ])

    const recommendations = this.generateRecommendations(trends, predictions)

    return {
      trends,
      predictions,
      recommendations,
      goalProgress
    }
  }
}

export default ReviewAnalyzer
