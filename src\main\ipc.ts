import { ipcMain, dialog, app, BrowserWindow } from 'electron'
import kyselyDatabaseService from './kyselyDatabase'
import fileSystemService from './fileSystem'
import fileWatcherService from './fileWatcher'
import { registerGlobalShortcut, unregisterGlobalShortcut, unregisterAllGlobalShortcuts } from './index'
import { setWorkspaceDirectory as setKyselyWorkspaceDirectory } from './kyselyClient'
import { configManager } from './configManager'
import { IPC_CHANNELS } from '../shared/ipcTypes'
import fs from 'fs'
import path from 'path'

class IpcHandler {
  private isInitialized = false

  /**
   * Initialize IPC handlers
   */
  initialize(): void {
    if (this.isInitialized) {
      return
    }

    this.setupDatabaseHandlers()
    this.setupFileSystemHandlers()
    this.setupAppHandlers()
    this.setupWindowHandlers()
    this.setupEventForwarding()
    this.setupEmergencyHandlers()

    this.isInitialized = true
    console.log('IPC handlers initialized')
  }

  /**
   * Setup emergency handlers for database initialization
   */
  private setupEmergencyHandlers(): void {
    // Emergency database initialization
    ipcMain.handle('emergency-init-database', async () => {
      try {
        console.log('🚨 [IPC] Emergency database initialization requested')

        // Try to get workspace directory from app settings
        const userDataPath = app.getPath('userData')
        console.log('🚨 [IPC] Using userData path:', userDataPath)

        kyselyDatabaseService.setWorkspaceDirectory(userDataPath)
        const result = await kyselyDatabaseService.initialize()

        console.log('🚨 [IPC] Emergency database initialization result:', result)
        return result
      } catch (error) {
        console.error('🚨 [IPC] Emergency database initialization failed:', error)
        return { success: false, error: error.message }
      }
    })
  }

  /**
   * Setup database operation handlers
   */
  private setupDatabaseHandlers(): void {
    // Database initialization
    ipcMain.handle(IPC_CHANNELS.DB_INITIALIZE, async () => {
      try {
        return await kyselyDatabaseService.initialize()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Database initialization failed'
        }
      }
    })

    // Database status
    ipcMain.handle(IPC_CHANNELS.DB_GET_STATUS, async () => {
      try {
        return await kyselyDatabaseService.getStatus()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get database status'
        }
      }
    })

    // Projects
    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECTS, async () => {
      try {
        return await kyselyDatabaseService.getProjects()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get projects'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_BY_ID, async (_, id, includeArchived) => {
      try {
        return await kyselyDatabaseService.getProjectById(id, includeArchived)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_ARCHIVE_PROJECT, async (_, id) => {
      try {
        return await kyselyDatabaseService.archiveProject(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to archive project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_ARCHIVED_PROJECTS, async (_) => {
      try {
        return await kyselyDatabaseService.getArchivedProjects()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get archived projects'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_ARCHIVED_AREAS, async (_) => {
      try {
        return await kyselyDatabaseService.getArchivedAreas()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get archived areas'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_RESTORE_PROJECT, async (_, id) => {
      try {
        return await kyselyDatabaseService.restoreProject(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to restore project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_CREATE_PROJECT, async (_, data) => {
      try {
        return await kyselyDatabaseService.createProject(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_PROJECT, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateProject(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_PROJECT, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteProject(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete project'
        }
      }
    })

    // Areas
    ipcMain.handle(IPC_CHANNELS.DB_GET_AREAS, async () => {
      try {
        return await kyselyDatabaseService.getAreas()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get areas'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_CREATE_AREA, async (_, data) => {
      try {
        return await kyselyDatabaseService.createArea(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create area'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_AREA, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateArea(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update area'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_AREA, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteArea(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete area'
        }
      }
    })

    // Tasks
    ipcMain.handle(IPC_CHANNELS.DB_GET_TASKS, async (_, filters) => {
      try {
        return await kyselyDatabaseService.getTasks(filters)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get tasks'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_CREATE_TASK, async (_, data) => {
      try {
        return await kyselyDatabaseService.createTask(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create task'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_TASK, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateTask(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update task'
        }
      }
    })

    // Habits
    ipcMain.handle(IPC_CHANNELS.DB_GET_HABITS_BY_AREA, async (_, areaId) => {
      try {
        return await kyselyDatabaseService.getHabitsByArea(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get habits'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_CREATE_HABIT, async (_, data) => {
      try {
        return await kyselyDatabaseService.createHabit(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create habit'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_HABIT, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateHabit(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update habit'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_HABIT, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteHabit(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete habit'
        }
      }
    })

    // Habit Records
    ipcMain.handle(IPC_CHANNELS.DB_GET_HABIT_RECORDS, async (_, habitId) => {
      try {
        return await kyselyDatabaseService.getHabitRecords(habitId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get habit records'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_CREATE_HABIT_RECORD, async (_, data) => {
      try {
        return await kyselyDatabaseService.createHabitRecord(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create habit record'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_HABIT_RECORD, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateHabitRecord(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update habit record'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_HABIT_RECORD, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteHabitRecord(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete habit record'
        }
      }
    })

    // Recurring Tasks
    ipcMain.handle(IPC_CHANNELS.DB_GET_RECURRING_TASKS, async (_, areaId) => {
      try {
        return await kyselyDatabaseService.getRecurringTasks(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get recurring tasks'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_CREATE_RECURRING_TASK, async (_, data) => {
      try {
        return await kyselyDatabaseService.createRecurringTask(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create recurring task'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_RECURRING_TASK, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateRecurringTask(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update recurring task'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_RECURRING_TASK, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteRecurringTask(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete recurring task'
        }
      }
    })

    // Deliverables
    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_DELIVERABLES, async (_, projectId) => {
      try {
        return await kyselyDatabaseService.getProjectDeliverables(projectId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get deliverables'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_CREATE_DELIVERABLE, async (_, data) => {
      try {
        return await kyselyDatabaseService.createDeliverable(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create deliverable'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_DELIVERABLE, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateDeliverable(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update deliverable'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_DELIVERABLE, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteDeliverable(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete deliverable'
        }
      }
    })

    // Reviews
    ipcMain.handle(IPC_CHANNELS.DB_GET_REVIEWS, async (_, data) => {
      try {
        return await kyselyDatabaseService.getReviews(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get reviews'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_CREATE_REVIEW, async (_, data) => {
      try {
        return await kyselyDatabaseService.createReview(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create review'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_REVIEW, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateReview(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update review'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_REVIEW, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteReview(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete review'
        }
      }
    })

    // Review Templates
    ipcMain.handle(IPC_CHANNELS.DB_GET_REVIEW_TEMPLATES, async () => {
      try {
        return await kyselyDatabaseService.getReviewTemplates()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get review templates'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_CREATE_REVIEW_TEMPLATE, async (_, data) => {
      try {
        return await kyselyDatabaseService.createReviewTemplate(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create review template'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_REVIEW_TEMPLATE, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateReviewTemplate(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update review template'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_REVIEW_TEMPLATE, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteReviewTemplate(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete review template'
        }
      }
    })

    // Settings
    ipcMain.handle(IPC_CHANNELS.SETTINGS_GET_USER_SETTINGS, async () => {
      try {
        return await kyselyDatabaseService.getUserSettings()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get user settings'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.SETTINGS_UPDATE_USER_SETTINGS, async (_, settings) => {
      try {
        return await kyselyDatabaseService.updateUserSettings(settings)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update user settings'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.SETTINGS_GET_DATABASE_INFO, async () => {
      try {
        return await kyselyDatabaseService.getDatabaseInfo()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get database info'
        }
      }
    })

    // 移除了资源路径选择和更新功能，避免数据丢失风险
    // 工作目录在首次设置后不允许更改

    // App - 退出确认响应
    ipcMain.handle('app-exit-action', async (_, action: 'exit' | 'minimize' | 'cancel') => {
      console.log('🔧 [IPC] Exit action received:', action)

      const mainWindow = BrowserWindow.getAllWindows()[0]
      if (!mainWindow) {
        console.error('❌ [IPC] No main window found')
        return { success: false, error: 'No main window found' }
      }

      try {
        switch (action) {
          case 'exit':
            console.log('🚪 [IPC] Exiting application')
            // 设置强制退出标志并退出
            (mainWindow as any).forceQuit = true
            app.quit()
            return { success: true }

          case 'minimize':
            console.log('🔽 [IPC] Minimizing to tray')
            const beforeState = {
              visible: mainWindow.isVisible(),
              minimized: mainWindow.isMinimized(),
              maximized: mainWindow.isMaximized(),
              focused: mainWindow.isFocused()
            }
            console.log('🔽 [IPC] Window state before hide:', JSON.stringify(beforeState))

            // 添加hide事件监听器
            const hideListener = () => {
              console.log('✅ [IPC] Hide event was triggered')
            }
            mainWindow.once('hide', hideListener)

            // 隐藏窗口到托盘
            console.log('🔽 [IPC] Calling mainWindow.hide()')
            mainWindow.hide()

            // 验证隐藏是否成功
            setTimeout(() => {
              const afterState = {
                visible: mainWindow.isVisible(),
                minimized: mainWindow.isMinimized()
              }
              console.log('🔽 [IPC] Window state after hide:', JSON.stringify(afterState))

              if (mainWindow.isVisible()) {
                console.warn('⚠️ [IPC] PROBLEM: Window is still visible after hide() call!')
                console.log('🔧 [IPC] This indicates the hide() method is not working properly')

                // 尝试其他隐藏方法
                try {
                  console.log('🔧 [IPC] Trying setSkipTaskbar + minimize + hide')
                  if (typeof mainWindow.setSkipTaskbar === 'function') {
                    mainWindow.setSkipTaskbar(true)
                  }
                  mainWindow.minimize()
                  setTimeout(() => {
                    mainWindow.hide()
                    console.log('🔧 [IPC] Alternative hide method completed')
                  }, 100)
                } catch (error) {
                  console.error('❌ [IPC] Alternative hide method failed:', error)
                }
              } else {
                console.log('✅ [IPC] Window successfully hidden (isVisible = false)')
                console.log('🔧 [IPC] If user still sees window, there may be a display/OS issue')
              }
            }, 100)

            return { success: true }

          case 'cancel':
            console.log('❌ [IPC] Exit cancelled')
            // 什么都不做，只是取消
            return { success: true }

          default:
            console.error('❌ [IPC] Invalid action:', action)
            return { success: false, error: 'Invalid action' }
        }
      } catch (error) {
        console.error('❌ [IPC] Error handling exit action:', error)
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
      }
    })

    // Area Metrics
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_AREA_METRIC, async (_, data) => {
      try {
        return await kyselyDatabaseService.createAreaMetric(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create area metric'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_AREA_METRICS, async (_, areaId) => {
      try {
        return await kyselyDatabaseService.getAreaMetrics(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get area metrics'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_AREA_METRIC, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateAreaMetric(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update area metric'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_AREA_METRIC, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteAreaMetric(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete area metric'
        }
      }
    })

    // Area Metric Records
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_AREA_METRIC_RECORD, async (_, data) => {
      try {
        return await kyselyDatabaseService.createAreaMetricRecord(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create area metric record'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_AREA_METRIC_RECORDS, async (_, metricId, limit) => {
      try {
        return await kyselyDatabaseService.getAreaMetricRecords(metricId, limit)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get area metric records'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_AREA_METRIC_RECORD, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateAreaMetricRecord(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update area metric record'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_AREA_METRIC_RECORD, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteAreaMetricRecord(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete area metric record'
        }
      }
    })

    // Area Resources
    ipcMain.handle(IPC_CHANNELS.DB_GET_AREA_RESOURCES, async (_, areaId) => {
      try {
        return await kyselyDatabaseService.getAreaResources(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get area resources'
        }
      }
    })

    // Project KPIs
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_PROJECT_KPI, async (_, data) => {
      try {
        return await kyselyDatabaseService.createProjectKPI(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create project KPI'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_KPIS, async (_, projectId) => {
      try {
        return await kyselyDatabaseService.getProjectKPIs(projectId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get project KPIs'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_PROJECT_KPI, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateProjectKPI(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update project KPI'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_PROJECT_KPI, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteProjectKPI(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete project KPI'
        }
      }
    })

    // KPI Records
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_KPI_RECORD, async (_, data) => {
      try {
        return await kyselyDatabaseService.createKPIRecord(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create KPI record'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_KPI_RECORDS, async (_, kpiId, limit) => {
      try {
        return await kyselyDatabaseService.getKPIRecords(kpiId, limit)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get KPI records'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_KPI_RECORD, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateKPIRecord(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update KPI record'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_KPI_RECORD, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteKPIRecord(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete KPI record'
        }
      }
    })

    // Checklist operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_CHECKLIST, async (_, data) => {
      try {
        return await kyselyDatabaseService.createChecklist(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create checklist'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_CHECKLISTS, async (_, areaId) => {
      try {
        return await kyselyDatabaseService.getChecklists(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get checklists'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_CHECKLIST, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateChecklist(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update checklist'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_CHECKLIST, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteChecklist(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete checklist'
        }
      }
    })

    // Checklist Instance operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_CHECKLIST_INSTANCE, async (_, data) => {
      try {
        return await kyselyDatabaseService.createChecklistInstance(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create checklist instance'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_CHECKLIST_INSTANCES, async (_, areaId) => {
      try {
        return await kyselyDatabaseService.getChecklistInstances(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get checklist instances'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_CHECKLIST_INSTANCE, async (_, data) => {
      try {
        return await kyselyDatabaseService.updateChecklistInstance(data.id, data.updates)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update checklist instance'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_CHECKLIST_INSTANCE, async (_, id) => {
      try {
        return await kyselyDatabaseService.deleteChecklistInstance(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete checklist instance'
        }
      }
    })

    // Resources
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_RESOURCE, async (_, data) => {
      try {
        return await kyselyDatabaseService.createResource(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create resource'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_RESOURCES, async (_, filters) => {
      try {
        return await kyselyDatabaseService.getResources(filters)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get resources'
        }
      }
    })

    // Project Resources
    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_RESOURCES, async (_, projectId) => {
      try {
        return await kyselyDatabaseService.getProjectResources(projectId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get project resources'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_LINK_RESOURCE_TO_PROJECT, async (_, data) => {
      try {
        return await kyselyDatabaseService.linkResourceToProject(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to link resource to project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UNLINK_RESOURCE_FROM_PROJECT, async (_, resourceId, projectId) => {
      try {
        return await kyselyDatabaseService.unlinkResourceFromProject(resourceId, projectId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to unlink resource from project'
        }
      }
    })
  }

  /**
   * Setup file system operation handlers
   */
  private setupFileSystemHandlers(): void {
    // File system initialization
    ipcMain.handle(IPC_CHANNELS.FS_INITIALIZE, async () => {
      try {
        return await fileSystemService.initialize()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'File system initialization failed'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_REINITIALIZE, async (_, workspaceDirectory) => {
      try {
        return await fileSystemService.initialize(workspaceDirectory)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'File system re-initialization failed'
        }
      }
    })

    // File operations
    ipcMain.handle(IPC_CHANNELS.FS_READ_FILE, async (_, data) => {
      try {
        return await fileSystemService.readFile(data.path, { encoding: data.encoding })
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to read file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_WRITE_FILE, async (_, data) => {
      try {
        return await fileSystemService.writeFile(data.path, data.content, {
          encoding: data.encoding,
          createDirs: data.createDirs,
          backup: data.backup
        })
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to write file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_DELETE_FILE, async (_, path) => {
      try {
        return await fileSystemService.deleteFile(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_MOVE_FILE, async (_, data) => {
      try {
        return await fileSystemService.moveFile(data.sourcePath, data.targetPath, {
          overwrite: data.overwrite,
          createDirs: data.createDirs
        })
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to move file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_COPY_FILE, async (_, data) => {
      try {
        return await fileSystemService.copyFile(data.sourcePath, data.targetPath, {
          overwrite: data.overwrite,
          createDirs: data.createDirs
        })
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to copy file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_FILE_EXISTS, async (_, path) => {
      try {
        return await fileSystemService.fileExists(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to check file existence'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_GET_FILE_INFO, async (_, path) => {
      try {
        return await fileSystemService.getFileInfo(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get file info'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_LIST_DIRECTORY, async (_, path) => {
      try {
        return await fileSystemService.listDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to list directory'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_CREATE_DIRECTORY, async (_, path) => {
      try {
        return await fileSystemService.createDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create directory'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_DELETE_DIRECTORY, async (_, path) => {
      try {
        return await fileSystemService.deleteDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete directory'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_RENAME, async (_, oldPath, newPath) => {
      try {
        return await fileSystemService.rename(oldPath, newPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to rename file or directory'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_WATCH_DIRECTORY, async (_, path) => {
      try {
        return await fileWatcherService.watchDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to watch directory'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_UNWATCH_DIRECTORY, async (_, path) => {
      try {
        return await fileWatcherService.unwatchDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to unwatch directory'
        }
      }
    })
  }

  /**
   * Setup application operation handlers
   */
  private setupAppHandlers(): void {
    ipcMain.handle(IPC_CHANNELS.APP_GET_VERSION, async () => {
      return app.getVersion()
    })

    ipcMain.handle(IPC_CHANNELS.APP_GET_PATH, async (_, name) => {
      return app.getPath(name as any)
    })

    ipcMain.handle(IPC_CHANNELS.APP_SHOW_MESSAGE_BOX, async (_, options) => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        return await dialog.showMessageBox(focusedWindow, options)
      } else {
        return await dialog.showMessageBox(options)
      }
    })

    ipcMain.handle(IPC_CHANNELS.APP_SHOW_ERROR_BOX, async (_, title, content) => {
      dialog.showErrorBox(title, content)
    })

    ipcMain.handle(IPC_CHANNELS.APP_SHOW_OPEN_DIALOG, async (_, options) => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        return await dialog.showOpenDialog(focusedWindow, options)
      } else {
        return await dialog.showOpenDialog(options)
      }
    })

    ipcMain.handle(IPC_CHANNELS.APP_SHOW_SAVE_DIALOG, async (_, options) => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        return await dialog.showSaveDialog(focusedWindow, options)
      } else {
        return await dialog.showSaveDialog(options)
      }
    })

    // Exit preference handlers
    ipcMain.handle(IPC_CHANNELS.APP_GET_EXIT_PREFERENCE, async () => {
      try {
        // Reuse helper from main/index via dynamic import to avoid circular deps
        const { getPath } = app
        const fs = await import('fs/promises')
        const path = await import('path')
        const file = path.join(getPath('userData'), 'exit-preference.json')
        try {
          const data = await fs.readFile(file, 'utf-8')
          const pref = JSON.parse(data)
          if (pref && (pref.action === 'minimize' || pref.action === 'exit') && typeof pref.remember === 'boolean') {
            return { success: true, data: pref }
          }
        } catch {}
        return { success: true, data: null }
      } catch (error) {
        return { success: false, error: (error as any)?.message || 'Failed to get preference' }
      }
    })

    ipcMain.handle(IPC_CHANNELS.APP_SET_EXIT_PREFERENCE, async (_e, pref: { mode: 'ask' | 'minimize' | 'exit' }) => {
      try {
        const fs = await import('fs/promises')
        const path = await import('path')
        const file = path.join(app.getPath('userData'), 'exit-preference.json')
        if (pref.mode === 'ask') {
          // Remove file to go back to asking
          try { await fs.unlink(file) } catch {}
          return { success: true }
        }
        const data = { remember: true, action: pref.mode }
        await fs.writeFile(file, JSON.stringify(data), 'utf-8')
        return { success: true }
      } catch (error) {
        return { success: false, error: (error as any)?.message || 'Failed to set preference' }
      }
    })

    ipcMain.handle(IPC_CHANNELS.APP_CLEAR_EXIT_PREFERENCE, async () => {
      try {
        const fs = await import('fs/promises')
        const path = await import('path')
        const file = path.join(app.getPath('userData'), 'exit-preference.json')
        try { await fs.unlink(file) } catch {}
        return { success: true }
      } catch (error) {
        return { success: false, error: (error as any)?.message || 'Failed to clear preference' }
      }
    })

    // Global shortcuts
    ipcMain.handle(IPC_CHANNELS.GLOBAL_SHORTCUT_REGISTER, async (_, accelerator, action) => {
      try {
        return registerGlobalShortcut(accelerator, action)
      } catch (error) {
        console.error('Error in global shortcut register handler:', error)
        return false
      }
    })

    ipcMain.handle(IPC_CHANNELS.GLOBAL_SHORTCUT_UNREGISTER, async (_, accelerator) => {
      try {
        return unregisterGlobalShortcut(accelerator)
      } catch (error) {
        console.error('Error in global shortcut unregister handler:', error)
        return false
      }
    })

    ipcMain.handle(IPC_CHANNELS.GLOBAL_SHORTCUT_UNREGISTER_ALL, async () => {
      try {
        unregisterAllGlobalShortcuts()
      } catch (error) {
        console.error('Error in global shortcut unregister all handler:', error)
      }
    })

    // App control handlers for exit confirmation
    ipcMain.handle(IPC_CHANNELS.APP_FORCE_QUIT, async () => {
      console.log('Force quit requested')
      try {
        await this.performAppCleanup()

        // Close all windows first
        BrowserWindow.getAllWindows().forEach(window => {
          if (!window.isDestroyed()) {
            window.destroy()
          }
        })

        app.quit()

        // Force exit if app.quit() doesn't work within 2 seconds
        setTimeout(() => {
          console.log('Force exiting process...')
          process.exit(0)
        }, 2000)
      } catch (error) {
        console.error('Error during force quit:', error)
        process.exit(1)
      }
    })

    ipcMain.handle(IPC_CHANNELS.APP_CANCEL_QUIT, () => {
      console.log('Quit cancelled')
      // Just acknowledge the cancellation, no action needed
      return true
    })

    // Database configuration (deprecated - only for compatibility)
    ipcMain.handle(IPC_CHANNELS.DB_SET_WORKSPACE_DIRECTORY, async (_, directory: string) => {
      console.log('⚠️ DB_SET_WORKSPACE_DIRECTORY is deprecated, use DB_INITIALIZE_WITH_WORKSPACE instead')
      return { success: false, error: 'This method is deprecated' }
    })

    // Initialize database with workspace directory (for first time startup)
    ipcMain.handle(IPC_CHANNELS.DB_INITIALIZE_WITH_WORKSPACE, async (_, directory: string) => {
      console.log('🆕 [IPC] Initializing database for first time with workspace directory:', directory)
      try {
        // 使用配置管理器保存配置
        const configSaved = configManager.setWorkspaceDirectory(directory)
        if (!configSaved) {
          throw new Error('Failed to save workspace directory to config')
        }

        // 设置数据库工作目录
        await kyselyDatabaseService.setWorkspaceDirectory(directory)
        setKyselyWorkspaceDirectory(directory)

        // 初始化数据库
        const dbResult = await kyselyDatabaseService.initialize()
        if (!dbResult.success) {
          throw new Error(dbResult.error)
        }

        console.log('✅ [IPC] Database initialized successfully for first time startup')
        return { success: true, message: 'Database initialized successfully' }
      } catch (error) {
        console.error('❌ [IPC] Failed to initialize database for first time startup:', error)
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
      }
    })

    // Complete first time setup (save user config)
    ipcMain.handle('config:complete-first-time-setup', async (_, data: { username: string, workspaceDirectory: string }) => {
      console.log('🎉 [IPC] Completing first time setup:', data)
      try {
        const success = configManager.completeFirstTimeSetup(data.username, data.workspaceDirectory)
        if (!success) {
          throw new Error('Failed to save first time setup configuration')
        }

        console.log('✅ [IPC] First time setup completed successfully')
        return { success: true, message: 'First time setup completed' }
      } catch (error) {
        console.error('❌ [IPC] Failed to complete first time setup:', error)
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
      }
    })

    // Get configuration status
    ipcMain.handle('config:get-status', async () => {
      try {
        const isFirstTime = configManager.isFirstTimeStartup()
        const config = configManager.getCurrentConfig()

        console.log('📄 [IPC] Config status requested - isFirstTime:', isFirstTime)

        return {
          success: true,
          data: {
            isFirstTime,
            hasConfig: !!config,
            workspaceDirectory: config?.workspaceDirectory || null,
            username: config?.username || null
          }
        }
      } catch (error) {
        console.error('❌ [IPC] Failed to get config status:', error)
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
      }
    })
  }

  /**
   * Setup window operation handlers
   */
  private setupWindowHandlers(): void {
    ipcMain.handle(IPC_CHANNELS.WINDOW_MINIMIZE, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        focusedWindow.minimize()
      }
    })

    ipcMain.handle(IPC_CHANNELS.WINDOW_MAXIMIZE, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        if (focusedWindow.isMaximized()) {
          focusedWindow.unmaximize()
        } else {
          focusedWindow.maximize()
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.WINDOW_CLOSE, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        focusedWindow.close()
      }
    })

    ipcMain.handle(IPC_CHANNELS.WINDOW_IS_MAXIMIZED, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        return focusedWindow.isMaximized()
      }
      return false
    })

    ipcMain.handle(IPC_CHANNELS.WINDOW_TOGGLE_DEVTOOLS, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        focusedWindow.webContents.toggleDevTools()
      }
    })
  }

  /**
   * Setup event forwarding
   */
  private setupEventForwarding(): void {
    // Forward file system events to all renderer processes
    fileWatcherService.on('file-system-event', (event) => {
      BrowserWindow.getAllWindows().forEach((window) => {
        window.webContents.send('file-system-event', event)
      })
    })
  }

  /**
   * Perform application cleanup
   */
  private async performAppCleanup(): Promise<void> {
    try {
      console.log('Starting application cleanup...')
      console.log('Application cleanup completed')
    } catch (error) {
      console.error('Error during application cleanup:', error)
    }
  }

  /**
   * Remove all IPC handlers
   */
  cleanup(): void {
    if (!this.isInitialized) {
      return
    }

    // Remove all IPC handlers
    ipcMain.removeAllListeners()

    // Perform cleanup
    this.performAppCleanup()

    this.isInitialized = false
    console.log('IPC handlers cleaned up')
  }
}

// Export singleton instance
export const ipcHandler = new IpcHandler()
export default ipcHandler
