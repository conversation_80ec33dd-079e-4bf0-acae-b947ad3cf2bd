import { useState, useEffect, useRef, useMemo } from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import {
  X,
  Calendar,
  Clock,
  Flag,
  User,
  Tag,
  FileText,
  Save,
  Trash2,
  Plus,
  Minus,
  Play,
  Pause,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { cn } from '../../lib/utils'
import { usePanelHeight, usePanelPosition } from '../../hooks/useWindowSize'
import { useTaskStore } from '../../store/taskStore'
import { useLanguage } from '../../contexts/LanguageContext'
import type { ExtendedTask } from '../../store/taskStore'
import type { Project, Area } from '../../../../shared/types'

interface TaskDetailPanelProps {
  task: ExtendedTask | null
  isOpen: boolean
  onClose: () => void
  onSave: (taskData: Partial<ExtendedTask>) => void
  onDelete?: (taskId: string) => void
  onAddSubtask?: (parentId: string) => void
  projects?: Project[]
  areas?: Area[]
  className?: string
}

export function TaskDetailPanel({
  task,
  isOpen,
  onClose,
  onSave,
  onDelete,
  onAddSubtask,
  projects = [],
  areas = [],
  className
}: TaskDetailPanelProps) {
  const { t } = useLanguage()
  const [formData, setFormData] = useState<Partial<ExtendedTask>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSelectOpen, setIsSelectOpen] = useState(false) // 跟踪下拉菜单状态
  const panelRef = useRef<HTMLDivElement>(null)

  // 自适应高度计算
  const { panelHeight, availableHeight } = usePanelHeight({
    topOffset: 60, // 考虑顶部导航栏
    bottomOffset: 40, // 底部留白
    minHeight: 400, // 最小高度确保基本内容可见
    maxHeight: 1000 // 最大高度限制
  })

  // 面板位置计算
  const panelPosition = usePanelPosition(panelHeight, {
    rightOffset: 16,
    preferredTop: 80
  })

  // 计算内容区域的最大高度
  const contentMaxHeight = useMemo(() => {
    // 头部高度约 80px，底部按钮区域约 100px，留一些缓冲
    const headerHeight = 60
    const footerHeight = 80
    const padding = 20
    const calculatedHeight = panelHeight - headerHeight - footerHeight - padding

    // 确保最小高度
    return Math.max(200, calculatedHeight)
  }, [panelHeight])

  // 窗口大小变化时重新计算面板位置
  useEffect(() => {
    if (isOpen && panelRef.current) {
      // 确保面板在窗口大小变化后仍然完全可见
      const rect = panelRef.current.getBoundingClientRect()
      const windowHeight = window.innerHeight

      // 如果面板底部超出了窗口，调整位置
      if (rect.bottom > windowHeight) {
        const newTop = Math.max(16, windowHeight - rect.height - 16)
        panelRef.current.style.top = `${newTop}px`
      }
    }
  }, [isOpen, panelHeight, availableHeight])

  // 调试信息（开发环境）
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && isOpen) {
      console.log('TaskDetailPanel Height Info:', {
        panelHeight,
        contentMaxHeight,
        availableHeight,
        panelPosition
      })
    }
  }, [panelHeight, contentMaxHeight, availableHeight, panelPosition, isOpen])

  // 初始化表单数据
  useEffect(() => {
    if (task) {
      setFormData({
        content: task.content || '',
        description: task.description || '',
        priority: task.priority || 'none',
        status: task.status || 'todo',
        deadline: task.deadline ? new Date(task.deadline).toISOString().split('T')[0] : '',
        estimatedHours: task.estimatedHours || 0,
        actualHours: task.actualHours || 0,
        progress: task.progress || 0,
        projectId: task.projectId || 'none',
        areaId: task.areaId || 'none',
        blockedBy: task.blockedBy || ''
      })
    }
  }, [task])

  // 点击外部关闭面板
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node

      // 如果有下拉菜单打开，不关闭面板
      if (isSelectOpen) {
        return
      }

      // 检查是否点击在面板内
      if (panelRef.current && !panelRef.current.contains(target)) {
        // 检查是否点击在下拉菜单或其他弹出组件内
        const isClickInDropdown = target instanceof Element && (
          target.closest('[data-radix-popper-content-wrapper]') ||
          target.closest('[data-radix-select-content]') ||
          target.closest('[data-radix-dropdown-menu-content]') ||
          target.closest('[role="listbox"]') ||
          target.closest('[role="menu"]') ||
          target.closest('[role="dialog"]') ||
          target.closest('[data-state="open"]') // 添加对 Radix 组件状态的检查
        )

        // 只有在不是点击下拉菜单时才关闭面板
        if (!isClickInDropdown) {
          onClose()
        }
      }
    }

    if (isOpen) {
      // 延迟添加事件监听器，避免立即触发
      const timeoutId = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside, true)
      }, 100)

      return () => {
        clearTimeout(timeoutId)
        document.removeEventListener('mousedown', handleClickOutside, true)
      }
    }
  }, [isOpen, onClose, isSelectOpen])

  // ESC 键关闭面板
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscKey)
    return () => document.removeEventListener('keydown', handleEscKey)
  }, [isOpen, onClose])

  const handleSave = async () => {
    if (!task || !formData.content?.trim()) return

    setIsSubmitting(true)
    try {
      const updates = {
        ...formData,
        content: formData.content!.trim(),
        description: formData.description?.trim() || null,
        priority: formData.priority === 'none' ? null : formData.priority,
        deadline: formData.deadline ? new Date(formData.deadline) : null,
        projectId: formData.projectId === 'none' ? null : formData.projectId,
        areaId: formData.areaId === 'none' ? null : formData.areaId,
        updatedAt: new Date()
      }
      
      await onSave(updates)
      onClose()
    } catch (error) {
      console.error('Failed to save task:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = () => {
    if (task && onDelete) {
      onDelete(task.id)
      onClose()
    }
  }

  const handleAddSubtask = () => {
    if (task && onAddSubtask) {
      onAddSubtask(task.id)
    }
  }

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200'
    }
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'blocked':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'review':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'done':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200'
    }
  }

  if (!task) return null

  return (
    <>
      {/* 背景遮罩 - 调整为不覆盖整个屏幕 */}
      <div
        className={cn(
          'fixed inset-0 bg-black/10 backdrop-blur-sm z-40 transition-opacity duration-300',
          isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        )}
        onClick={onClose}
      />

      {/* 滑出面板 */}
      <div
        ref={panelRef}
        className={cn(
          'fixed bg-background border border-border rounded-lg shadow-2xl z-50 transform transition-transform duration-300 ease-in-out overflow-hidden flex flex-col',
          // 响应式宽度：移动端占大部分，桌面端固定宽度
          'w-[calc(100vw-2rem)] sm:w-[400px] md:w-[450px]',
          isOpen ? 'translate-x-0' : 'translate-x-full',
          className
        )}
        style={{
          top: `${panelPosition.top}px`,
          right: `${panelPosition.right}px`,
          height: `${panelPosition.height}px`,
          maxHeight: `${availableHeight}px`
        }}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b bg-muted/30">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-muted-foreground" />
              <h2 className="text-lg font-semibold">{t('pages.projects.detail.tasks.taskDetails')}</h2>
            </div>
            {formData.priority && formData.priority !== 'none' && (
              <Badge className={cn("text-xs", getPriorityColor(formData.priority))}>
                {t(`pages.projects.detail.tasks.priority${formData.priority.charAt(0).toUpperCase() + formData.priority.slice(1)}`)}
              </Badge>
            )}
            {formData.status && formData.status !== 'todo' && (
              <Badge className={cn("text-xs", getStatusColor(formData.status))}>
                {formData.status?.replace('_', ' ')}
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* 内容区域 - 可滚动，动态高度确保底部按钮可见 */}
        <div
          className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 min-h-0"
          style={{ maxHeight: `${contentMaxHeight}px` }}
        >
          <div className="p-6 space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.taskTitle')}</label>
              <Input
                value={formData.content || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                placeholder={t('pages.projects.detail.tasks.enterTaskTitle')}
                className="w-full"
              />
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.taskDescription')}</label>
              <Textarea
                value={formData.description || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder={t('pages.projects.detail.tasks.addTaskDescription')}
                className="w-full min-h-[100px] resize-none"
              />
            </div>
          </div>

          {/* 状态和优先级 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.priority')}</label>
              <Select
                value={formData.priority || 'none'}
                onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
                onOpenChange={setIsSelectOpen}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">{t('pages.projects.detail.tasks.priorityNone')}</SelectItem>
                  <SelectItem value="low">{t('pages.projects.detail.tasks.priorityLow')}</SelectItem>
                  <SelectItem value="medium">{t('pages.projects.detail.tasks.priorityMedium')}</SelectItem>
                  <SelectItem value="high">{t('pages.projects.detail.tasks.priorityHigh')}</SelectItem>
                  <SelectItem value="critical">{t('pages.projects.detail.tasks.priorityCritical')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.status')}</label>
              <Select
                value={formData.status || 'todo'}
                onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
                onOpenChange={setIsSelectOpen}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todo">{t('pages.projects.detail.tasks.statusTodo')}</SelectItem>
                  <SelectItem value="in_progress">{t('pages.projects.detail.tasks.statusInProgress')}</SelectItem>
                  <SelectItem value="blocked">{t('pages.projects.detail.tasks.statusBlocked')}</SelectItem>
                  <SelectItem value="review">{t('pages.projects.detail.tasks.statusReview')}</SelectItem>
                  <SelectItem value="done">{t('pages.projects.detail.tasks.statusDone')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 时间管理 */}
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.deadline')}</label>
              <Input
                type="date"
                value={formData.deadline || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, deadline: e.target.value }))}
                className="w-full"
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.estimatedHours')}</label>
                <Input
                  type="number"
                  min="0"
                  step="0.5"
                  value={formData.estimatedHours || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, estimatedHours: parseFloat(e.target.value) || 0 }))}
                  placeholder="0"
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.actualHours')}</label>
                <Input
                  type="number"
                  min="0"
                  step="0.5"
                  value={formData.actualHours || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, actualHours: parseFloat(e.target.value) || 0 }))}
                  placeholder="0"
                />
              </div>
            </div>
          </div>

          {/* 子任务进度 */}
          <SubtaskProgress task={task} />

          {/* 项目和领域关联 */}
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.project')}</label>
              <Select
                value={formData.projectId || 'none'}
                onValueChange={(value) => setFormData(prev => ({ ...prev, projectId: value }))}
                onOpenChange={setIsSelectOpen}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">{t('pages.projects.detail.tasks.noProject')}</SelectItem>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.area')}</label>
              <Select
                value={formData.areaId || 'none'}
                onValueChange={(value) => setFormData(prev => ({ ...prev, areaId: value }))}
                onOpenChange={setIsSelectOpen}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">{t('pages.projects.detail.tasks.noArea')}</SelectItem>
                  {areas.map((area) => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 阻塞原因 */}
          {formData.status === 'blocked' && (
            <div>
              <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.blockedBy')}</label>
              <Textarea
                value={formData.blockedBy || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, blockedBy: e.target.value }))}
                placeholder={t('pages.projects.detail.tasks.describeBlocking')}
                className="w-full min-h-[80px] resize-none"
              />
            </div>
          )}
          </div>
        </div>

        {/* 底部操作栏 - 固定在面板底部，不参与滚动 */}
        <div className="border-t p-4 bg-muted/30 flex-shrink-0 mt-auto">
          {/* 移动端：垂直布局，桌面端：水平布局 */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-3">
            {/* 左侧按钮组 */}
            <div className="flex items-center gap-2 flex-wrap">
              {onAddSubtask && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAddSubtask}
                  className="text-xs flex-shrink-0"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  {t('pages.projects.detail.tasks.subtask')}
                </Button>
              )}
              {onDelete && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDelete}
                  className="text-xs text-destructive hover:text-destructive flex-shrink-0"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  {t('common.delete')}
                </Button>
              )}
            </div>

            {/* 右侧主要操作按钮 */}
            <div className="flex items-center gap-2 flex-shrink-0">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
                className="flex-1 sm:flex-initial"
              >
                {t('common.cancel')}
              </Button>
              <Button
                onClick={handleSave}
                disabled={!formData.content?.trim() || isSubmitting}
                className="flex-1 sm:flex-initial"
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    <span className="hidden sm:inline">{t('common.saving')}</span>
                    <span className="sm:hidden">{t('common.save')}</span>
                  </div>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">{t('common.save')}</span>
                    <span className="sm:hidden">{t('common.save')}</span>
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

// 子任务进度组件
function SubtaskProgress({ task }: { task: ExtendedTask | null }) {
  const { getSubtaskProgress } = useTaskStore()

  if (!task) return null

  const progress = getSubtaskProgress(task.id)

  return (
    <div>
      <label className="text-sm font-medium mb-2 block">
        子任务进度 ({progress.completed}/{progress.total} - {progress.percentage}%)
      </label>
      <div className="space-y-2">
        <Progress value={progress.percentage} className="w-full" />
        <div className="text-xs text-muted-foreground">
          {progress.total === 1 && progress.completed === 0 ?
            '这是一个叶子任务，完成状态通过复选框控制' :
            `已完成 ${progress.completed} 个子任务，共 ${progress.total} 个`
          }
        </div>
      </div>
    </div>
  )
}

export default TaskDetailPanel
