# PaoLife 应用图标配置指南

## 概述

本指南详细说明如何修改和配置PaoLife Electron应用程序的各种图标，包括任务栏图标、应用内图标、通知图标等。

## 1. 主应用图标配置

### 1.1 图标文件格式要求

#### Windows平台
- **文件名**: `icon.ico`
- **格式**: ICO格式
- **尺寸要求**: 
  - 16x16 (小图标)
  - 32x32 (中等图标)
  - 48x48 (大图标)
  - 256x256 (高分辨率)
- **颜色深度**: 32位 (支持透明度)

#### macOS平台
- **文件名**: `icon.icns`
- **格式**: ICNS格式
- **尺寸要求**:
  - 16x16, 32x32, 64x64, 128x128, 256x256, 512x512, 1024x1024
- **Retina支持**: 包含@2x版本

#### Linux平台
- **文件名**: `icon.png`
- **格式**: PNG格式
- **推荐尺寸**: 512x512
- **颜色深度**: 32位RGBA

#### 源文件
- **文件名**: `icon.png`
- **推荐尺寸**: 1024x1024
- **用途**: 作为生成其他格式的源文件

### 1.2 图标文件存放位置

```
PaoLife/
├── build/                    # 构建资源目录
│   ├── icon.png             # 主图标文件 (1024x1024)
│   ├── icon.ico             # Windows图标
│   ├── icon.icns            # macOS图标
│   └── entitlements.mac.plist
├── src/
│   └── main/
│       └── index.ts         # 主进程文件
└── electron-builder.yml     # 构建配置
```

### 1.3 代码配置

#### 主进程配置 (src/main/index.ts)
```typescript
import icon from '../../build/icon.png?asset'

// 创建主窗口时的图标配置
mainWindow = new BrowserWindow({
  width: 1920,
  height: 1080,
  frame: false,
  show: false,
  autoHideMenuBar: true,
  // Linux平台需要显式设置图标
  ...(process.platform === 'linux' ? { icon } : {}),
  webPreferences: {
    preload: join(__dirname, '../preload/index.js'),
    sandbox: false
  }
})
```

#### electron-builder配置 (electron-builder.yml)
```yaml
appId: com.electron.app
productName: paolife
directories:
  buildResources: build    # 指定构建资源目录

# Windows特定配置
win:
  executableName: paolife
  icon: build/icon.ico     # 可选，默认会自动查找

# macOS特定配置  
mac:
  icon: build/icon.icns    # 可选，默认会自动查找

# Linux特定配置
linux:
  icon: build/icon.png     # 可选，默认会自动查找
  category: Utility
```

## 2. 图标制作建议

### 2.1 设计原则

#### 视觉设计
- **简洁明了**: 图标应该简洁，避免过多细节
- **识别性强**: 在小尺寸下仍能清晰识别
- **品牌一致**: 与应用的整体设计风格保持一致
- **平台适配**: 遵循各平台的设计规范

#### 技术要求
- **矢量优先**: 使用矢量图形确保缩放质量
- **透明背景**: 使用透明背景适应不同主题
- **边距适当**: 保留适当的边距避免被裁切
- **对比度**: 确保在浅色和深色背景下都清晰可见

### 2.2 制作工具推荐

#### 专业工具
- **Adobe Illustrator**: 矢量图标设计
- **Sketch**: macOS平台图标设计
- **Figma**: 在线协作设计工具
- **Inkscape**: 免费矢量图形编辑器

#### 图标生成工具
- **Icon Generator**: 在线图标生成器
- **App Icon Generator**: 移动应用图标生成
- **Electron Icon Maker**: 专门的Electron图标工具
- **ImageMagick**: 命令行图像处理工具

### 2.3 图标生成流程

#### 步骤1: 创建高分辨率源文件
```bash
# 推荐尺寸和格式
源文件: 1024x1024 PNG (32位RGBA)
```

#### 步骤2: 生成Windows ICO文件
```bash
# 使用ImageMagick生成ICO文件
magick icon.png -define icon:auto-resize=256,64,48,32,16 icon.ico
```

#### 步骤3: 生成macOS ICNS文件
```bash
# 创建iconset目录结构
mkdir icon.iconset
sips -z 16 16 icon.png --out icon.iconset/icon_16x16.png
sips -z 32 32 icon.png --out icon.iconset/<EMAIL>
sips -z 32 32 icon.png --out icon.iconset/icon_32x32.png
sips -z 64 64 icon.png --out icon.iconset/<EMAIL>
sips -z 128 128 icon.png --out icon.iconset/icon_128x128.png
sips -z 256 256 icon.png --out icon.iconset/<EMAIL>
sips -z 256 256 icon.png --out icon.iconset/icon_256x256.png
sips -z 512 512 icon.png --out icon.iconset/<EMAIL>
sips -z 512 512 icon.png --out icon.iconset/icon_512x512.png
sips -z 1024 1024 icon.png --out icon.iconset/<EMAIL>

# 生成ICNS文件
iconutil -c icns icon.iconset
```

## 3. 应用内图标配置

### 3.1 UI组件图标

#### Lucide React图标库
项目已集成Lucide React图标库，提供丰富的UI图标：

```typescript
import { Home, Settings, User, FileText } from 'lucide-react'

// 使用示例
<Home className="w-4 h-4" />
<Settings size={16} />
```

#### 自定义SVG图标
```typescript
// 在组件中使用自定义SVG
const CustomIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M8 0L10.5 5.5L16 8L10.5 10.5L8 16L5.5 10.5L0 8L5.5 5.5Z" />
  </svg>
)
```

### 3.2 文件类型图标

在文件树组件中配置不同文件类型的图标：

```typescript
// src/renderer/src/components/features/FileTreeNode.tsx
const getFileIcon = (type: string, name: string) => {
  if (type === 'folder') {
    return <FolderIcon />
  }
  
  const extension = name.split('.').pop()?.toLowerCase()
  switch (extension) {
    case 'md':
      return <FileTextIcon />
    case 'png':
    case 'jpg':
    case 'jpeg':
      return <ImageIcon />
    case 'pdf':
      return <FileIcon />
    default:
      return <DocumentIcon />
  }
}
```

## 4. 通知图标配置

### 4.1 系统通知图标

```typescript
// 在主进程中配置通知图标
import { Notification } from 'electron'

const notification = new Notification({
  title: 'PaoLife',
  body: '任务提醒',
  icon: join(__dirname, '../../build/icon.png'), // 通知图标
  silent: false
})
```

### 4.2 托盘图标配置

```typescript
// 创建系统托盘图标
import { Tray, Menu } from 'electron'

let tray: Tray | null = null

const createTray = () => {
  // 使用单色图标，适应系统主题
  const trayIcon = join(__dirname, '../../build/tray-icon.png')
  tray = new Tray(trayIcon)
  
  const contextMenu = Menu.buildFromTemplate([
    { label: '显示', click: () => mainWindow?.show() },
    { label: '退出', click: () => app.quit() }
  ])
  
  tray.setContextMenu(contextMenu)
  tray.setToolTip('PaoLife')
}
```

**托盘图标要求**:
- **Windows**: 16x16 PNG，单色设计
- **macOS**: 16x16 PNG，黑白设计，支持暗色模式
- **Linux**: 22x22 PNG，遵循系统图标主题

## 5. 图标更新流程

### 5.1 替换现有图标

1. **备份原图标**:
   ```bash
   cp build/icon.png build/icon.png.backup
   cp build/icon.ico build/icon.ico.backup
   cp build/icon.icns build/icon.icns.backup
   ```

2. **替换新图标**:
   - 将新的图标文件放入 `build/` 目录
   - 确保文件名和格式正确

3. **重新构建应用**:
   ```bash
   npm run build:win    # Windows
   npm run build:mac    # macOS  
   npm run build:linux  # Linux
   ```

### 5.2 验证图标配置

#### 开发环境验证
```bash
npm run dev
# 检查应用窗口图标是否正确显示
```

#### 构建后验证
- **Windows**: 检查exe文件图标和任务栏图标
- **macOS**: 检查app文件图标和Dock图标
- **Linux**: 检查桌面图标和应用菜单图标

## 6. 常见问题解决

### 6.1 图标不显示

**可能原因**:
- 图标文件路径错误
- 图标文件格式不支持
- 缓存问题

**解决方案**:
```bash
# 清理构建缓存
rm -rf dist out
npm run build
```

### 6.2 图标模糊

**可能原因**:
- 源图标分辨率不足
- 缩放算法问题

**解决方案**:
- 使用更高分辨率的源图标
- 为不同尺寸单独设计图标

### 6.3 Linux图标问题

**可能原因**:
- 图标路径在打包后失效
- 系统图标缓存问题

**解决方案**:
```typescript
// 确保图标路径正确
const iconPath = app.isPackaged 
  ? join(process.resourcesPath, 'build/icon.png')
  : join(__dirname, '../../build/icon.png')
```

## 7. 最佳实践

### 7.1 图标管理
- 使用版本控制管理图标文件
- 建立图标设计规范文档
- 定期审查和更新图标

### 7.2 性能优化
- 压缩图标文件大小
- 使用适当的图标格式
- 避免过大的图标文件

### 7.3 用户体验
- 保持图标风格一致
- 适配不同系统主题
- 提供高分辨率支持

---

通过遵循本指南，您可以为PaoLife应用程序配置完整、专业的图标系统，提升用户体验和品牌形象。
