@tailwind base;
@tailwind components;
@tailwind utilities;

/* Milkdown 主题和样式 - 必须在 @tailwind base 之后导入 */
@import '@milkdown/theme-nord/style.css';
@import '@milkdown/crepe/theme/crepe.css';
@import '@milkdown/crepe/theme/nord.css';
@import '@milkdown/crepe/theme/common/prosemirror.css';
@import '@milkdown/crepe/theme/common/reset.css';
@import '@milkdown/crepe/theme/common/block-edit.css';
@import '@milkdown/crepe/theme/common/toolbar.css';
@import '@milkdown/crepe/theme/common/code-mirror.css';
@import '@milkdown/crepe/theme/common/image-block.css';
@import '@milkdown/crepe/theme/common/link-tooltip.css';
@import '@milkdown/crepe/theme/common/list-item.css';

/* Milkdown编辑器自定义样式 */
.markdown-editor-container .milkdown {
  height: 100%;
  border: none;
  outline: none;
}

.markdown-editor-container .milkdown .editor {
  height: 100%;
  padding: 1rem;
}

/* 确保编辑器内容区域正确显示 */
.markdown-editor-container .ProseMirror {
  height: 100%;
  outline: none;
  padding: 1rem;
  font-size: 14px;
  line-height: 1.6;
}

@layer base {
  :root {
    /* 基础色彩系统 - 明亮主题 */
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47% 11%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47% 11%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47% 11%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 221.2 83.2% 53.3%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;

    /* P.A.R.A. 方法论色彩系统 - 更现代化的色调 */
    --project: 142.1 70.2% 45.3%;
    --project-foreground: 0 0% 100%;
    --area: 262.1 83.3% 57.8%;
    --area-foreground: 0 0% 100%;
    --resource: 24.6 95% 53.1%;
    --resource-foreground: 0 0% 100%;
    --archive: 215.4 16.3% 46.9%;
    --archive-foreground: 0 0% 100%;

    /* 状态色彩 - 更鲜明的对比 */
    --success: 142.1 70.2% 45.3%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --info: 199.89 89.09% 48.04%;
    --info-foreground: 0 0% 100%;

    /* 优先级色彩 */
    --priority-high: 0 84.2% 60.2%;
    --priority-medium: 38 92% 50%;
    --priority-low: 142.1 70.2% 45.3%;

    /* 侧边栏 - 明亮主题采用浅色雾面，提升对比与阅读性 */
    --sidebar: 210 40% 97%;
    --sidebar-foreground: 222.2 47% 11%;
    --sidebar-border: 214.3 31.8% 91.4%;
  }

  .dark {
    /* 基础色彩系统 - 暗色主题 */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 91.2% 59.8%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;

    /* P.A.R.A. 方法论色彩系统 - 暗色主题 */
    --project: 142.1 70.6% 45.3%;
    --project-foreground: 0 0% 100%;
    --area: 262.1 83.3% 57.8%;
    --area-foreground: 0 0% 100%;
    --resource: 20.5 90.2% 48.2%;
    --resource-foreground: 0 0% 100%;
    --archive: 215.4 16.3% 46.9%;
    --archive-foreground: 0 0% 100%;

    /* 状态色彩 - 暗色主题 */
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --info: 199.89 89.09% 48.04%;
    --info-foreground: 0 0% 100%;

    /* 优先级色彩 - 暗色主题 */
    --priority-high: 0 62.8% 30.6%;
    --priority-medium: 38 92% 50%;
    --priority-low: 142.1 70.6% 45.3%;

    /* 侧边栏 - 暗色主题 */
    --sidebar: 224 71.4% 4.1%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-border: 215 27.9% 16.9%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }

  /* 灵感窗口透明模式：避免透明 Electron 窗口下出现多余页面背景 */
  html.inspiration-transparent,
  body.inspiration-transparent,
  #root.inspiration-transparent {
    background-color: transparent !important;
    backdrop-filter: none;
  }

}

@layer components {
  /* PARA Method Component Styles */
  .para-project {
    background-color: hsl(var(--project) / 0.2);
    color: hsl(var(--project));
    border: 1px solid hsl(var(--project) / 0.3);
    font-weight: 600;
  }
  .para-area {
    background-color: hsl(var(--area) / 0.2);
    color: hsl(var(--area));
    border: 1px solid hsl(var(--area) / 0.3);
    font-weight: 600;
  }
  .para-resource {
    background-color: hsl(var(--resource) / 0.2);
    color: hsl(var(--resource));
    border: 1px solid hsl(var(--resource) / 0.3);
    font-weight: 600;
  }
  .para-archive {
    background-color: hsl(var(--archive) / 0.2);
    color: hsl(var(--archive));
    border: 1px solid hsl(var(--archive) / 0.3);
    font-weight: 600;
  }

  /* Priority Styles */
  .priority-high {
    background-color: hsl(var(--priority-high) / 0.1);
    color: hsl(var(--priority-high));
    border-color: hsl(var(--priority-high) / 0.2);
  }
  .priority-medium {
    background-color: hsl(var(--priority-medium) / 0.1);
    color: hsl(var(--priority-medium));
    border-color: hsl(var(--priority-medium) / 0.2);
  }
  .priority-low {
    background-color: hsl(var(--priority-low) / 0.1);
    color: hsl(var(--priority-low));
    border-color: hsl(var(--priority-low) / 0.2);
  }

  /* Status Styles */
  .status-success {
    background-color: hsl(var(--success) / 0.1);
    color: hsl(var(--success));
    border-color: hsl(var(--success) / 0.2);
  }
  .status-warning {
    background-color: hsl(var(--warning) / 0.1);
    color: hsl(var(--warning));
    border-color: hsl(var(--warning) / 0.2);
  }
  .status-info {
    background-color: hsl(var(--info) / 0.1);
    color: hsl(var(--info));
    border-color: hsl(var(--info) / 0.2);
  }

  /* Layout Utilities */
  .sidebar-layout {
    background-color: hsl(var(--sidebar));
    border-right: 1px solid hsl(var(--sidebar-border));
    color: hsl(var(--sidebar-foreground));
    background-image: linear-gradient(to bottom right, hsl(var(--sidebar) / 1), hsl(var(--sidebar) / 0.96));
  }

  /* Window Drag Region */
  .drag-region {
    -webkit-app-region: drag;
  }

  .no-drag {
    -webkit-app-region: no-drag;
  }

  /* Modern Windows 11 Style Window Controls */
  .window-control-btn {
    width: 46px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: transparent;
    color: hsl(var(--foreground) / 0.7);
    transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    border-radius: 0;
    cursor: pointer;
  }

  .window-control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: hsl(var(--foreground) / 0.06);
    opacity: 0;
    transition: opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .window-control-btn:hover {
    color: hsl(var(--foreground) / 0.9);
    background: hsl(var(--foreground) / 0.08);
  }

  .window-control-btn:hover::before {
    opacity: 1;
  }

  .window-control-btn:active {
    background: hsl(var(--foreground) / 0.12);
    transform: scale(0.98);
  }

  .window-control-btn:active::before {
    background: hsl(var(--foreground) / 0.08);
  }

  /* 关闭按钮的特殊样式 - Windows 11 红色 */
  .window-control-btn.close:hover {
    background: #c42b1c;
    color: white;
  }

  .window-control-btn.close:hover::before {
    opacity: 0;
  }

  .window-control-btn.close:active {
    background: #a23119;
    color: white;
    transform: scale(0.98);
  }

  .window-control-btn svg {
    width: 12px;
    height: 12px;
    z-index: 1;
    position: relative;
    transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* 微妙的悬停效果 */
  .window-control-btn:hover svg {
    transform: scale(1.02);
  }

  .window-control-btn:active svg {
    transform: scale(0.96);
  }

  /* 关闭按钮的图标动画 */
  .window-control-btn.close:hover svg {
    transform: scale(1.05);
  }

  .window-control-btn.close:active svg {
    transform: scale(0.95);
  }

  /* 焦点样式 */
  .window-control-btn:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: -2px;
  }

  /* Animation Utilities */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.4s ease-out;
  }

  /* Page Transition Utilities */
  .page-transition {
    transition:
      opacity 0.2s ease-in-out,
      transform 0.2s ease-in-out;
  }

  .page-enter {
    opacity: 0;
    transform: translateY(8px);
  }

  .page-enter-active {
    opacity: 1;
    transform: translateY(0);
  }

  /* Focus Styles */
  .focus-ring:focus {
    outline: none;
    box-shadow:
      0 0 0 2px hsl(var(--ring)),
      0 0 0 4px hsl(var(--background));
  }

  /* 隐藏滚动条样式 - 符合用户要求 */
  .scrollbar-hidden {
    /* Firefox */
    scrollbar-width: none;
    /* IE and Edge */
    -ms-overflow-style: none;
  }

  .scrollbar-hidden::-webkit-scrollbar {
    /* Chrome, Safari, Opera */
    display: none;
  }

  /* 自定义滚动条样式 - 仅在需要时显示 */
  .scrollbar-custom {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  }

  .scrollbar-custom::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-custom::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-custom::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
  }

  .scrollbar-custom::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground) / 0.6);
  }

  .scrollbar-custom::-webkit-scrollbar-corner {
    background: transparent;
  }
}

@layer utilities {
  /* Text Utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Interaction Utilities */
  .clickable {
    cursor: pointer;
    transition:
      color 0.2s,
      background-color 0.2s;
  }

  .clickable:hover {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }

  /* Layout Utilities */
  .center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .center-x {
    display: flex;
    justify-content: center;
  }

  .center-y {
    display: flex;
    align-items: center;
  }

  /* Quick Action Button Styles */
  .quick-action-btn {
    padding: 1rem;
    border-radius: 0.5rem;
    border: 2px dashed hsl(var(--muted-foreground) / 0.25);
    transition:
      border-color 0.2s,
      background-color 0.2s;
    text-align: center;
  }

  .quick-action-btn:hover {
    background-color: hsl(var(--accent) / 0.05);
  }

  .quick-action-icon {
    width: 2rem;
    height: 2rem;
    margin: 0 auto 0.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .quick-action-icon-primary {
    background-color: hsl(var(--primary) / 0.1);
  }
}

/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  70% {
    transform: scale(0.9);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Drag and Drop Styles */
.drop-target {
  position: relative;
}

.drop-target::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 2px;
  opacity: 0.8;
  animation: pulse 1.5s ease-in-out infinite;
}

.drag-overlay {
  pointer-events: none;
  transform-origin: center;
  animation: dragFloat 0.3s ease-out;
}

@keyframes dragFloat {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(1.05) rotate(3deg);
    opacity: 0.9;
  }
}

/* Drag handle hover effect */
.group:hover .drag-handle {
  opacity: 1;
}

/* Drop zone indicator */
.drop-zone-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background: #3b82f6;
  border-radius: 1px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.drop-zone-indicator.active {
  opacity: 1;
}

/* Hide scrollbar */
.scrollbar-hide {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

/* ===== Vditor Editor Styles ===== */

/* Vditor 容器基础样式 */
.vditor {
  @apply border border-border rounded-lg bg-background;
  font-family: inherit;
}

/* Vditor 工具栏样式 */
.vditor-toolbar {
  @apply border-b border-border bg-muted/50;
  border-radius: calc(var(--radius) - 2px) calc(var(--radius) - 2px) 0 0;
}

.vditor-toolbar .vditor-toolbar__item {
  @apply text-muted-foreground hover:text-foreground hover:bg-accent/50;
  border-radius: calc(var(--radius) - 4px);
  transition: all 0.2s ease;
}

.vditor-toolbar .vditor-toolbar__item--current {
  @apply bg-accent text-accent-foreground;
}

.vditor-toolbar .vditor-toolbar__divider {
  @apply bg-border;
}

/* Vditor 编辑区域样式 */
.vditor-content {
  @apply bg-background text-foreground;
}

.vditor-ir {
  @apply bg-background text-foreground;
  font-family: var(--font-mono), 'JetBrains Mono', 'Fira Code', monospace;
}

.vditor-wysiwyg {
  @apply bg-background text-foreground;
}

.vditor-sv {
  @apply bg-background text-foreground;
  font-family: var(--font-mono), 'JetBrains Mono', 'Fira Code', monospace;
}

/* Vditor 预览区域样式 */
.vditor-preview {
  @apply bg-background text-foreground;
}

.vditor-preview .vditor-reset {
  @apply text-foreground;
}

/* Vditor 分割线样式 */
.vditor-resize {
  @apply bg-border hover:bg-accent;
}

/* Vditor 状态栏样式 */
.vditor-counter {
  @apply text-muted-foreground bg-muted/30;
  border-top: 1px solid hsl(var(--border));
}

/* Vditor 提示框样式 */
.vditor-hint {
  @apply bg-popover border border-border shadow-md;
  border-radius: var(--radius);
}

.vditor-hint .vditor-hint__item {
  @apply text-popover-foreground hover:bg-accent hover:text-accent-foreground;
}

.vditor-hint .vditor-hint__item--current {
  @apply bg-accent text-accent-foreground;
}

/* Vditor 深色主题特殊适配 */
.dark .vditor {
  border-color: hsl(var(--border));
  background-color: hsl(var(--background));
}

.dark .vditor-toolbar {
  background-color: hsl(var(--muted) / 0.5);
  border-bottom-color: hsl(var(--border));
}

.dark .vditor-content,
.dark .vditor-ir,
.dark .vditor-wysiwyg,
.dark .vditor-sv {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

.dark .vditor-preview {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Vditor 内容区域的 Markdown 样式 */
.vditor-reset h1,
.vditor-reset h2,
.vditor-reset h3,
.vditor-reset h4,
.vditor-reset h5,
.vditor-reset h6 {
  @apply text-foreground font-semibold;
}

.vditor-reset h1 {
  @apply text-3xl mt-8 mb-4;
}
.vditor-reset h2 {
  @apply text-2xl mt-6 mb-3;
}
.vditor-reset h3 {
  @apply text-xl mt-4 mb-2;
}
.vditor-reset h4 {
  @apply text-lg mt-3 mb-2;
}
.vditor-reset h5 {
  @apply text-base mt-2 mb-1;
}
.vditor-reset h6 {
  @apply text-sm mt-2 mb-1;
}

.vditor-reset p {
  @apply mb-4 leading-relaxed text-foreground;
}

.vditor-reset blockquote {
  @apply border-l-4 border-primary/30 pl-4 py-2 my-4 bg-muted/50 text-muted-foreground italic;
}

.vditor-reset code {
  @apply bg-muted px-1.5 py-0.5 rounded text-sm font-mono text-foreground;
}

.vditor-reset pre {
  @apply bg-muted p-4 rounded-lg overflow-x-auto my-4;
}

.vditor-reset pre code {
  @apply bg-transparent p-0;
}

.vditor-reset a {
  @apply text-primary hover:text-primary/80 hover:underline;
}

.vditor-reset ul,
.vditor-reset ol {
  @apply space-y-1 my-4;
}

.vditor-reset li {
  @apply py-1;
}

.vditor-reset table {
  @apply min-w-full border border-border rounded-lg my-4;
}

.vditor-reset th {
  @apply px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider bg-muted/50;
}

.vditor-reset td {
  @apply px-4 py-2 text-sm text-foreground border-t border-border;
}

.vditor-reset tr:hover {
  @apply bg-muted/30;
}

/* ===== Milkdown Editor Theme Styles ===== */

/* 编辑器主题样式 */
.milkdown-editor {
  height: 100%;
  width: 100%;
}

/* 经典主题 */
.editor-theme-classic {
  --crepe-color-primary: hsl(var(--primary));
  --crepe-color-background: hsl(var(--background));
  --crepe-color-text: hsl(var(--foreground));
  --crepe-color-border: hsl(var(--border));
  --crepe-color-muted: hsl(var(--muted));
}

.editor-theme-classic .milkdown {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
}

/* 深色主题 */
.editor-theme-dark {
  --crepe-color-primary: #60a5fa;
  --crepe-color-background: #1e293b;
  --crepe-color-text: #f1f5f9;
  --crepe-color-border: #334155;
  --crepe-color-muted: #475569;
}

.editor-theme-dark .milkdown {
  background-color: #1e293b;
  color: #f1f5f9;
  border: 1px solid #334155;
  border-radius: 8px;
}

/* 专注模式 */
.editor-focus-mode .milkdown .crepe-toolbar {
  display: none !important;
}

.editor-focus-mode .milkdown {
  border: none !important;
  box-shadow: none !important;
}

.editor-focus-mode {
  --crepe-color-border: transparent;
}

/* WikiLink 样式 */
.wiki-link {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  margin: 0 1px;
  background-color: hsl(var(--primary) / 0.1);
  border: 1px solid hsl(var(--primary) / 0.3);
  border-radius: 4px;
  color: hsl(var(--primary));
  text-decoration: none;
  font-size: 0.9em;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.wiki-link:hover {
  background-color: hsl(var(--primary) / 0.2);
  border-color: hsl(var(--primary) / 0.4);
}

.link-text {
  text-decoration: none;
  font-weight: 500;
}

/* 自定义滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(209 213 219);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgb(156 163 175);
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hidden {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

/* 灵感记录窗口专用样式 */
.inspiration-window {
  overflow: hidden;
}

.inspiration-window * {
  box-sizing: border-box;
}

/* Gamma 风格：角落彩带渐变与按钮描边动画 */
.inspiration-ribbon {
  position: absolute;
  pointer-events: none;
  inset: 0;
}
.inspiration-ribbon::before,
.inspiration-ribbon::after {
  content: "";
  position: absolute;
  width: 160px;
  height: 160px;
  filter: blur(32px);
  opacity: 0.08;
  border-radius: 50%;
}
/* 左下角彩带 */
.inspiration-ribbon::before {
  left: -40px;
  bottom: -40px;
  background: conic-gradient(from 220deg, hsl(var(--primary) / 0.8), hsl(var(--accent) / 0.8), hsl(var(--primary) / 0.8));
}
/* 右上角彩带 */
.inspiration-ribbon::after {
  right: -40px;
  top: -40px;
  background: conic-gradient(from 40deg, hsl(var(--warning) / 0.8), hsl(var(--success) / 0.8), hsl(var(--warning) / 0.8));
}

/* 霓虹微光按钮（非动画，提升可读性与层次） */
.btn-neon {
  box-shadow: 0 0 0 1px hsl(var(--primary) / 0.35), 0 6px 16px rgba(0,0,0,0.15);
}

/* 标题与 ESC 提示渐显 */
@keyframes rise-fade {
  from { transform: translateY(4px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
.rise-fade { animation: rise-fade 240ms ease-out both; }



/* ===== Resources Page Layout Helpers ===== */
/* 高度 = 窗口高度 - 标题栏高度 - 上下 padding；支持上层覆写变量值 */
.resources-layout {
  height: calc(100vh - var(--titlebar-h, 32px) - var(--content-pt, 24px) - var(--content-pb, 24px));
  overflow: hidden; /* 禁止页面级别滚动 */
}

/* 强制浅色编辑器区域（不随全局暗色主题改变） */
.force-light-editor .milkdown {
  background-color: #ffffff !important;
  color: #111827 !important; /* tailwind gray-900 */
}

.force-light-editor .milkdown .editor {
  background-color: #ffffff !important;
}

/* 确保编辑器容器正确填充高度并处理滚动 */
.markdown-editor-container {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.markdown-editor-container .milkdown-editor {
  height: 100% !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.markdown-editor-container .milkdown {
  height: 100% !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.markdown-editor-container .milkdown .editor {
  height: 100% !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* 强制覆盖 Crepe 的默认样式 */
.markdown-editor-container .milkdown .crepe {
  height: 100% !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.markdown-editor-container .milkdown .crepe-editor {
  height: 100% !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* 编辑器内容区域滚动设置 - 隐藏滚动条 */
.markdown-editor-container .milkdown .editor .ProseMirror {
  flex: 1 1 auto !important;
  min-height: 0 !important;
  height: auto !important;
  overflow-y: auto !important;
  overscroll-behavior: contain !important;
  /* 隐藏滚动条 */
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

.markdown-editor-container .milkdown .editor .ProseMirror::-webkit-scrollbar {
  display: none !important; /* Chrome, Safari, Opera */
}

/* 强制覆盖任何可能影响高度的 Crepe 样式 */
.markdown-editor-container .milkdown .crepe .ProseMirror {
  flex: 1 1 auto !important;
  min-height: 0 !important;
  height: auto !important;
  overflow-y: auto !important;
  overscroll-behavior: contain !important;
  /* 隐藏滚动条 */
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.markdown-editor-container .milkdown .crepe .ProseMirror::-webkit-scrollbar {
  display: none !important;
}

/* 确保工具栏不影响编辑器高度 */
.markdown-editor-container .milkdown .toolbar {
  flex-shrink: 0 !important;
}

/* 确保编辑器主体区域占据剩余空间 */
.markdown-editor-container .milkdown .editor-body,
.markdown-editor-container .milkdown .crepe-body {
  flex: 1 1 auto !important;
  min-height: 0 !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 强制编辑器固定高度的辅助类 */
.editor-fixed-height {
  height: 100% !important;
  min-height: 0 !important;
  max-height: 100% !important;
}

/* 针对Milkdown Crepe的具体DOM结构进行强制样式设置 */
.markdown-editor-container [data-milkdown-root] {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.markdown-editor-container [data-milkdown-root] > div {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* 强制设置所有可能的编辑器容器 */
.markdown-editor-container .milkdown-container,
.markdown-editor-container .crepe-container,
.markdown-editor-container .editor-container {
  height: 100% !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* 确保编辑区域占据剩余空间并可滚动 */
.markdown-editor-container .ProseMirror,
.markdown-editor-container [contenteditable="true"] {
  flex: 1 1 auto !important;
  min-height: 0 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  height: auto !important;
  /* 隐藏滚动条 */
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.markdown-editor-container .ProseMirror::-webkit-scrollbar,
.markdown-editor-container [contenteditable="true"]::-webkit-scrollbar {
  display: none !important;
}

/* 防止任何子元素破坏布局 */
.markdown-editor-container * {
  box-sizing: border-box !important;
}

/* 最激进的方法：强制覆盖所有可能的编辑器样式 */
.markdown-editor-container .milkdown,
.markdown-editor-container .crepe,
.markdown-editor-container [data-milkdown-root],
.markdown-editor-container .editor,
.markdown-editor-container .milkdown-container,
.markdown-editor-container .crepe-container {
  height: 100% !important;
  max-height: 100% !important;
  min-height: 0 !important;
  flex: 1 1 auto !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* 强制编辑区域样式 - 关键修复：确保滚动功能正常 */
.markdown-editor-container .ProseMirror,
.markdown-editor-container [contenteditable="true"],
.markdown-editor-container .editor-content,
.markdown-editor-container .prosemirror-editor {
  /* 使用明确的高度计算，避免flex导致的高度问题 */
  height: calc(100vh - 200px) !important; /* 临时固定高度，将被JS动态设置 */
  min-height: 300px !important;
  max-height: calc(100vh - 200px) !important;

  /* 强制启用滚动功能 */
  overflow: auto !important;
  overflow-y: scroll !important;
  overflow-x: hidden !important;
  overscroll-behavior: contain !important;
  -webkit-overflow-scrolling: touch !important; /* iOS滚动优化 */

  position: relative !important;
  /* 确保正确的坐标计算 */
  box-sizing: border-box !important;
  padding: 16px !important;

  /* 确保内容可以超出容器 */
  word-wrap: break-word !important;
  white-space: pre-wrap !important;

  /* 强制隐藏滚动条但保持滚动功能 */
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.markdown-editor-container .ProseMirror::-webkit-scrollbar,
.markdown-editor-container [contenteditable="true"]::-webkit-scrollbar,
.markdown-editor-container .editor-content::-webkit-scrollbar,
.markdown-editor-container .prosemirror-editor::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* 强制启用滚动功能 - 移除可能阻止滚动的样式 */
.markdown-editor-container .ProseMirror {
  resize: none !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;

  /* 确保滚动功能正常工作 */
  touch-action: pan-y !important; /* 允许垂直滚动 */
  pointer-events: auto !important; /* 确保可以交互 */
  user-select: text !important; /* 允许文本选择 */
}

/* 移除可能阻止滚动的父容器样式 */
.markdown-editor-container .milkdown .editor {
  overflow: hidden !important; /* 让子元素ProseMirror处理滚动 */
}

/* 确保编辑器容器不阻止滚动 */
.markdown-editor-container .milkdown {
  overflow: hidden !important; /* 让子元素ProseMirror处理滚动 */
}

