// Kysely Migration Manager
// Handles database initialization and data migration from Prisma to Kysely

import { getKyselyClient, runMigrations, serializeJson, booleanToInt, getCurrentTimestamp } from './kyselyClient'
import fs from 'fs'
import path from 'path'
import type { Database } from '../shared/database-types'

export class KyselyMigrationManager {
  private get db() {
    return getKyselyClient()
  }

  /**
   * Initialize database with all tables and indexes
   */
  async initializeDatabase(): Promise<void> {
    console.log('Initializing Kysely database...')
    
    try {
      await runMigrations()
      console.log('✅ Database initialization completed successfully')
    } catch (error) {
      console.error('❌ Database initialization failed:', error)
      throw error
    }
  }

  /**
   * Check if database is properly initialized
   */
  async isDatabaseInitialized(): Promise<boolean> {
    try {
      // Check if key tables exist by querying them
      const tables = ['User', 'Area', 'Project', 'Task', 'Habit']
      
      for (const table of tables) {
        await this.db.selectFrom(table as any).select('id').limit(1).execute()
      }
      
      return true
    } catch (error) {
      console.log('Database not initialized or missing tables:', error)
      return false
    }
  }

  /**
   * Migrate data from existing Prisma database
   */
  async migrateFromPrisma(prismaDbPath: string): Promise<void> {
    if (!fs.existsSync(prismaDbPath)) {
      console.log('No existing Prisma database found, skipping migration')
      return
    }

    console.log('Starting data migration from Prisma database...')
    
    try {
      // This would require reading from the old Prisma database
      // For now, we'll create a basic structure
      await this.createDefaultData()
      console.log('✅ Data migration completed successfully')
    } catch (error) {
      console.error('❌ Data migration failed:', error)
      throw error
    }
  }

  /**
   * Create default data for new installations
   */
  async createDefaultData(): Promise<void> {
    console.log('Creating default data...')

    // Create default user if none exists
    const existingUsers = await this.db.selectFrom('User').select('id').limit(1).execute()

    if (existingUsers.length === 0) {
      await this.db.insertInto('User').values({
        id: 'default-user',
        username: 'user',
        password: 'password', // In real app, this should be hashed
        settings: serializeJson({
          theme: 'light',
          language: 'zh-CN'
        })
      }).execute()

      console.log('✅ Default user created')
    }

    // 不再自动创建默认领域，让用户自己创建
    // 这样可以避免用户看到不需要的默认数据
    console.log('ℹ️ Skipping default areas creation - users will create their own areas')

    // 注释掉默认领域创建逻辑
    /*
    // Create default areas if none exist
    const existingAreas = await this.db.selectFrom('Area').select('id').limit(1).execute()

    if (existingAreas.length === 0) {
      const defaultAreas = [
        {
          id: 'area-health',
          name: '健康',
          description: '身体健康和运动',
          icon: '🏃‍♂️',
          color: '#10B981',
          archived: booleanToInt(false),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        },
        {
          id: 'area-work',
          name: '工作',
          description: '职业发展和工作项目',
          icon: '💼',
          color: '#3B82F6',
          archived: booleanToInt(false),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        },
        {
          id: 'area-learning',
          name: '学习',
          description: '知识学习和技能提升',
          icon: '📚',
          color: '#8B5CF6',
          archived: booleanToInt(false),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        }
      ]

      await this.db.insertInto('Area').values(defaultAreas).execute()
      console.log('✅ Default areas created')
    }
    */

    // Create default review templates if none exist
    const existingTemplates = await this.db.selectFrom('ReviewTemplate').select('id').limit(1).execute()
    
    if (existingTemplates.length === 0) {
      const defaultTemplates = [
        {
          id: 'template-daily',
          name: '每日回顾',
          description: '每日反思和计划',
          type: 'daily',
          structure: serializeJson([
            { type: 'text', label: '今天完成了什么？', key: 'completed' },
            { type: 'text', label: '遇到了什么挑战？', key: 'challenges' },
            { type: 'text', label: '明天的重点是什么？', key: 'tomorrow' }
          ]),
          isDefault: booleanToInt(true),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        },
        {
          id: 'template-weekly',
          name: '每周回顾',
          description: '每周总结和规划',
          type: 'weekly',
          structure: serializeJson([
            { type: 'text', label: '本周主要成就', key: 'achievements' },
            { type: 'text', label: '需要改进的地方', key: 'improvements' },
            { type: 'text', label: '下周目标', key: 'next_goals' }
          ]),
          isDefault: booleanToInt(true),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        }
      ]

      await this.db.insertInto('ReviewTemplate').values(defaultTemplates).execute()
      console.log('✅ Default review templates created')
    }

    console.log('✅ Default data creation completed')
  }

  /**
   * Verify database integrity
   */
  async verifyDatabaseIntegrity(): Promise<boolean> {
    try {
      console.log('Verifying database integrity...')

      // Check foreign key constraints
      const areas = await this.db.selectFrom('Area').select('id').execute()
      const projects = await this.db.selectFrom('Project').selectAll().execute()
      const tasks = await this.db.selectFrom('Task').selectAll().execute()

      // Verify project-area relationships
      for (const project of projects) {
        if (project.areaId && !areas.find(a => a.id === project.areaId)) {
          console.warn(`Project ${project.id} references non-existent area ${project.areaId}`)
          return false
        }
      }

      // Verify task relationships
      for (const task of tasks) {
        if (task.areaId && !areas.find(a => a.id === task.areaId)) {
          console.warn(`Task ${task.id} references non-existent area ${task.areaId}`)
          return false
        }
        if (task.projectId && !projects.find(p => p.id === task.projectId)) {
          console.warn(`Task ${task.id} references non-existent project ${task.projectId}`)
          return false
        }
      }

      console.log('✅ Database integrity verification passed')
      return true
    } catch (error) {
      console.error('❌ Database integrity verification failed:', error)
      return false
    }
  }

  /**
   * Get migration status and statistics
   */
  async getMigrationStatus(): Promise<{
    initialized: boolean
    tableCount: number
    recordCounts: Record<string, number>
  }> {
    const initialized = await this.isDatabaseInitialized()
    
    if (!initialized) {
      return {
        initialized: false,
        tableCount: 0,
        recordCounts: {}
      }
    }

    const tables = [
      'User', 'Area', 'Project', 'ProjectKPI', 'KPIRecord',
      'Deliverable', 'DeliverableResource', 'AreaMetric', 'AreaMetricRecord',
      'Habit', 'HabitRecord', 'Checklist', 'ChecklistInstance',
      'Task', 'Tag', 'TaskTag', 'RecurringTask', 'ResourceLink',
      'Review', 'ReviewTemplate', 'InboxNote', 'DocumentLink'
    ]

    const recordCounts: Record<string, number> = {}

    for (const table of tables) {
      try {
        const result = await this.db.selectFrom(table as any)
          .select(this.db.fn.count('id').as('count'))
          .executeTakeFirst()
        recordCounts[table] = Number(result?.count || 0)
      } catch (error) {
        recordCounts[table] = 0
      }
    }

    return {
      initialized: true,
      tableCount: tables.length,
      recordCounts
    }
  }
}
