:root {
  --ev-c-white: #ffffff;
  --ev-c-white-soft: #f8f8f8;
  --ev-c-white-mute: #f2f2f2;

  --ev-c-black: #1b1b1f;
  --ev-c-black-soft: #222222;
  --ev-c-black-mute: #282828;

  --ev-c-gray-1: #515c67;
  --ev-c-gray-2: #414853;
  --ev-c-gray-3: #32363f;

  --ev-c-text-1: rgba(255, 255, 245, 0.86);
  --ev-c-text-2: rgba(235, 235, 245, 0.6);
  --ev-c-text-3: rgba(235, 235, 245, 0.38);

  --ev-button-alt-border: transparent;
  --ev-button-alt-text: var(--ev-c-text-1);
  --ev-button-alt-bg: var(--ev-c-gray-3);
  --ev-button-alt-hover-border: transparent;
  --ev-button-alt-hover-text: var(--ev-c-text-1);
  --ev-button-alt-hover-bg: var(--ev-c-gray-2);
  --color-background: var(--ev-c-black);
  --color-background-soft: var(--ev-c-black-soft);
  --color-background-mute: var(--ev-c-black-mute);

  --color-text: var(--ev-c-text-1);
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

ul {
  list-style: none;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  line-height: 1.6;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: JetBrains Mono, Fira Code, Monaco, Consolas, Liberation Mono, Courier New, monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

:root {
    /* 基础色彩系统 - 明亮主题 */
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47% 11%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47% 11%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47% 11%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 221.2 83.2% 53.3%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;

    /* P.A.R.A. 方法论色彩系统 - 更现代化的色调 */
    --project: 142.1 70.2% 45.3%;
    --project-foreground: 0 0% 100%;
    --area: 262.1 83.3% 57.8%;
    --area-foreground: 0 0% 100%;
    --resource: 24.6 95% 53.1%;
    --resource-foreground: 0 0% 100%;
    --archive: 215.4 16.3% 46.9%;
    --archive-foreground: 0 0% 100%;

    /* 状态色彩 - 更鲜明的对比 */
    --success: 142.1 70.2% 45.3%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --info: 199.89 89.09% 48.04%;
    --info-foreground: 0 0% 100%;

    /* 优先级色彩 */
    --priority-high: 0 84.2% 60.2%;
    --priority-medium: 38 92% 50%;
    --priority-low: 142.1 70.2% 45.3%;

    /* 侧边栏 - 明亮主题采用浅色雾面，提升对比与阅读性 */
    --sidebar: 210 40% 97%;
    --sidebar-foreground: 222.2 47% 11%;
    --sidebar-border: 214.3 31.8% 91.4%;
  }

.dark {
    /* 基础色彩系统 - 暗色主题 */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 91.2% 59.8%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;

    /* P.A.R.A. 方法论色彩系统 - 暗色主题 */
    --project: 142.1 70.6% 45.3%;
    --project-foreground: 0 0% 100%;
    --area: 262.1 83.3% 57.8%;
    --area-foreground: 0 0% 100%;
    --resource: 20.5 90.2% 48.2%;
    --resource-foreground: 0 0% 100%;
    --archive: 215.4 16.3% 46.9%;
    --archive-foreground: 0 0% 100%;

    /* 状态色彩 - 暗色主题 */
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --info: 199.89 89.09% 48.04%;
    --info-foreground: 0 0% 100%;

    /* 优先级色彩 - 暗色主题 */
    --priority-high: 0 62.8% 30.6%;
    --priority-medium: 38 92% 50%;
    --priority-low: 142.1 70.6% 45.3%;

    /* 侧边栏 - 暗色主题 */
    --sidebar: 224 71.4% 4.1%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-border: 215 27.9% 16.9%;
  }

* {
    border-color: hsl(var(--border));
  }

body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }

/* 灵感窗口透明模式：避免透明 Electron 窗口下出现多余页面背景 */

html.inspiration-transparent,
  body.inspiration-transparent,
  #root.inspiration-transparent {
    background-color: transparent !important;
    backdrop-filter: none;
  }

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}

.prose {
  color: var(--tw-prose-body);
  max-width: 65ch;
}

.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}

.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}

.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
}

.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}

.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets);
}

.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}

.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}

.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
}

.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: open-quote;
}

.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: close-quote;
}

.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 900;
  color: inherit;
}

.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}

.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}

.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}

.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em;
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "`";
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: "`";
}

.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em;
}

.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em;
}

.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-inline-start: 1.1428571em;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: none;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: none;
}

.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}

.prose :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}

.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.prose :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}

.prose :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 0;
}

.prose :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: baseline;
}

.prose :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}

.prose :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: top;
}

.prose :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  text-align: start;
}

.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}

.prose {
  --tw-prose-body: #374151;
  --tw-prose-headings: #111827;
  --tw-prose-lead: #4b5563;
  --tw-prose-links: #111827;
  --tw-prose-bold: #111827;
  --tw-prose-counters: #6b7280;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #e5e7eb;
  --tw-prose-quotes: #111827;
  --tw-prose-quote-borders: #e5e7eb;
  --tw-prose-captions: #6b7280;
  --tw-prose-kbd: #111827;
  --tw-prose-kbd-shadows: 17 24 39;
  --tw-prose-code: #111827;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #d1d5db;
  --tw-prose-td-borders: #e5e7eb;
  --tw-prose-invert-body: #d1d5db;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #9ca3af;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #9ca3af;
  --tw-prose-invert-bullets: #4b5563;
  --tw-prose-invert-hr: #374151;
  --tw-prose-invert-quotes: #f3f4f6;
  --tw-prose-invert-quote-borders: #374151;
  --tw-prose-invert-captions: #9ca3af;
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #d1d5db;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #4b5563;
  --tw-prose-invert-td-borders: #374151;
  font-size: 1rem;
  line-height: 1.75;
}

.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}

.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}

.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  padding-inline-start: 1.625em;
}

.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-sm {
  font-size: 0.875rem;
  line-height: 1.7142857;
}

.prose-sm :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
}

.prose-sm :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2857143em;
  line-height: 1.5555556;
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}

.prose-sm :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-inline-start: 1.1111111em;
}

.prose-sm :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 2.1428571em;
  margin-top: 0;
  margin-bottom: 0.8em;
  line-height: 1.2;
}

.prose-sm :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.4285714em;
  margin-top: 1.6em;
  margin-bottom: 0.8em;
  line-height: 1.4;
}

.prose-sm :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2857143em;
  margin-top: 1.5555556em;
  margin-bottom: 0.4444444em;
  line-height: 1.5555556;
}

.prose-sm :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.4285714em;
  margin-bottom: 0.5714286em;
  line-height: 1.4285714;
}

.prose-sm :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-sm :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  border-radius: 0.3125rem;
  padding-top: 0.1428571em;
  padding-inline-end: 0.3571429em;
  padding-bottom: 0.1428571em;
  padding-inline-start: 0.3571429em;
}

.prose-sm :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
}

.prose-sm :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
}

.prose-sm :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
}

.prose-sm :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.6666667;
  margin-top: 1.6666667em;
  margin-bottom: 1.6666667em;
  border-radius: 0.25rem;
  padding-top: 0.6666667em;
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}

.prose-sm :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
  padding-inline-start: 1.5714286em;
}

.prose-sm :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
  padding-inline-start: 1.5714286em;
}

.prose-sm :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.2857143em;
  margin-bottom: 0.2857143em;
}

.prose-sm :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4285714em;
}

.prose-sm :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4285714em;
}

.prose-sm :where(.prose-sm > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}

.prose-sm :where(.prose-sm > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(.prose-sm > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.1428571em;
}

.prose-sm :where(.prose-sm > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(.prose-sm > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.1428571em;
}

.prose-sm :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}

.prose-sm :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
}

.prose-sm :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.2857143em;
  padding-inline-start: 1.5714286em;
}

.prose-sm :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2.8571429em;
  margin-bottom: 2.8571429em;
}

.prose-sm :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.5;
}

.prose-sm :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}

.prose-sm :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-sm :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-sm :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.6666667em;
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}

.prose-sm :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-sm :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-sm :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-sm :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.3333333;
  margin-top: 0.6666667em;
}

.prose-sm :where(.prose-sm > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(.prose-sm > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

/* PARA Method Component Styles */

.para-project {
    background-color: hsl(var(--project) / 0.2);
    color: hsl(var(--project));
    border: 1px solid hsl(var(--project) / 0.3);
    font-weight: 600;
  }

.para-area {
    background-color: hsl(var(--area) / 0.2);
    color: hsl(var(--area));
    border: 1px solid hsl(var(--area) / 0.3);
    font-weight: 600;
  }

.para-resource {
    background-color: hsl(var(--resource) / 0.2);
    color: hsl(var(--resource));
    border: 1px solid hsl(var(--resource) / 0.3);
    font-weight: 600;
  }

.para-archive {
    background-color: hsl(var(--archive) / 0.2);
    color: hsl(var(--archive));
    border: 1px solid hsl(var(--archive) / 0.3);
    font-weight: 600;
  }

/* Priority Styles */

/* Status Styles */

/* Layout Utilities */

.sidebar-layout {
    background-color: hsl(var(--sidebar));
    border-right: 1px solid hsl(var(--sidebar-border));
    color: hsl(var(--sidebar-foreground));
    background-image: linear-gradient(to bottom right, hsl(var(--sidebar) / 1), hsl(var(--sidebar) / 0.96));
  }

/* Window Drag Region */

.drag-region {
    -webkit-app-region: drag;
  }

.no-drag {
    -webkit-app-region: no-drag;
  }

/* Modern Windows 11 Style Window Controls */

.window-control-btn {
    width: 46px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: transparent;
    color: hsl(var(--foreground) / 0.7);
    transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    border-radius: 0;
    cursor: pointer;
  }

.window-control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: hsl(var(--foreground) / 0.06);
    opacity: 0;
    transition: opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

.window-control-btn:hover {
    color: hsl(var(--foreground) / 0.9);
    background: hsl(var(--foreground) / 0.08);
  }

.window-control-btn:hover::before {
    opacity: 1;
  }

.window-control-btn:active {
    background: hsl(var(--foreground) / 0.12);
    transform: scale(0.98);
  }

.window-control-btn:active::before {
    background: hsl(var(--foreground) / 0.08);
  }

/* 关闭按钮的特殊样式 - Windows 11 红色 */

.window-control-btn.close:hover {
    background: #c42b1c;
    color: white;
  }

.window-control-btn.close:hover::before {
    opacity: 0;
  }

.window-control-btn.close:active {
    background: #a23119;
    color: white;
    transform: scale(0.98);
  }

.window-control-btn svg {
    width: 12px;
    height: 12px;
    z-index: 1;
    position: relative;
    transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

/* 微妙的悬停效果 */

.window-control-btn:hover svg {
    transform: scale(1.02);
  }

.window-control-btn:active svg {
    transform: scale(0.96);
  }

/* 关闭按钮的图标动画 */

.window-control-btn.close:hover svg {
    transform: scale(1.05);
  }

.window-control-btn.close:active svg {
    transform: scale(0.95);
  }

/* 焦点样式 */

.window-control-btn:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: -2px;
  }

/* Animation Utilities */

/* Page Transition Utilities */

/* Focus Styles */

/* 隐藏滚动条样式 - 符合用户要求 */

.scrollbar-hidden {
    /* Firefox */
    scrollbar-width: none;
    /* IE and Edge */
    -ms-overflow-style: none;
  }

.scrollbar-hidden::-webkit-scrollbar {
    /* Chrome, Safari, Opera */
    display: none;
  }

/* 自定义滚动条样式 - 仅在需要时显示 */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.\!visible {
  visibility: visible !important;
}

.visible {
  visibility: visible;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.inset-0 {
  inset: 0px;
}

.inset-4 {
  inset: 1rem;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.-bottom-0\.5 {
  bottom: -0.125rem;
}

.-bottom-12 {
  bottom: -3rem;
}

.-left-6 {
  left: -1.5rem;
}

.-right-0\.5 {
  right: -0.125rem;
}

.-top-1 {
  top: -0.25rem;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-2 {
  bottom: 0.5rem;
}

.bottom-4 {
  bottom: 1rem;
}

.bottom-full {
  bottom: 100%;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-2 {
  left: 0.5rem;
}

.left-3 {
  left: 0.75rem;
}

.left-4 {
  left: 1rem;
}

.left-\[50\%\] {
  left: 50%;
}

.right-0 {
  right: 0px;
}

.right-1 {
  right: 0.25rem;
}

.right-2 {
  right: 0.5rem;
}

.right-4 {
  right: 1rem;
}

.top-0 {
  top: 0px;
}

.top-1\/2 {
  top: 50%;
}

.top-1\/4 {
  top: 25%;
}

.top-2 {
  top: 0.5rem;
}

.top-4 {
  top: 1rem;
}

.top-6 {
  top: 1.5rem;
}

.top-\[50\%\] {
  top: 50%;
}

.top-full {
  top: 100%;
}

.z-10 {
  z-index: 10;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.col-start-2 {
  grid-column-start: 2;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

.row-start-1 {
  grid-row-start: 1;
}

.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.-ml-2 {
  margin-left: -0.5rem;
}

.-mt-px {
  margin-top: -1px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mt-0\.5 {
  margin-top: 0.125rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-1\.5 {
  margin-top: 0.375rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-auto {
  margin-top: auto;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.size-2 {
  width: 0.5rem;
  height: 0.5rem;
}

.size-2\.5 {
  width: 0.625rem;
  height: 0.625rem;
}

.size-3\.5 {
  width: 0.875rem;
  height: 0.875rem;
}

.size-4 {
  width: 1rem;
  height: 1rem;
}

.size-9 {
  width: 2.25rem;
  height: 2.25rem;
}

.h-0 {
  height: 0px;
}

.h-0\.5 {
  height: 0.125rem;
}

.h-1 {
  height: 0.25rem;
}

.h-1\.5 {
  height: 0.375rem;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-2 {
  height: 0.5rem;
}

.h-20 {
  height: 5rem;
}

.h-3 {
  height: 0.75rem;
}

.h-3\.5 {
  height: 0.875rem;
}

.h-32 {
  height: 8rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-64 {
  height: 16rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[300px\] {
  height: 300px;
}

.h-\[400px\] {
  height: 400px;
}

.h-\[calc\(100\%-1px\)\] {
  height: calc(100% - 1px);
}

.h-\[calc\(100vh-280px\)\] {
  height: calc(100vh - 280px);
}

.h-\[calc\(100vh-4rem\)\] {
  height: calc(100vh - 4rem);
}

.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}

.h-auto {
  height: auto;
}

.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.h-screen {
  height: 100vh;
}

.max-h-32 {
  max-height: 8rem;
}

.max-h-40 {
  max-height: 10rem;
}

.max-h-44 {
  max-height: 11rem;
}

.max-h-48 {
  max-height: 12rem;
}

.max-h-60 {
  max-height: 15rem;
}

.max-h-96 {
  max-height: 24rem;
}

.max-h-\[200px\] {
  max-height: 200px;
}

.max-h-\[400px\] {
  max-height: 400px;
}

.max-h-\[600px\] {
  max-height: 600px;
}

.max-h-\[80vh\] {
  max-height: 80vh;
}

.max-h-\[90vh\] {
  max-height: 90vh;
}

.min-h-0 {
  min-height: 0px;
}

.min-h-16 {
  min-height: 4rem;
}

.min-h-\[100px\] {
  min-height: 100px;
}

.min-h-\[180px\] {
  min-height: 180px;
}

.min-h-\[400px\] {
  min-height: 400px;
}

.min-h-\[4px\] {
  min-height: 4px;
}

.min-h-\[80px\] {
  min-height: 80px;
}

.min-h-\[90px\] {
  min-height: 90px;
}

.min-h-screen {
  min-height: 100vh;
}

.w-0 {
  width: 0px;
}

.w-1 {
  width: 0.25rem;
}

.w-1\.5 {
  width: 0.375rem;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.333333%;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-12 {
  width: 3rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: 0.5rem;
}

.w-2\/3 {
  width: 66.666667%;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-3 {
  width: 0.75rem;
}

.w-3\.5 {
  width: 0.875rem;
}

.w-3\/4 {
  width: 75%;
}

.w-32 {
  width: 8rem;
}

.w-36 {
  width: 9rem;
}

.w-4 {
  width: 1rem;
}

.w-40 {
  width: 10rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.w-64 {
  width: 16rem;
}

.w-7 {
  width: 1.75rem;
}

.w-8 {
  width: 2rem;
}

.w-80 {
  width: 20rem;
}

.w-9 {
  width: 2.25rem;
}

.w-\[calc\(100vw-2rem\)\] {
  width: calc(100vw - 2rem);
}

.w-auto {
  width: auto;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.w-px {
  width: 1px;
}

.min-w-0 {
  min-width: 0px;
}

.min-w-\[140px\] {
  min-width: 140px;
}

.min-w-\[160px\] {
  min-width: 160px;
}

.min-w-\[200px\] {
  min-width: 200px;
}

.min-w-\[80px\] {
  min-width: 80px;
}

.min-w-\[8rem\] {
  min-width: 8rem;
}

.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-32 {
  max-width: 8rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.max-w-\[300px\] {
  max-width: 300px;
}

.max-w-\[calc\(100\%-2rem\)\] {
  max-width: calc(100% - 2rem);
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-none {
  max-width: none;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink-0 {
  flex-shrink: 0;
}

.grow {
  flex-grow: 1;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-5 {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[calc\(-50\%_-_2px\)\] {
  --tw-translate-y: calc(-50% - 2px);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-3 {
  --tw-rotate: 3deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes ping {

  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes pulse {

  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.cursor-default {
  cursor: default;
}

.cursor-grab {
  cursor: grab;
}

.cursor-grabbing {
  cursor: grabbing;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.select-all {
  -webkit-user-select: all;
     -moz-user-select: all;
          user-select: all;
}

.resize-none {
  resize: none;
}

.resize {
  resize: both;
}

.scroll-my-1 {
  scroll-margin-top: 0.25rem;
  scroll-margin-bottom: 0.25rem;
}

.list-disc {
  list-style-type: disc;
}

.auto-rows-min {
  grid-auto-rows: min-content;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-rows-\[auto_auto\] {
  grid-template-rows: auto auto;
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.gap-0\.5 {
  gap: 0.125rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-1\.5 {
  gap: 0.375rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.375rem * var(--tw-space-x-reverse));
  margin-left: calc(0.375rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.self-start {
  align-self: flex-start;
}

.justify-self-end {
  justify-self: end;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.text-balance {
  text-wrap: balance;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-\[2px\] {
  border-radius: 2px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: var(--radius);
}

.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}

.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-t {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-r {
  border-right-width: 1px;
}

.border-r-4 {
  border-right-width: 4px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-4 {
  border-top-width: 4px;
}

.border-dashed {
  border-style: dashed;
}

.border-amber-200 {
  --tw-border-opacity: 1;
  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));
}

.border-archive\/20 {
  border-color: hsl(var(--archive) / 0.2);
}

.border-area {
  border-color: hsl(var(--area));
}

.border-area\/20 {
  border-color: hsl(var(--area) / 0.2);
}

.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}

.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}

.border-blue-300\/70 {
  border-color: rgb(147 197 253 / 0.7);
}

.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}

.border-border {
  border-color: hsl(var(--border));
}

.border-border\/40 {
  border-color: hsl(var(--border) / 0.4);
}

.border-border\/50 {
  border-color: hsl(var(--border) / 0.5);
}

.border-current {
  border-color: currentColor;
}

.border-destructive\/20 {
  border-color: hsl(var(--destructive) / 0.2);
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}

.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}

.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.border-green-600 {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}

.border-input {
  border-color: hsl(var(--input));
}

.border-muted {
  border-color: hsl(var(--muted));
}

.border-muted-foreground\/30 {
  border-color: hsl(var(--muted-foreground) / 0.3);
}

.border-orange-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));
}

.border-orange-300 {
  --tw-border-opacity: 1;
  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));
}

.border-primary {
  border-color: hsl(var(--primary));
}

.border-primary\/20 {
  border-color: hsl(var(--primary) / 0.2);
}

.border-primary\/50 {
  border-color: hsl(var(--primary) / 0.5);
}

.border-project {
  border-color: hsl(var(--project));
}

.border-project\/20 {
  border-color: hsl(var(--project) / 0.2);
}

.border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}

.border-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-resource\/20 {
  border-color: hsl(var(--resource) / 0.2);
}

.border-sidebar {
  border-color: hsl(var(--sidebar));
}

.border-sidebar-border {
  border-color: hsl(var(--sidebar-border));
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}

.border-yellow-400 {
  --tw-border-opacity: 1;
  border-color: rgb(250 204 21 / var(--tw-border-opacity, 1));
}

.border-yellow-500 {
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}

.border-l-blue-400 {
  --tw-border-opacity: 1;
  border-left-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.border-l-blue-500 {
  --tw-border-opacity: 1;
  border-left-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.border-l-green-500 {
  --tw-border-opacity: 1;
  border-left-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.border-l-orange-500 {
  --tw-border-opacity: 1;
  border-left-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}

.border-l-project {
  border-left-color: hsl(var(--project));
}

.border-l-purple-500 {
  --tw-border-opacity: 1;
  border-left-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.border-t-background {
  border-top-color: hsl(var(--background));
}

.border-t-blue-400 {
  --tw-border-opacity: 1;
  border-top-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.border-t-blue-600 {
  --tw-border-opacity: 1;
  border-top-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}

.border-t-border {
  border-top-color: hsl(var(--border));
}

.border-t-gray-400 {
  --tw-border-opacity: 1;
  border-top-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.border-t-orange-400 {
  --tw-border-opacity: 1;
  border-top-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}

.border-t-popover {
  border-top-color: hsl(var(--popover));
}

.border-t-transparent {
  border-top-color: transparent;
}

.border-t-yellow-400 {
  --tw-border-opacity: 1;
  border-top-color: rgb(250 204 21 / var(--tw-border-opacity, 1));
}

.bg-accent {
  background-color: hsl(var(--accent));
}

.bg-accent\/20 {
  background-color: hsl(var(--accent) / 0.2);
}

.bg-accent\/50 {
  background-color: hsl(var(--accent) / 0.5);
}

.bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}

.bg-archive\/10 {
  background-color: hsl(var(--archive) / 0.1);
}

.bg-area {
  background-color: hsl(var(--area));
}

.bg-area\/10 {
  background-color: hsl(var(--area) / 0.1);
}

.bg-background {
  background-color: hsl(var(--background));
}

.bg-background\/50 {
  background-color: hsl(var(--background) / 0.5);
}

.bg-background\/80 {
  background-color: hsl(var(--background) / 0.8);
}

.bg-background\/95 {
  background-color: hsl(var(--background) / 0.95);
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}

.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.bg-blue-50\/30 {
  background-color: rgb(239 246 255 / 0.3);
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-blue-500\/20 {
  background-color: rgb(59 130 246 / 0.2);
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.bg-border {
  background-color: hsl(var(--border));
}

.bg-card {
  background-color: hsl(var(--card));
}

.bg-card\/50 {
  background-color: hsl(var(--card) / 0.5);
}

.bg-current {
  background-color: currentColor;
}

.bg-destructive {
  background-color: hsl(var(--destructive));
}

.bg-destructive\/10 {
  background-color: hsl(var(--destructive) / 0.1);
}

.bg-destructive\/5 {
  background-color: hsl(var(--destructive) / 0.05);
}

.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.bg-gray-500\/20 {
  background-color: rgb(107 114 128 / 0.2);
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}

.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-green-500\/20 {
  background-color: rgb(34 197 94 / 0.2);
}

.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.bg-input {
  background-color: hsl(var(--input));
}

.bg-muted {
  background-color: hsl(var(--muted));
}

.bg-muted\/20 {
  background-color: hsl(var(--muted) / 0.2);
}

.bg-muted\/30 {
  background-color: hsl(var(--muted) / 0.3);
}

.bg-muted\/50 {
  background-color: hsl(var(--muted) / 0.5);
}

.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}

.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}

.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.bg-orange-500\/20 {
  background-color: rgb(249 115 22 / 0.2);
}

.bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}

.bg-popover {
  background-color: hsl(var(--popover));
}

.bg-primary {
  background-color: hsl(var(--primary));
}

.bg-primary\/10 {
  background-color: hsl(var(--primary) / 0.1);
}

.bg-primary\/20 {
  background-color: hsl(var(--primary) / 0.2);
}

.bg-primary\/5 {
  background-color: hsl(var(--primary) / 0.05);
}

.bg-project {
  background-color: hsl(var(--project));
}

.bg-project\/10 {
  background-color: hsl(var(--project) / 0.1);
}

.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}

.bg-purple-500\/20 {
  background-color: rgb(168 85 247 / 0.2);
}

.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.bg-red-50\/50 {
  background-color: rgb(254 242 242 / 0.5);
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-red-500\/20 {
  background-color: rgb(239 68 68 / 0.2);
}

.bg-resource\/10 {
  background-color: hsl(var(--resource) / 0.1);
}

.bg-secondary {
  background-color: hsl(var(--secondary));
}

.bg-sidebar {
  background-color: hsl(var(--sidebar));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}

.bg-white\/95 {
  background-color: rgb(255 255 255 / 0.95);
}

.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}

.bg-yellow-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));
}

.bg-yellow-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}

.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500\/20 {
  background-color: rgb(234 179 8 / 0.2);
}

.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-background {
  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-50 {
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-50\/70 {
  --tw-gradient-from: rgb(239 246 255 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-50 {
  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-muted\/30 {
  --tw-gradient-from: hsl(var(--muted) / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-50 {
  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-yellow-50 {
  --tw-gradient-from: #fefce8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 252 232 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-blue-300\/50 {
  --tw-gradient-to: rgb(147 197 253 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(147 197 253 / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-blue-400\/60 {
  --tw-gradient-to: rgb(96 165 250 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(96 165 250 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-purple-50\/50 {
  --tw-gradient-to: rgb(250 245 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(250 245 255 / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-blue-50 {
  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);
}

.to-emerald-50 {
  --tw-gradient-to: #ecfdf5 var(--tw-gradient-to-position);
}

.to-indigo-50 {
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}

.to-muted\/10 {
  --tw-gradient-to: hsl(var(--muted) / 0.1) var(--tw-gradient-to-position);
}

.to-muted\/20 {
  --tw-gradient-to: hsl(var(--muted) / 0.2) var(--tw-gradient-to-position);
}

.to-orange-50 {
  --tw-gradient-to: #fff7ed var(--tw-gradient-to-position);
}

.to-pink-50 {
  --tw-gradient-to: #fdf2f8 var(--tw-gradient-to-position);
}

.to-pink-50\/70 {
  --tw-gradient-to: rgb(253 242 248 / 0.7) var(--tw-gradient-to-position);
}

.to-purple-50 {
  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);
}

.to-purple-500 {
  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}

.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.fill-current {
  fill: currentColor;
}

.fill-muted-foreground {
  fill: hsl(var(--muted-foreground));
}

.fill-primary {
  fill: hsl(var(--primary));
}

.p-0 {
  padding: 0px;
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.p-\[3px\] {
  padding: 3px;
}

.px-0\.5 {
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-14 {
  padding-left: 3.5rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pr-12 {
  padding-right: 3rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-20 {
  padding-right: 5rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pt-0 {
  padding-top: 0px;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.font-mono {
  font-family: JetBrains Mono, Fira Code, Monaco, Consolas, Liberation Mono, Courier New, monospace;
}

.font-sans {
  font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-\[10px\] {
  font-size: 10px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.leading-none {
  line-height: 1;
}

.leading-relaxed {
  line-height: 1.625;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

.text-accent-foreground {
  color: hsl(var(--accent-foreground));
}

.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}

.text-amber-600 {
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}

.text-amber-700 {
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}

.text-amber-800 {
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}

.text-archive {
  color: hsl(var(--archive));
}

.text-area {
  color: hsl(var(--area));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-blue-300 {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.text-card-foreground {
  color: hsl(var(--card-foreground));
}

.text-current {
  color: currentColor;
}

.text-cyan-400 {
  --tw-text-opacity: 1;
  color: rgb(34 211 238 / var(--tw-text-opacity, 1));
}

.text-destructive {
  color: hsl(var(--destructive));
}

.text-destructive\/80 {
  color: hsl(var(--destructive) / 0.8);
}

.text-emerald-400 {
  --tw-text-opacity: 1;
  color: rgb(52 211 153 / var(--tw-text-opacity, 1));
}

.text-emerald-600 {
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity, 1));
}

.text-foreground {
  color: hsl(var(--foreground));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-green-300 {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.text-green-900 {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}

.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.text-muted-foreground\/20 {
  color: hsl(var(--muted-foreground) / 0.2);
}

.text-muted-foreground\/40 {
  color: hsl(var(--muted-foreground) / 0.4);
}

.text-muted-foreground\/60 {
  color: hsl(var(--muted-foreground) / 0.6);
}

.text-muted-foreground\/70 {
  color: hsl(var(--muted-foreground) / 0.7);
}

.text-muted-foreground\/80 {
  color: hsl(var(--muted-foreground) / 0.8);
}

.text-muted\/20 {
  color: hsl(var(--muted) / 0.2);
}

.text-orange-300 {
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}

.text-orange-400 {
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}

.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}

.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}

.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}

.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}

.text-primary {
  color: hsl(var(--primary));
}

.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.text-primary\/80 {
  color: hsl(var(--primary) / 0.8);
}

.text-project {
  color: hsl(var(--project));
}

.text-purple-300 {
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}

.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}

.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}

.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}

.text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.text-red-900 {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}

.text-resource {
  color: hsl(var(--resource));
}

.text-rose-400 {
  --tw-text-opacity: 1;
  color: rgb(251 113 133 / var(--tw-text-opacity, 1));
}

.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}

.text-sidebar-foreground {
  color: hsl(var(--sidebar-foreground));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-yellow-300 {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}

.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}

.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}

.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.text-yellow-900 {
  --tw-text-opacity: 1;
  color: rgb(113 63 18 / var(--tw-text-opacity, 1));
}

.underline {
  text-decoration-line: underline;
}

.line-through {
  text-decoration-line: line-through;
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

.placeholder-gray-500::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}

.placeholder-gray-500::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-20 {
  opacity: 0.2;
}

.opacity-30 {
  opacity: 0.3;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-70 {
  opacity: 0.7;
}

.opacity-75 {
  opacity: 0.75;
}

.opacity-80 {
  opacity: 0.8;
}

.opacity-90 {
  opacity: 0.9;
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-blue-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(191 219 254 / var(--tw-ring-opacity, 1));
}

.ring-blue-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(96 165 250 / var(--tw-ring-opacity, 1));
}

.ring-blue-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.ring-primary {
  --tw-ring-color: hsl(var(--primary));
}

.ring-opacity-70 {
  --tw-ring-opacity: 0.7;
}

.ring-offset-1 {
  --tw-ring-offset-width: 1px;
}

.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}

.\!filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur {
  --tw-backdrop-blur: blur(8px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-xl {
  --tw-backdrop-blur: blur(24px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-filter {
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[color\2c box-shadow\] {
  transition-property: color,box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-100 {
  transition-duration: 100ms;
}

.duration-1000 {
  transition-duration: 1000ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

@keyframes enter {

  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {

  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

.animate-in {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.fade-in-0 {
  --tw-enter-opacity: 0;
}

.zoom-in-95 {
  --tw-enter-scale: .95;
}

.slide-in-from-right-full {
  --tw-enter-translate-x: 100%;
}

.duration-100 {
  animation-duration: 100ms;
}

.duration-1000 {
  animation-duration: 1000ms;
}

.duration-150 {
  animation-duration: 150ms;
}

.duration-200 {
  animation-duration: 200ms;
}

.duration-300 {
  animation-duration: 300ms;
}

.duration-500 {
  animation-duration: 500ms;
}

.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out {
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.paused {
  animation-play-state: paused;
}

/* Text Utilities */

.text-balance {
    text-wrap: balance;
  }

/* Interaction Utilities */

/* Layout Utilities */

.center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

/* Quick Action Button Styles */

/* Milkdown 主题和样式 - 必须在 @tailwind base 之后导入 */

/* Milkdown编辑器自定义样式 */

.markdown-editor-container .milkdown {
  height: 100%;
  border: none;
  outline: none;
}

.markdown-editor-container .milkdown .editor {
  height: 100%;
  padding: 1rem;
}

/* 确保编辑器内容区域正确显示 */

.markdown-editor-container .ProseMirror {
  height: 100%;
  outline: none;
  padding: 1rem;
  font-size: 14px;
  line-height: 1.6;
}

/* Keyframe Animations */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  70% {
    transform: scale(0.9);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Drag and Drop Styles */

.drop-target {
  position: relative;
}

.drop-target::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 2px;
  opacity: 0.8;
  animation: pulse 1.5s ease-in-out infinite;
}

.drag-overlay {
  pointer-events: none;
  transform-origin: center;
  animation: dragFloat 0.3s ease-out;
}

@keyframes dragFloat {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(1.05) rotate(3deg);
    opacity: 0.9;
  }
}

/* Drag handle hover effect */

.group:hover .drag-handle {
  opacity: 1;
}

/* Drop zone indicator */

.drop-zone-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background: #3b82f6;
  border-radius: 1px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.drop-zone-indicator.active {
  opacity: 1;
}

/* Hide scrollbar */

.scrollbar-hide {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

/* ===== Vditor Editor Styles ===== */

/* Vditor 容器基础样式 */

.vditor {
  border-radius: var(--radius);
  border-width: 1px;
  border-color: hsl(var(--border));
  background-color: hsl(var(--background));
  font-family: inherit;
}

/* Vditor 工具栏样式 */

.vditor-toolbar {
  border-bottom-width: 1px;
  border-color: hsl(var(--border));
  background-color: hsl(var(--muted) / 0.5);
  border-radius: calc(var(--radius) - 2px) calc(var(--radius) - 2px) 0 0;
}

.vditor-toolbar .vditor-toolbar__item {
  color: hsl(var(--muted-foreground));
}

.vditor-toolbar .vditor-toolbar__item:hover {
  background-color: hsl(var(--accent) / 0.5);
  color: hsl(var(--foreground));
}

.vditor-toolbar .vditor-toolbar__item {
  border-radius: calc(var(--radius) - 4px);
  transition: all 0.2s ease;
}

.vditor-toolbar .vditor-toolbar__item--current {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.vditor-toolbar .vditor-toolbar__divider {
  background-color: hsl(var(--border));
}

/* Vditor 编辑区域样式 */

.vditor-content {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

.vditor-ir {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-mono), 'JetBrains Mono', 'Fira Code', monospace;
}

.vditor-wysiwyg {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

.vditor-sv {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-mono), 'JetBrains Mono', 'Fira Code', monospace;
}

/* Vditor 预览区域样式 */

.vditor-preview {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

.vditor-preview .vditor-reset {
  color: hsl(var(--foreground));
}

/* Vditor 分割线样式 */

.vditor-resize {
  background-color: hsl(var(--border));
}

.vditor-resize:hover {
  background-color: hsl(var(--accent));
}

/* Vditor 状态栏样式 */

.vditor-counter {
  background-color: hsl(var(--muted) / 0.3);
  color: hsl(var(--muted-foreground));
  border-top: 1px solid hsl(var(--border));
}

/* Vditor 提示框样式 */

.vditor-hint {
  border-width: 1px;
  border-color: hsl(var(--border));
  background-color: hsl(var(--popover));
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  border-radius: var(--radius);
}

.vditor-hint .vditor-hint__item {
  color: hsl(var(--popover-foreground));
}

.vditor-hint .vditor-hint__item:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.vditor-hint .vditor-hint__item--current {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* Vditor 深色主题特殊适配 */

.dark .vditor {
  border-color: hsl(var(--border));
  background-color: hsl(var(--background));
}

.dark .vditor-toolbar {
  background-color: hsl(var(--muted) / 0.5);
  border-bottom-color: hsl(var(--border));
}

.dark .vditor-content,
.dark .vditor-ir,
.dark .vditor-wysiwyg,
.dark .vditor-sv {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

.dark .vditor-preview {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Vditor 内容区域的 Markdown 样式 */

.vditor-reset h1,
.vditor-reset h2,
.vditor-reset h3,
.vditor-reset h4,
.vditor-reset h5,
.vditor-reset h6 {
  font-weight: 600;
  color: hsl(var(--foreground));
}

.vditor-reset h1 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.vditor-reset h2 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-size: 1.5rem;
  line-height: 2rem;
}

.vditor-reset h3 {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.vditor-reset h4 {
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.vditor-reset h5 {
  margin-top: 0.5rem;
  margin-bottom: 0.25rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

.vditor-reset h6 {
  margin-top: 0.5rem;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.vditor-reset p {
  margin-bottom: 1rem;
  line-height: 1.625;
  color: hsl(var(--foreground));
}

.vditor-reset blockquote {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border-left-width: 4px;
  border-color: hsl(var(--primary) / 0.3);
  background-color: hsl(var(--muted) / 0.5);
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.vditor-reset code {
  border-radius: 0.25rem;
  background-color: hsl(var(--muted));
  padding-left: 0.375rem;
  padding-right: 0.375rem;
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
  font-family: JetBrains Mono, Fira Code, Monaco, Consolas, Liberation Mono, Courier New, monospace;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: hsl(var(--foreground));
}

.vditor-reset pre {
  margin-top: 1rem;
  margin-bottom: 1rem;
  overflow-x: auto;
  border-radius: var(--radius);
  background-color: hsl(var(--muted));
  padding: 1rem;
}

.vditor-reset pre code {
  background-color: transparent;
  padding: 0px;
}

.vditor-reset a {
  color: hsl(var(--primary));
}

.vditor-reset a:hover {
  color: hsl(var(--primary) / 0.8);
  text-decoration-line: underline;
}

.vditor-reset ul,
.vditor-reset ol {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.vditor-reset ul > :not([hidden]) ~ :not([hidden]),
.vditor-reset ol > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.vditor-reset li {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.vditor-reset table {
  margin-top: 1rem;
  margin-bottom: 1rem;
  min-width: 100%;
  border-radius: var(--radius);
  border-width: 1px;
  border-color: hsl(var(--border));
}

.vditor-reset th {
  background-color: hsl(var(--muted) / 0.5);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: left;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: hsl(var(--muted-foreground));
}

.vditor-reset td {
  border-top-width: 1px;
  border-color: hsl(var(--border));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: hsl(var(--foreground));
}

.vditor-reset tr:hover {
  background-color: hsl(var(--muted) / 0.3);
}

/* ===== Milkdown Editor Theme Styles ===== */

/* 编辑器主题样式 */

.milkdown-editor {
  height: 100%;
  width: 100%;
}

/* 经典主题 */

.editor-theme-classic {
  --crepe-color-primary: hsl(var(--primary));
  --crepe-color-background: hsl(var(--background));
  --crepe-color-text: hsl(var(--foreground));
  --crepe-color-border: hsl(var(--border));
  --crepe-color-muted: hsl(var(--muted));
}

.editor-theme-classic .milkdown {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
}

/* 深色主题 */

.editor-theme-dark {
  --crepe-color-primary: #60a5fa;
  --crepe-color-background: #1e293b;
  --crepe-color-text: #f1f5f9;
  --crepe-color-border: #334155;
  --crepe-color-muted: #475569;
}

.editor-theme-dark .milkdown {
  background-color: #1e293b;
  color: #f1f5f9;
  border: 1px solid #334155;
  border-radius: 8px;
}

/* 专注模式 */

.editor-focus-mode .milkdown .crepe-toolbar {
  display: none !important;
}

.editor-focus-mode .milkdown {
  border: none !important;
  box-shadow: none !important;
}

.editor-focus-mode {
  --crepe-color-border: transparent;
}

/* WikiLink 样式 */

.wiki-link {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  margin: 0 1px;
  background-color: hsl(var(--primary) / 0.1);
  border: 1px solid hsl(var(--primary) / 0.3);
  border-radius: 4px;
  color: hsl(var(--primary));
  text-decoration: none;
  font-size: 0.9em;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.wiki-link:hover {
  background-color: hsl(var(--primary) / 0.2);
  border-color: hsl(var(--primary) / 0.4);
}

.link-text {
  text-decoration: none;
  font-weight: 500;
}

/* 自定义滚动条样式 */

.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(209 213 219);
  border-radius: 3px;
  -webkit-transition: background-color 0.2s ease;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgb(156 163 175);
}

/* 隐藏滚动条但保持滚动功能 */

.scrollbar-hidden {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

/* 灵感记录窗口专用样式 */

.inspiration-window {
  overflow: hidden;
}

.inspiration-window * {
  box-sizing: border-box;
}

/* Gamma 风格：角落彩带渐变与按钮描边动画 */

.inspiration-ribbon {
  position: absolute;
  pointer-events: none;
  inset: 0;
}

.inspiration-ribbon::before,
.inspiration-ribbon::after {
  content: "";
  position: absolute;
  width: 160px;
  height: 160px;
  filter: blur(32px);
  opacity: 0.08;
  border-radius: 50%;
}

/* 左下角彩带 */

.inspiration-ribbon::before {
  left: -40px;
  bottom: -40px;
  background: conic-gradient(from 220deg, hsl(var(--primary) / 0.8), hsl(var(--accent) / 0.8), hsl(var(--primary) / 0.8));
}

/* 右上角彩带 */

.inspiration-ribbon::after {
  right: -40px;
  top: -40px;
  background: conic-gradient(from 40deg, hsl(var(--warning) / 0.8), hsl(var(--success) / 0.8), hsl(var(--warning) / 0.8));
}

/* 霓虹微光按钮（非动画，提升可读性与层次） */

.btn-neon {
  box-shadow: 0 0 0 1px hsl(var(--primary) / 0.35), 0 6px 16px rgba(0,0,0,0.15);
}

/* 标题与 ESC 提示渐显 */

@keyframes rise-fade {
  from { transform: translateY(4px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.rise-fade { animation: rise-fade 240ms ease-out both; }

/* ===== Resources Page Layout Helpers ===== */

/* 高度 = 窗口高度 - 标题栏高度 - 上下 padding；支持上层覆写变量值 */

.resources-layout {
  height: calc(100vh - var(--titlebar-h, 32px) - var(--content-pt, 24px) - var(--content-pb, 24px));
  overflow: hidden; /* 禁止页面级别滚动 */
}

/* 强制浅色编辑器区域（不随全局暗色主题改变） */

.force-light-editor .milkdown {
  background-color: #ffffff !important;
  color: #111827 !important; /* tailwind gray-900 */
}

.force-light-editor .milkdown .editor {
  background-color: #ffffff !important;
}

/* 确保编辑器容器正确填充高度并处理滚动 */

.markdown-editor-container {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.markdown-editor-container .milkdown-editor {
  height: 100% !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.markdown-editor-container .milkdown {
  height: 100% !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.markdown-editor-container .milkdown .editor {
  height: 100% !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* 强制覆盖 Crepe 的默认样式 */

.markdown-editor-container .milkdown .crepe {
  height: 100% !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.markdown-editor-container .milkdown .crepe-editor {
  height: 100% !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* 编辑器内容区域滚动设置 - 隐藏滚动条 */

.markdown-editor-container .milkdown .editor .ProseMirror {
  flex: 1 1 auto !important;
  min-height: 0 !important;
  height: auto !important;
  overflow-y: auto !important;
  overscroll-behavior: contain !important;
  /* 隐藏滚动条 */
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

.markdown-editor-container .milkdown .editor .ProseMirror::-webkit-scrollbar {
  display: none !important; /* Chrome, Safari, Opera */
}

/* 强制覆盖任何可能影响高度的 Crepe 样式 */

.markdown-editor-container .milkdown .crepe .ProseMirror {
  flex: 1 1 auto !important;
  min-height: 0 !important;
  height: auto !important;
  overflow-y: auto !important;
  overscroll-behavior: contain !important;
  /* 隐藏滚动条 */
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.markdown-editor-container .milkdown .crepe .ProseMirror::-webkit-scrollbar {
  display: none !important;
}

/* 确保工具栏不影响编辑器高度 */

.markdown-editor-container .milkdown .toolbar {
  flex-shrink: 0 !important;
}

/* 确保编辑器主体区域占据剩余空间 */

.markdown-editor-container .milkdown .editor-body,
.markdown-editor-container .milkdown .crepe-body {
  flex: 1 1 auto !important;
  min-height: 0 !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 强制编辑器固定高度的辅助类 */

.editor-fixed-height {
  height: 100% !important;
  min-height: 0 !important;
  max-height: 100% !important;
}

/* 针对Milkdown Crepe的具体DOM结构进行强制样式设置 */

.markdown-editor-container [data-milkdown-root] {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.markdown-editor-container [data-milkdown-root] > div {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* 强制设置所有可能的编辑器容器 */

.markdown-editor-container .milkdown-container,
.markdown-editor-container .crepe-container,
.markdown-editor-container .editor-container {
  height: 100% !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* 确保编辑区域占据剩余空间并可滚动 */

.markdown-editor-container .ProseMirror,
.markdown-editor-container [contenteditable="true"] {
  flex: 1 1 auto !important;
  min-height: 0 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  height: auto !important;
  /* 隐藏滚动条 */
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.markdown-editor-container .ProseMirror::-webkit-scrollbar,
.markdown-editor-container [contenteditable="true"]::-webkit-scrollbar {
  display: none !important;
}

/* 防止任何子元素破坏布局 */

.markdown-editor-container * {
  box-sizing: border-box !important;
}

/* 最激进的方法：强制覆盖所有可能的编辑器样式 */

.markdown-editor-container .milkdown,
.markdown-editor-container .crepe,
.markdown-editor-container [data-milkdown-root],
.markdown-editor-container .editor,
.markdown-editor-container .milkdown-container,
.markdown-editor-container .crepe-container {
  height: 100% !important;
  max-height: 100% !important;
  min-height: 0 !important;
  flex: 1 1 auto !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* 强制编辑区域样式 - 关键修复：确保滚动功能正常 */

.markdown-editor-container .ProseMirror,
.markdown-editor-container [contenteditable="true"],
.markdown-editor-container .editor-content,
.markdown-editor-container .prosemirror-editor {
  /* 使用明确的高度计算，避免flex导致的高度问题 */
  height: calc(100vh - 200px) !important; /* 临时固定高度，将被JS动态设置 */
  min-height: 300px !important;
  max-height: calc(100vh - 200px) !important;

  /* 强制启用滚动功能 */
  overflow: auto !important;
  overflow-y: scroll !important;
  overflow-x: hidden !important;
  overscroll-behavior: contain !important;
  -webkit-overflow-scrolling: touch !important; /* iOS滚动优化 */

  position: relative !important;
  /* 确保正确的坐标计算 */
  box-sizing: border-box !important;
  padding: 16px !important;

  /* 确保内容可以超出容器 */
  word-wrap: break-word !important;
  white-space: pre-wrap !important;

  /* 强制隐藏滚动条但保持滚动功能 */
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.markdown-editor-container .ProseMirror::-webkit-scrollbar,
.markdown-editor-container [contenteditable="true"]::-webkit-scrollbar,
.markdown-editor-container .editor-content::-webkit-scrollbar,
.markdown-editor-container .prosemirror-editor::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* 强制启用滚动功能 - 移除可能阻止滚动的样式 */

.markdown-editor-container .ProseMirror {
  resize: none !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;

  /* 确保滚动功能正常工作 */
  touch-action: pan-y !important; /* 允许垂直滚动 */
  pointer-events: auto !important; /* 确保可以交互 */
  -webkit-user-select: text !important;
     -moz-user-select: text !important;
          user-select: text !important; /* 允许文本选择 */
}

/* 移除可能阻止滚动的父容器样式 */

.markdown-editor-container .milkdown .editor {
  overflow: hidden !important; /* 让子元素ProseMirror处理滚动 */
}

/* 确保编辑器容器不阻止滚动 */

.markdown-editor-container .milkdown {
  overflow: hidden !important; /* 让子元素ProseMirror处理滚动 */
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: hsl(var(--foreground));
  background-color: hsl(var(--background));
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

#root {
  min-height: 100vh;
  width: 100%;
}

.logo {
  margin-bottom: 20px;
  -webkit-user-drag: none;
  height: 128px;
  width: 128px;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 1.2em #6988e6aa);
}

.creator {
  font-size: 14px;
  line-height: 16px;
  color: var(--ev-c-text-2);
  font-weight: 600;
  margin-bottom: 10px;
}

.text {
  font-size: 28px;
  color: var(--ev-c-text-1);
  font-weight: 700;
  line-height: 32px;
  text-align: center;
  margin: 0 10px;
  padding: 16px 0;
}

.tip {
  font-size: 16px;
  line-height: 24px;
  color: var(--ev-c-text-2);
  font-weight: 600;
}

.react {
  background: -webkit-linear-gradient(315deg, #087ea4 55%, #7c93ee);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.ts {
  background: -webkit-linear-gradient(315deg, #3178c6 45%, #f0dc4e);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.actions {
  display: flex;
  padding-top: 32px;
  margin: -6px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.action {
  flex-shrink: 0;
  padding: 6px;
}

.action a {
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  border: 1px solid transparent;
  text-align: center;
  font-weight: 600;
  white-space: nowrap;
  border-radius: 20px;
  padding: 0 20px;
  line-height: 38px;
  font-size: 14px;
  border-color: var(--ev-button-alt-border);
  color: var(--ev-button-alt-text);
  background-color: var(--ev-button-alt-bg);
}

.action a:hover {
  border-color: var(--ev-button-alt-hover-border);
  color: var(--ev-button-alt-hover-text);
  background-color: var(--ev-button-alt-hover-bg);
}

.versions {
  position: absolute;
  bottom: 30px;
  margin: 0 auto;
  padding: 15px 0;
  font-family: 'Menlo', 'Lucida Console', monospace;
  display: inline-flex;
  overflow: hidden;
  align-items: center;
  border-radius: 22px;
  background-color: #202127;
  backdrop-filter: blur(24px);
}

.versions li {
  display: block;
  float: left;
  border-right: 1px solid var(--ev-c-gray-1);
  padding: 0 20px;
  font-size: 14px;
  line-height: 14px;
  opacity: 0.8;
  &:last-child {
    border: none;
  }
}

@media (max-width: 720px) {
  .text {
    font-size: 20px;
  }
}

@media (max-width: 620px) {
  .versions {
    display: none;
  }
}

@media (max-width: 350px) {
  .tip,
  .actions {
    display: none;
  }
}

.selection\:bg-primary *::-moz-selection {
  background-color: hsl(var(--primary));
}

.selection\:bg-primary *::selection {
  background-color: hsl(var(--primary));
}

.selection\:text-primary-foreground *::-moz-selection {
  color: hsl(var(--primary-foreground));
}

.selection\:text-primary-foreground *::selection {
  color: hsl(var(--primary-foreground));
}

.selection\:bg-primary::-moz-selection {
  background-color: hsl(var(--primary));
}

.selection\:bg-primary::selection {
  background-color: hsl(var(--primary));
}

.selection\:text-primary-foreground::-moz-selection {
  color: hsl(var(--primary-foreground));
}

.selection\:text-primary-foreground::selection {
  color: hsl(var(--primary-foreground));
}

.file\:inline-flex::file-selector-button {
  display: inline-flex;
}

.file\:h-7::file-selector-button {
  height: 1.75rem;
}

.file\:border-0::file-selector-button {
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}

.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.file\:text-foreground::file-selector-button {
  color: hsl(var(--foreground));
}

.placeholder\:text-muted-foreground::-moz-placeholder {
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}

.first\:rounded-t-lg:first-child {
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}

.last\:rounded-b-lg:last-child {
  border-bottom-right-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:border-primary\/20:hover {
  border-color: hsl(var(--primary) / 0.2);
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:bg-accent\/50:hover {
  background-color: hsl(var(--accent) / 0.5);
}

.hover\:bg-black\/10:hover {
  background-color: rgb(0 0 0 / 0.1);
}

.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-destructive:hover {
  background-color: hsl(var(--destructive));
}

.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / 0.9);
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50\/80:hover {
  background-color: rgb(249 250 251 / 0.8);
}

.hover\:bg-green-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.hover\:bg-muted:hover {
  background-color: hsl(var(--muted));
}

.hover\:bg-muted\/30:hover {
  background-color: hsl(var(--muted) / 0.3);
}

.hover\:bg-muted\/50:hover {
  background-color: hsl(var(--muted) / 0.5);
}

.hover\:bg-orange-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary\/10:hover {
  background-color: hsl(var(--primary) / 0.1);
}

.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}

.hover\:bg-purple-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / 0.8);
}

.hover\:bg-yellow-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.hover\:text-destructive:hover {
  color: hsl(var(--destructive));
}

.hover\:text-destructive-foreground:hover {
  color: hsl(var(--destructive-foreground));
}

.hover\:text-foreground:hover {
  color: hsl(var(--foreground));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.hover\:text-green-600:hover {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-600:hover {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.hover\:text-primary:hover {
  color: hsl(var(--primary));
}

.hover\:text-primary\/80:hover {
  color: hsl(var(--primary) / 0.8);
}

.hover\:text-red-600:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.hover\:text-red-700:hover {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:opacity-80:hover {
  opacity: 0.8;
}

.hover\:opacity-90:hover {
  opacity: 0.9;
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-sm:hover {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:border-yellow-400:focus {
  --tw-border-opacity: 1;
  border-color: rgb(250 204 21 / var(--tw-border-opacity, 1));
}

.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}

.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}

.focus\:text-red-600:focus {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.focus\:placeholder-gray-400:focus::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.focus\:placeholder-gray-400:focus::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\:ring-primary:focus {
  --tw-ring-color: hsl(var(--primary));
}

.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-yellow-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 204 21 / var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-0:focus {
  --tw-ring-offset-width: 0px;
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\:ring-offset-background:focus {
  --tw-ring-offset-color: hsl(var(--background));
}

.focus-visible\:border-ring:focus-visible {
  border-color: hsl(var(--ring));
}

.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:outline-1:focus-visible {
  outline-width: 1px;
}

.focus-visible\:outline-ring:focus-visible {
  outline-color: hsl(var(--ring));
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-\[3px\]:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-destructive\/20:focus-visible {
  --tw-ring-color: hsl(var(--destructive) / 0.2);
}

.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-ring\/50:focus-visible {
  --tw-ring-color: hsl(var(--ring) / 0.5);
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:ring-offset-background:focus-visible {
  --tw-ring-offset-color: hsl(var(--background));
}

.active\:cursor-grabbing:active {
  cursor: grabbing;
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-50 {
  opacity: 0.5;
}

.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: 0.7;
}

.has-\[\>svg\]\:px-2\.5:has(>svg) {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.has-\[\>svg\]\:px-3:has(>svg) {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.has-\[\>svg\]\:px-4:has(>svg) {
  padding-left: 1rem;
  padding-right: 1rem;
}

.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}

.data-\[size\=default\]\:h-9[data-size="default"] {
  height: 2.25rem;
}

.data-\[size\=sm\]\:h-8[data-size="sm"] {
  height: 2rem;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=active\]\:bg-background[data-state="active"] {
  background-color: hsl(var(--background));
}

.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  background-color: hsl(var(--primary));
}

.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent));
}

.data-\[inset\]\:pl-8[data-inset] {
  padding-left: 2rem;
}

.data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
  color: hsl(var(--muted-foreground));
}

.data-\[state\=active\]\:text-foreground[data-state="active"] {
  color: hsl(var(--foreground));
}

.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
  color: hsl(var(--primary-foreground));
}

.data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
  color: hsl(var(--accent-foreground));
}

.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
  color: hsl(var(--muted-foreground));
}

.data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"] {
  color: hsl(var(--destructive));
}

.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}

.data-\[state\=active\]\:shadow-sm[data-state="active"] {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -0.5rem;
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: 0.5rem;
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -0.5rem;
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: 0.5rem;
}

.\*\:data-\[slot\=select-value\]\:line-clamp-1[data-slot="select-value"] > * {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.\*\:data-\[slot\=select-value\]\:flex[data-slot="select-value"] > * {
  display: flex;
}

.\*\:data-\[slot\=select-value\]\:items-center[data-slot="select-value"] > * {
  align-items: center;
}

.\*\:data-\[slot\=select-value\]\:gap-2[data-slot="select-value"] > * {
  gap: 0.5rem;
}

.data-\[variant\=destructive\]\:focus\:bg-destructive\/10:focus[data-variant="destructive"] {
  background-color: hsl(var(--destructive) / 0.1);
}

.data-\[variant\=destructive\]\:focus\:text-destructive:focus[data-variant="destructive"] {
  color: hsl(var(--destructive));
}

.group[data-disabled="true"] .group-data-\[disabled\=true\]\:pointer-events-none {
  pointer-events: none;
}

.group[data-disabled="true"] .group-data-\[disabled\=true\]\:opacity-50 {
  opacity: 0.5;
}

@supports (backdrop-filter: var(--tw)) {

  .supports-\[backdrop-filter\]\:bg-background\/60 {
    background-color: hsl(var(--background) / 0.6);
  }
}

.dark\:border-gray-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.dark\:border-input:is(.dark *) {
  border-color: hsl(var(--input));
}

.dark\:bg-destructive\/60:is(.dark *) {
  background-color: hsl(var(--destructive) / 0.6);
}

.dark\:bg-gray-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.dark\:bg-input\/30:is(.dark *) {
  background-color: hsl(var(--input) / 0.3);
}

.dark\:text-gray-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.dark\:text-green-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.dark\:text-muted-foreground:is(.dark *) {
  color: hsl(var(--muted-foreground));
}

.dark\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:hover\:bg-accent\/50:hover:is(.dark *) {
  background-color: hsl(var(--accent) / 0.5);
}

.dark\:hover\:bg-input\/50:hover:is(.dark *) {
  background-color: hsl(var(--input) / 0.5);
}

.dark\:focus-visible\:ring-destructive\/40:focus-visible:is(.dark *) {
  --tw-ring-color: hsl(var(--destructive) / 0.4);
}

.dark\:data-\[state\=active\]\:border-input[data-state="active"]:is(.dark *) {
  border-color: hsl(var(--input));
}

.dark\:data-\[state\=active\]\:bg-input\/30[data-state="active"]:is(.dark *) {
  background-color: hsl(var(--input) / 0.3);
}

.dark\:data-\[state\=active\]\:text-foreground[data-state="active"]:is(.dark *) {
  color: hsl(var(--foreground));
}

.dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:focus[data-variant="destructive"]:is(.dark *) {
  background-color: hsl(var(--destructive) / 0.2);
}

@media (min-width: 640px) {

  .sm\:inline {
    display: inline;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:w-36 {
    width: 9rem;
  }

  .sm\:w-40 {
    width: 10rem;
  }

  .sm\:w-64 {
    width: 16rem;
  }

  .sm\:w-\[400px\] {
    width: 400px;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:max-w-\[400px\] {
    max-width: 400px;
  }

  .sm\:max-w-\[425px\] {
    max-width: 425px;
  }

  .sm\:max-w-\[500px\] {
    max-width: 500px;
  }

  .sm\:max-w-\[600px\] {
    max-width: 600px;
  }

  .sm\:max-w-lg {
    max-width: 32rem;
  }

  .sm\:max-w-md {
    max-width: 28rem;
  }

  .sm\:max-w-xs {
    max-width: 20rem;
  }

  .sm\:flex-initial {
    flex: 0 1 auto;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:text-left {
    text-align: left;
  }
}

@media (min-width: 768px) {

  .md\:inset-8 {
    inset: 2rem;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:block {
    display: block;
  }

  .md\:hidden {
    display: none;
  }

  .md\:w-48 {
    width: 12rem;
  }

  .md\:w-96 {
    width: 24rem;
  }

  .md\:w-\[450px\] {
    width: 450px;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 1024px) {

  .lg\:inset-16 {
    inset: 4rem;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:p-8 {
    padding: 2rem;
  }
}

@media (min-width: 1280px) {

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.\[\&\>svg\]\:pointer-events-none>svg {
  pointer-events: none;
}

.\[\&\>svg\]\:size-3>svg {
  width: 0.75rem;
  height: 0.75rem;
}

.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*='size-']) {
  width: 1rem;
  height: 1rem;
}

.\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*='text-']) {
  color: hsl(var(--muted-foreground));
}

.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}

.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}

a.\[a\&\]\:hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

a.\[a\&\]\:hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / 0.9);
}

a.\[a\&\]\:hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}

a.\[a\&\]\:hover\:bg-secondary\/90:hover {
  background-color: hsl(var(--secondary) / 0.9);
}

a.\[a\&\]\:hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}
.ProseMirror {
  position: relative;
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror pre {
  white-space: pre-wrap;
}

.ProseMirror li {
  position: relative;
}

.ProseMirror-hideselection *::-moz-selection { background: transparent; }

.ProseMirror-hideselection *::selection { background: transparent; }

.ProseMirror-hideselection *::-moz-selection { background: transparent; }

.ProseMirror-hideselection { caret-color: transparent; }

/* See https://github.com/ProseMirror/prosemirror/issues/1421#issuecomment-1759320191 */

.ProseMirror [draggable][contenteditable=false] { -webkit-user-select: text; -moz-user-select: text; user-select: text }

.ProseMirror-selectednode {
  outline: 2px solid #8cf;
}

/* Make sure li selections wrap around markers */

li.ProseMirror-selectednode {
  outline: none;
}

li.ProseMirror-selectednode:after {
  content: "";
  position: absolute;
  left: -32px;
  right: -2px; top: -2px; bottom: -2px;
  border: 2px solid #8cf;
  pointer-events: none;
}

/* Protect against generic img rules */

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
}

.milkdown {
  position: relative;
}

.milkdown * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

.milkdown button,
  .milkdown input {
    border: none;
    background: none;
    box-shadow: none;
  }

.milkdown button:focus, .milkdown input:focus {
      outline: none;
    }

.milkdown :focus-visible {
    outline: none;
  }

.milkdown {

  font-family: var(--crepe-font-default);
  color: var(--crepe-color-on-background);
  background: var(--crepe-color-background);
}

.milkdown .milkdown-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

.milkdown .ProseMirror-focused {
    outline: none;
  }

.milkdown .ProseMirror {
    padding: 60px 120px;
  }

.milkdown .ProseMirror *::-moz-selection {
      background: var(--crepe-color-selected);
    }

.milkdown .ProseMirror *::selection {
      background: var(--crepe-color-selected);
    }

.milkdown .ProseMirror li.ProseMirror-selectednode {
      background: var(--crepe-color-selected);
      outline: none;
    }

.milkdown .ProseMirror li.ProseMirror-selectednode ::-moz-selection {
        background: transparent;
      }

.milkdown .ProseMirror li.ProseMirror-selectednode ::selection {
        background: transparent;
      }

.milkdown .ProseMirror li.ProseMirror-selectednode::-moz-selection {
        background: transparent;
      }

.milkdown .ProseMirror li.ProseMirror-selectednode::selection {
        background: transparent;
      }

.milkdown .ProseMirror li.ProseMirror-selectednode:after {
      all: unset;
    }

.milkdown .ProseMirror .ProseMirror-selectednode {
      background: var(--crepe-color-selected);
      outline: none;
      background: color-mix(
        in srgb,
        var(--crepe-color-selected),
        transparent 60%
      );
    }

.milkdown .ProseMirror .ProseMirror-selectednode ::-moz-selection {
        background: transparent;
      }

.milkdown .ProseMirror .ProseMirror-selectednode ::selection {
        background: transparent;
      }

.milkdown .ProseMirror .ProseMirror-selectednode::-moz-selection {
        background: transparent;
      }

.milkdown .ProseMirror .ProseMirror-selectednode::selection {
        background: transparent;
      }

.milkdown .ProseMirror[data-dragging='true']::-moz-selection, .milkdown .ProseMirror[data-dragging='true'] *::-moz-selection {
        background: transparent;
      }

.milkdown .ProseMirror[data-dragging='true'] .ProseMirror-selectednode,
      .milkdown .ProseMirror[data-dragging='true']::selection,
      .milkdown .ProseMirror[data-dragging='true'] *::selection {
        background: transparent;
      }

.milkdown .ProseMirror[data-dragging='true'] input::-moz-selection {
        background: var(--crepe-color-selected);
      }

.milkdown .ProseMirror[data-dragging='true'] input::selection {
        background: var(--crepe-color-selected);
      }

.milkdown .ProseMirror img {
      vertical-align: bottom;
      max-width: 100%;
    }

.milkdown .ProseMirror img.ProseMirror-selectednode {
        background: none;
        outline: 2px solid var(--crepe-color-primary);
      }

.milkdown .ProseMirror h1,
    .milkdown .ProseMirror h2,
    .milkdown .ProseMirror h3,
    .milkdown .ProseMirror h4,
    .milkdown .ProseMirror h5,
    .milkdown .ProseMirror h6 {
      font-family: var(--crepe-font-title);
      font-weight: 400;
      padding: 2px 0;
    }

.milkdown .ProseMirror h1 {
      font-size: 42px;
      line-height: 50px;
      margin-top: 32px;
    }

.milkdown .ProseMirror h2 {
      font-size: 36px;
      line-height: 44px;
      margin-top: 28px;
    }

.milkdown .ProseMirror h3 {
      font-size: 32px;
      line-height: 40px;
      margin-top: 24px;
    }

.milkdown .ProseMirror h4 {
      font-size: 28px;
      line-height: 36px;
      margin-top: 20px;
    }

.milkdown .ProseMirror h5 {
      font-size: 24px;
      line-height: 32px;
      margin-top: 16px;
    }

.milkdown .ProseMirror h6 {
      font-size: 18px;
      font-weight: 700;
      line-height: 28px;
      margin-top: 16px;
    }

.milkdown .ProseMirror p {
      font-size: 16px;
      line-height: 24px;
      padding: 4px 0;
    }

.milkdown .ProseMirror code {
      color: var(--crepe-color-inline-code);
      background: color-mix(
        in srgb,
        var(--crepe-color-inline-area),
        transparent 40%
      );
      font-family: var(--crepe-font-code);
      padding: 0 2px;
      border-radius: 4px;
      font-size: 87.5%;
      display: inline-block;
      line-height: 1.4286;
    }

.milkdown .ProseMirror a {
      color: var(--crepe-color-primary);
      text-decoration: underline;
    }

.milkdown .ProseMirror pre {
      background: color-mix(
        in srgb,
        var(--crepe-color-inline-area),
        transparent 40%
      );
      padding: 10px;
      border-radius: 4px;
    }

.milkdown .ProseMirror pre code {
        padding: 0;
        background: transparent;
      }

.milkdown .ProseMirror blockquote {
      position: relative;
      padding-left: 40px;
      padding-top: 0;
      padding-bottom: 0;
      box-sizing: content-box;
      margin: 4px 0;
    }

.milkdown .ProseMirror blockquote::before {
        content: '';
        width: 4px;
        left: 0;
        top: 4px;
        bottom: 4px;
        position: absolute;
        background: var(--crepe-color-selected);
        border-radius: 100px;
      }

.milkdown .ProseMirror blockquote hr {
        margin-bottom: 16px;
      }

.milkdown .ProseMirror hr {
      border: none;
      background-color: color-mix(
        in srgb,
        var(--crepe-color-outline),
        transparent 80%
      );
      background-clip: content-box;
      padding: 6px 0;
      height: 13px;
      position: relative;
    }

.milkdown .ProseMirror hr.ProseMirror-selectednode {
        outline: none;
        background-color: color-mix(
          in srgb,
          var(--crepe-color-outline),
          transparent 20%
        );
        background-clip: content-box;
      }

.milkdown .ProseMirror hr.ProseMirror-selectednode::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          right: 0;
          background-color: color-mix(
            in srgb,
            var(--crepe-color-outline),
            transparent 80%
          );
          pointer-events: none;
        }

.milkdown .ProseMirror ul,
    .milkdown .ProseMirror ol {
      padding: 0;
    }

.milkdown .milkdown-block-handle[data-show='false'] {
      opacity: 0;
      pointer-events: none;
    }

.milkdown .milkdown-block-handle {
    transition: all 0.2s;
    position: absolute;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
}

.milkdown .milkdown-block-handle .operation-item {
      border-radius: 4px;
      width: 32px;
      height: 32px;
      padding: 4px;
    }

.milkdown .milkdown-block-handle .operation-item svg {
        width: 24px;
        height: 24px;
        fill: var(--crepe-color-outline);
      }

.milkdown .milkdown-block-handle .operation-item:hover {
        background: var(--crepe-color-hover);
      }

.milkdown .milkdown-block-handle .operation-item.active {
        background: var(--crepe-color-selected);
      }

.milkdown .milkdown-slash-menu[data-show='false'] {
      display: none;
    }

.milkdown .milkdown-slash-menu {
    position: absolute;
    z-index: 10;
    display: block;
    font-family: var(--crepe-font-default);
    color: var(--crepe-color-on-surface);
    background: var(--crepe-color-surface);
    border-radius: 12px;
    box-shadow: var(--crepe-shadow-1);
}

.milkdown .milkdown-slash-menu ul {
      list-style-type: none;
    }

.milkdown .milkdown-slash-menu ul li {
        cursor: pointer;
        border-radius: 8px;
      }

.milkdown .milkdown-slash-menu .tab-group {
      border-bottom: 1px solid
        color-mix(in srgb, var(--crepe-color-outline), transparent 80%);
      padding: 12px 12px 0;
    }

.milkdown .milkdown-slash-menu .tab-group ul {
        padding: 8px 10px;
        display: flex;
        gap: 10px;
        flex-wrap: nowrap;
      }

.milkdown .milkdown-slash-menu .tab-group ul li {
          padding: 6px 10px;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 20px;
        }

.milkdown .milkdown-slash-menu .tab-group ul li:hover {
            background: var(--crepe-color-hover);
          }

.milkdown .milkdown-slash-menu .tab-group ul li.selected {
            background: var(--crepe-color-selected);
          }

.milkdown .milkdown-slash-menu .menu-groups {
      padding: 0 12px 12px;
      max-height: 420px;
      overflow: auto;
      overscroll-behavior: contain;
      scroll-behavior: smooth;
    }

.milkdown .milkdown-slash-menu .menu-groups .menu-group h6 {
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 20px;
          padding: 14px 10px;
          text-transform: uppercase;
          color: color-mix(
            in srgb,
            var(--crepe-color-on-surface),
            transparent 40%
          );
        }

.milkdown .milkdown-slash-menu .menu-groups .menu-group li {
          min-width: 220px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          gap: 16px;
          padding: 14px 10px;
        }

.milkdown .milkdown-slash-menu .menu-groups .menu-group li.hover {
            background: var(--crepe-color-hover);
          }

.milkdown .milkdown-slash-menu .menu-groups .menu-group li.active {
            background: var(--crepe-color-selected);
          }

.milkdown .milkdown-slash-menu .menu-groups .menu-group li svg {
            width: 24px;
            height: 24px;
            color: var(--crepe-color-outline);
            fill: var(--crepe-color-outline);
          }

.milkdown .milkdown-slash-menu .menu-groups .menu-group li > span {
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 20px;
          }

.milkdown .milkdown-slash-menu .menu-groups .menu-group + .menu-group::before {
        content: '';
        display: block;
        height: 1px;
        background: color-mix(
          in srgb,
          var(--crepe-color-outline),
          transparent 80%
        );
        margin: 0 10px;
      }

.milkdown .milkdown-code-block {
    display: block;
    position: relative;
    padding: 8px 20px 20px;
    background: var(--crepe-color-surface);
    margin: 4px 0;
  }

.milkdown .milkdown-code-block .language-picker {
      padding-top: 10px;
      width: -moz-max-content;
      width: max-content;
      position: absolute;
      z-index: 999;
    }

.milkdown .milkdown-code-block .hidden {
      display: none !important;
    }

.milkdown .milkdown-code-block.selected {
      outline: 1px solid var(--crepe-color-primary);
    }

.milkdown .milkdown-code-block .cm-editor {
      outline: none !important;
      background: var(--crepe-color-surface);
    }

.milkdown .milkdown-code-block .cm-gutters {
      border-right: none;
      background: var(--crepe-color-surface);
    }

.milkdown .milkdown-code-block .cm-panel {
      font-family: var(--crepe-font-default);
      background: var(--crepe-color-surface);
      color: var(--crepe-color-on-surface);
    }

.milkdown .milkdown-code-block .cm-panel input {
        caret-color: var(--crepe-color-outline);
        border-radius: 4px;
        background: var(--crepe-color-surface-low);
      }

.milkdown .milkdown-code-block .cm-panel > button {
        text-transform: capitalize;
        background: var(--crepe-color-surface-low);
        color: var(--crepe-color-on-surface-variant);
        border: 1px solid var(--crepe-color-outline);
        font-weight: 600;
        cursor: pointer;
        border-radius: 4px;
      }

.milkdown .milkdown-code-block .cm-panel > button:hover {
          background: var(--crepe-color-hover);
        }

.milkdown .milkdown-code-block .cm-panel > label {
        display: inline-flex;
        align-items: center;
        text-transform: capitalize;
      }

.milkdown .milkdown-code-block .cm-panel > label input[type='checkbox'] {
          border-radius: 4px;
          cursor: pointer;
          -moz-appearance: none;
               appearance: none;
          -webkit-appearance: none;
          background: var(--crepe-color-surface-low);
          width: 1.15em;
          height: 1.15em;
          border: 1px solid var(--crepe-color-outline);
          display: grid;
          place-content: center;
        }

.milkdown .milkdown-code-block .cm-panel > label input[type='checkbox']::before {
            content: '';
            transform-origin: bottom left;
            width: 0.65em;
            height: 0.65em;
            transform: scale(0);
            transition: 120ms transform ease-in-out;
            box-shadow: inset 1em 1em var(--crepe-color-outline);
            clip-path: polygon(
              14% 44%,
              0 65%,
              50% 100%,
              100% 16%,
              80% 0%,
              43% 62%
            );
          }

.milkdown .milkdown-code-block .cm-panel > label input[type='checkbox']:checked::before {
            transform: scale(1);
          }

.milkdown .milkdown-code-block .tools {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

.milkdown .milkdown-code-block .tools input {
        caret-color: var(--crepe-color-outline);
      }

.milkdown .milkdown-code-block .tools .tools-button-group {
        display: flex;
        gap: 2px;
      }

.milkdown .milkdown-code-block .tools .tools-button-group button {
          background: var(--crepe-color-secondary);
          color: var(--crepe-color-on-surface-variant);
          padding: 4px 10px;
          opacity: 0;
          cursor: pointer;
          border-radius: 4px;
          font-size: 12px;
          line-height: 16px;
          font-weight: 600;
          font-family: var(--crepe-font-default);
          transition: opacity 0.2s ease-in-out;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;
        }

.milkdown .milkdown-code-block .tools .tools-button-group button svg {
            width: 14px;
            height: 14px;
            fill: var(--crepe-color-on-surface-variant);
          }

.milkdown .milkdown-code-block .tools .tools-button-group button:first-child {
          border-top-left-radius: 100px;
          border-bottom-left-radius: 100px;
        }

.milkdown .milkdown-code-block .tools .tools-button-group button:last-child {
          border-top-right-radius: 100px;
          border-bottom-right-radius: 100px;
        }

.milkdown .milkdown-code-block .tools .language-button {
        display: flex;
        align-items: center;
        font-family: var(--crepe-font-default);
        gap: 6px;
        padding: 2px 4px 2px 8px;
        background: var(--crepe-color-surface-low);
        color: var(--crepe-color-on-surface-variant);
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        line-height: 16px;
        margin-bottom: 8px;
        opacity: 0;
        cursor: pointer;
        transition: opacity 0.2s ease-in-out;
      }

.milkdown .milkdown-code-block .tools .language-button:hover {
          background: var(--crepe-color-hover);
        }

.milkdown .milkdown-code-block .tools .language-button .expand-icon {
          transition: transform 0.2s ease-in-out;
          width: 18px;
          height: 18px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

.milkdown .milkdown-code-block .tools .language-button .expand-icon svg {
          width: 14px;
          height: 14px;
          color: var(--crepe-color-outline);
        }

.milkdown .milkdown-code-block .tools .language-button[data-expanded='true'] .expand-icon {
          transform: rotate(180deg);
        }

.milkdown .milkdown-code-block .tools .language-button .expand-icon svg:focus,
        .milkdown .milkdown-code-block .tools .language-button .expand-icon:focus-visible {
          outline: none;
        }

.milkdown .milkdown-code-block:hover .language-button {
      opacity: 1;
    }

.milkdown .milkdown-code-block:hover .tools-button-group > button {
      opacity: 1;
    }

.milkdown .milkdown-code-block .list-wrapper {
      background: var(--crepe-color-surface-low);
      border-radius: 12px;
      box-shadow: var(--crepe-shadow-1);
      width: 240px;
      padding-top: 12px;
    }

.milkdown .milkdown-code-block .language-list {
      height: 410px;
      overflow-y: auto;
      overscroll-behavior: contain;
      margin: 0;
      padding: 0;
    }

.milkdown .milkdown-code-block .language-list-item {
      cursor: pointer;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 22px;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
    }

.milkdown .milkdown-code-block .language-list-item:hover {
        background: var(--crepe-color-hover);
      }

.milkdown .milkdown-code-block .language-list-item:focus-visible {
        outline: none;
        background: var(--crepe-color-hover);
      }

.milkdown .milkdown-code-block .language-list-item .leading,
      .milkdown .milkdown-code-block .language-list-item .leading svg {
        width: 24px;
        height: 24px;
      }

.milkdown .milkdown-code-block .language-list-item.no-result {
        cursor: default;
        opacity: 0.6;
      }

.milkdown .milkdown-code-block .language-list-item.no-result:hover {
          background: transparent;
        }

.milkdown .milkdown-code-block .search-box {
      display: flex;
      align-items: center;
      margin: 0 12px 8px;
      background: transparent;
      border-radius: 4px;
      outline: 1px solid var(--crepe-color-primary);
      gap: 8px;
      padding: 6px 10px;
    }

.milkdown .milkdown-code-block .search-box:has(input:focus) {
        outline: 2px solid var(--crepe-color-primary);
      }

.milkdown .milkdown-code-block .search-box .search-input {
        width: 100%;
        color: var(--crepe-color-on-surface);
      }

.milkdown .milkdown-code-block .search-box .search-icon {
        display: none;
      }

.milkdown .milkdown-code-block .search-box .clear-icon {
        cursor: pointer;
        width: 20px;
        height: 20px;
      }

.milkdown .milkdown-code-block .search-box .clear-icon svg {
          width: 20px;
          height: 20px;
          color: var(--crepe-color-primary);
          fill: var(--crepe-color-primary);
        }

.milkdown .milkdown-code-block .search-box .clear-icon:hover {
          background: var(--crepe-color-hover);
        }

.milkdown .milkdown-code-block .search-box input {
        font-family: var(--crepe-font-default);
        font-size: 14px;
        line-height: 20px;
        background: transparent;
      }

.milkdown .milkdown-code-block .search-box input:focus {
        outline: none;
      }

.milkdown .milkdown-code-block .preview-panel .preview-divider {
        height: 1px;
        opacity: 0.2;
        background: var(--crepe-color-outline);
        margin: 6px 0;
      }

.milkdown .milkdown-code-block .preview-panel .preview-label {
        margin: 6px 0;
        font-size: 12px;
        color: color-mix(
          in srgb,
          var(--crepe-color-on-surface),
          transparent 40%
        );
        font-weight: 600;
        text-transform: uppercase;
        font-family: var(--crepe-font-default);
      }

.milkdown .milkdown-code-block .preview-panel .preview {
        text-align: center;
        overflow-x: auto;
      }

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.ProseMirror.virtual-cursor-enabled {
  /* Hide the native cursor */
  caret-color: transparent;
}

.ProseMirror-focused {
  /* Color of the virtual cursor */
  --prosemirror-virtual-cursor-color: red;
}

.ProseMirror .prosemirror-virtual-cursor {
  position: absolute;
  cursor: text;
  pointer-events: none;
  transform: translate(-1px);
  -moz-user-select: none;
       user-select: none;
  -webkit-user-select: none;
  border-left: 2px solid var(--prosemirror-virtual-cursor-color);
}

.ProseMirror .prosemirror-virtual-cursor-left {
  width: 1ch;
  transform: translate(calc(-1ch + -1px));
  border-bottom: 2px solid var(--prosemirror-virtual-cursor-color);
  border-right: 2px solid var(--prosemirror-virtual-cursor-color);
  border-left: none;
}

.ProseMirror .prosemirror-virtual-cursor-right {
  width: 1ch;
  border-bottom: 2px solid var(--prosemirror-virtual-cursor-color);
  border-left: 2px solid var(--prosemirror-virtual-cursor-color);
  border-right: none;
}

.ProseMirror-focused .prosemirror-virtual-cursor-animation {
  animation: prosemirror-virtual-cursor-blink 1s linear infinite;
  animation-delay: 0.5s;
}

@keyframes prosemirror-virtual-cursor-blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.milkdown .crepe-drop-cursor {
    background-color: color-mix(
      in srgb,
      var(--crepe-color-outline),
      transparent 50%
    );
    opacity: 0.5;
    transition: all 0.2s;
    pointer-events: none;
  }

.milkdown .ProseMirror-gapcursor:after {
    box-sizing: border-box;
    border-top: 1px solid var(--crepe-color-on-background);
  }

.milkdown .ProseMirror-focused {
    --prosemirror-virtual-cursor-color: var(--crepe-color-outline);
  }

.milkdown .milkdown-image-inline {
    outline: none;
    display: inline-flex;
    vertical-align: text-bottom;
  }

.milkdown .milkdown-image-inline input {
      background: transparent;
      outline: none;
      border: 0;
      caret-color: var(--crepe-color-outline);
    }

.milkdown .milkdown-image-inline > .empty-image-inline {
      display: inline-flex;
    }

.milkdown .milkdown-image-inline > .empty-image-inline .confirm {
      cursor: pointer;
    }

.milkdown .milkdown-image-inline > .empty-image-inline .link-importer {
      position: relative;
      flex: 1;
    }

.milkdown .milkdown-image-inline > .empty-image-inline .link-importer > .link-input-area {
      width: 208px;
      color: var(--crepe-color-on-background);
      display: flex;
    }

.milkdown .milkdown-image-inline > .empty-image-inline .link-importer .placeholder {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      cursor: text;
    }

.milkdown .milkdown-image-inline > .empty-image-inline .link-importer .placeholder .uploader {
      cursor: pointer;
      display: flex;
    }

.milkdown .milkdown-image-inline .hidden {
      display: none !important;
    }

.milkdown .milkdown-image-inline.empty.selected {
      background: none;
      outline: none;
    }

.milkdown .milkdown-image-inline.empty.selected .empty-image-inline {
        box-shadow: var(--crepe-shadow-1);
      }

.milkdown .milkdown-image-inline.selected {
      background: none;
      outline: 1px solid var(--crepe-color-primary);
    }

.milkdown .milkdown-image-inline.selected :not(input)::-moz-selection {
        background: transparent;
      }

.milkdown .milkdown-image-inline.selected :not(input)::selection {
        background: transparent;
      }

.milkdown .milkdown-image-inline .empty-image-inline {
      align-items: center;
      padding: 4px 10px;
      gap: 10px;
      background: var(--crepe-color-surface);
      font-family: var(--crepe-font-default);
      border-radius: 8px;
      font-size: 16px;
    }

.milkdown .milkdown-image-inline .empty-image-inline .image-icon svg {
        width: 18px;
        height: 18px;
        fill: var(--crepe-color-outline);
      }

.milkdown .milkdown-image-inline .empty-image-inline .image-icon {
      padding: 3px;
      width: 24px;
      height: 24px;
}

.milkdown .milkdown-image-inline .empty-image-inline .link-importer {
      height: 24px;
    }

.milkdown .milkdown-image-inline .empty-image-inline .link-importer .placeholder {
      color: color-mix(
        in srgb,
        var(--crepe-color-on-background),
        transparent 60%
      );
    }

.milkdown .milkdown-image-inline .empty-image-inline .link-importer .placeholder :not(input)::-moz-selection {
        background: transparent;
      }

.milkdown .milkdown-image-inline .empty-image-inline .link-importer .placeholder :not(input)::selection {
        background: transparent;
      }

.milkdown .milkdown-image-inline .empty-image-inline .link-importer .link-input-area {
      line-height: 24px;
    }

.milkdown .milkdown-image-inline .empty-image-inline .link-importer .placeholder .uploader {
      gap: 8px;
      color: var(--crepe-color-primary);
      justify-content: center;
      transition: color 0.2s;
      font-family: var(--crepe-font-default);
    }

.milkdown .milkdown-image-inline .empty-image-inline .link-importer.focus .placeholder .uploader {
      color: unset;
    }

.milkdown .milkdown-image-inline .empty-image-inline .link-importer .placeholder .uploader:hover {
      color: var(--crepe-color-primary);
    }

.milkdown .milkdown-image-inline .empty-image-inline .link-importer .placeholder .text {
      margin-left: 8px;
    }

.milkdown .milkdown-image-inline .empty-image-inline .confirm svg {
        width: 18px;
        height: 18px;
      }

.milkdown .milkdown-image-inline .empty-image-inline .confirm {
      display: flex;
      width: 24px;
      height: 24px;
      padding: 3px;
      border-radius: 8px;
      color: var(--crepe-color-primary);
}

.milkdown .milkdown-image-inline .empty-image-inline .confirm:hover {
        background: var(--crepe-color-hover);
      }

.milkdown .milkdown-image-block {
    outline: none;
    margin: 4px 0;
    display: block;
  }

.milkdown .milkdown-image-block > .image-wrapper {
      position: relative;
      width: -moz-fit-content;
      width: fit-content;
      margin: 0 auto;
      min-width: 100px;
    }

.milkdown .milkdown-image-block > .image-wrapper .operation {
      position: absolute;
      display: flex;
    }

.milkdown .milkdown-image-block > .image-wrapper .operation > .operation-item {
      cursor: pointer;
    }

.milkdown .milkdown-image-block > .image-wrapper img {
      max-width: 100%;
      min-height: 100px;
      display: block;
      -o-object-fit: cover;
         object-fit: cover;
    }

.milkdown .milkdown-image-block > .image-wrapper > .image-resize-handle {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }

.milkdown .milkdown-image-block > .image-wrapper > .image-resize-handle:hover {
      cursor: row-resize;
    }

.milkdown .milkdown-image-block input {
      background: transparent;
      outline: none;
      border: 0;
      caret-color: var(--crepe-color-outline);
    }

.milkdown .milkdown-image-block > .caption-input {
      display: block;
      width: 100%;
      text-align: center;
      color: var(--crepe-color-on-background);
    }

.milkdown .milkdown-image-block > .image-edit {
      display: flex;
    }

.milkdown .milkdown-image-block > .image-edit .confirm {
      cursor: pointer;
    }

.milkdown .milkdown-image-block > .image-edit .link-importer {
      position: relative;
      flex: 1;
    }

.milkdown .milkdown-image-block > .image-edit .link-importer > .link-input-area {
      width: 100%;
    }

.milkdown .milkdown-image-block > .image-edit .link-importer .placeholder {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      cursor: text;
    }

.milkdown .milkdown-image-block > .image-edit .link-importer .placeholder .uploader {
      cursor: pointer;
      display: flex;
    }

.milkdown .milkdown-image-block .hidden {
      display: none !important;
    }

.milkdown .milkdown-image-block.selected > .image-edit:not(:has(input:focus)) {
      position: relative;
    }

.milkdown .milkdown-image-block.selected > .image-edit:not(:has(input:focus))::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: color-mix(
          in srgb,
          var(--crepe-color-selected),
          transparent 60%
        );
        pointer-events: none;
      }

.milkdown .milkdown-image-block.selected > .image-wrapper {
        position: relative;
      }

.milkdown .milkdown-image-block.selected > .image-wrapper::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: color-mix(
            in srgb,
            var(--crepe-color-selected),
            transparent 60%
          );
        }

.milkdown .milkdown-image-block.selected :not(input)::-moz-selection {
        background: transparent;
      }

.milkdown .milkdown-image-block.selected :not(input)::selection {
        background: transparent;
      }

.milkdown .milkdown-image-block .image-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
    }

.milkdown .milkdown-image-block .image-wrapper .operation {
      gap: 12px;
      right: 12px;
      top: 12px;
      opacity: 0;
      transition: all 0.2s;
    }

.milkdown .milkdown-image-block:hover > .image-wrapper .operation {
      opacity: 1;
    }

.milkdown .milkdown-image-block .image-wrapper .operation > .operation-item {
      color: var(--crepe-color-on-inverse);
      padding: 4px;
      background: var(--crepe-color-inverse);
      opacity: 0.6;
      border-radius: 50%;
      width: 32px;
      height: 32px;
    }

.milkdown .milkdown-image-block .image-wrapper .operation > .operation-item svg {
        width: 24px;
        height: 24px;
      }

.milkdown .milkdown-image-block .image-wrapper .image-resize-handle {
      height: 4px;
      bottom: -2px;
      max-width: 160px;
      width: 100%;
      background: var(--crepe-color-outline);
      opacity: 0;
      transition: all 0.2s;
      border-radius: 4px;
    }

.milkdown .milkdown-image-block:hover > .image-wrapper .image-resize-handle {
      opacity: 1;
    }

.milkdown .milkdown-image-block .caption-input {
      margin: 4px auto;
      font-family: var(--crepe-font-default);
    }

.milkdown .milkdown-image-block .image-edit {
      align-items: center;
      padding: 16px 24px;
      gap: 16px;
      background: var(--crepe-color-surface);
      height: 56px;
    }

.milkdown .milkdown-image-block .image-edit .image-icon {
      color: var(--crepe-color-outline);
    }

.milkdown .milkdown-image-block .image-edit .image-icon svg {
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      fill: var(--crepe-color-outline);
    }

.milkdown .milkdown-image-block .image-edit .link-importer .placeholder {
      color: color-mix(
        in srgb,
        var(--crepe-color-on-background),
        transparent 60%
      );
    }

.milkdown .milkdown-image-block .image-edit .link-importer .placeholder :not(input)::-moz-selection {
        background: transparent;
      }

.milkdown .milkdown-image-block .image-edit .link-importer .placeholder :not(input)::selection {
        background: transparent;
      }

.milkdown .milkdown-image-block .image-edit .link-importer .link-input-area {
      line-height: 24px;
      color: var(--crepe-color-on-background);
    }

.milkdown .milkdown-image-block .image-edit .link-importer .placeholder .uploader {
      gap: 8px;
      color: var(--crepe-color-primary);
      justify-content: center;
      transition: color 0.2s;
      font-weight: 600;
    }

.milkdown .milkdown-image-block .image-edit .link-importer.focus .placeholder .uploader {
      color: unset;
    }

.milkdown .milkdown-image-block .image-edit .link-importer .placeholder .uploader:hover {
      color: var(--crepe-color-primary);
    }

.milkdown .milkdown-image-block .image-edit .link-importer .placeholder .text {
      margin-left: 8px;
    }

.milkdown .milkdown-image-block .image-edit .confirm {
      background: var(--crepe-color-secondary);
      color: var(--crepe-color-on-secondary);
      line-height: 40px;
      padding: 0 24px;
      border-radius: 100px;
      font-size: 14px;
      font-weight: 600;
    }

.milkdown .milkdown-image-block .image-edit .confirm:hover {
        background:
          linear-gradient(
            0deg,
            rgba(29, 25, 43, 0.08) 0%,
            rgba(29, 25, 43, 0.08) 100%
          ),
          var(--crepe-color-secondary);
      }

.milkdown .milkdown-link-preview {
    position: absolute;
    z-index: 10;
  }

.milkdown .milkdown-link-preview[data-show='false'] {
      display: none;
    }

.milkdown .milkdown-link-preview > .link-preview {
      height: 32px;
      display: flex;
      justify-content: center;
      padding: 4px 10px;
      background: var(--crepe-color-surface);
      gap: 10px;
      border-radius: 8px;
      cursor: pointer;
      box-shadow: var(--crepe-shadow-1);
    }

.milkdown .milkdown-link-preview > .link-preview > .link-display {
        text-decoration: none;
        color: unset;
      }

.milkdown .milkdown-link-preview > .link-preview > .link-display:hover:before {
        display: block;
      }

.milkdown .milkdown-link-preview > .link-preview > .link-icon > svg {
          width: 18px;
          height: 18px;
          color: var(--crepe-color-outline);
          fill: var(--crepe-color-outline);
        }

.milkdown .milkdown-link-preview > .link-preview > .link-icon {
        border-radius: 8px;
        padding: 3px;
        line-height: 24px;
}

.milkdown .milkdown-link-preview > .link-preview > .link-icon:hover {
          background: var(--crepe-color-hover);
        }

.milkdown .milkdown-link-preview > .link-preview > .link-display {
        width: 240px;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 14px;
        white-space: nowrap;
      }

.milkdown .milkdown-link-preview > .link-preview > .link-display:hover {
          text-decoration: underline;
        }

.milkdown .milkdown-link-preview > .link-preview > .button > svg {
          width: 18px;
          height: 18px;
          color: var(--crepe-color-outline);
          fill: var(--crepe-color-outline);
        }

.milkdown .milkdown-link-preview > .link-preview > .button {
        padding: 3px;
        border-radius: 8px;
        line-height: 24px;
}

.milkdown .milkdown-link-preview > .link-preview > .button:hover {
          background: var(--crepe-color-hover);
        }

.milkdown .milkdown-link-edit {
    position: absolute;
    z-index: 10;
  }

.milkdown .milkdown-link-edit[data-show='false'] {
      display: none;
    }

.milkdown .milkdown-link-edit > .link-edit {
      height: 32px;
      display: flex;
      justify-content: center;
      padding: 4px 10px 4px 20px;
      background: var(--crepe-color-surface);
      gap: 8px;
      border-radius: 8px;
      box-shadow: var(--crepe-shadow-1);
    }

.milkdown .milkdown-link-edit > .link-edit > .input-area {
        outline: none;
        background: transparent;
        width: 200px;
        font-size: 14px;
        color: var(--crepe-color-on-background);
      }

.milkdown .milkdown-link-edit > .link-edit > .button > svg {
          width: 18px;
          height: 18px;
          color: var(--crepe-color-outline);
          fill: var(--crepe-color-outline);
        }

.milkdown .milkdown-link-edit > .link-edit > .button {
        padding: 3px;
        cursor: pointer;
        border-radius: 8px;
        font-size: 12px;
        line-height: 24px;
}

.milkdown .milkdown-link-edit > .link-edit > .button:hover {
          background: var(--crepe-color-hover);
        }

.milkdown .milkdown-link-edit > .link-edit > .button.hidden {
          visibility: hidden;
        }

.milkdown .milkdown-list-item-block {
    display: block;
    padding: 0;
  }

.milkdown .milkdown-list-item-block > .list-item {
      display: flex;
      align-items: flex-start;
    }

.milkdown .milkdown-list-item-block > .list-item > .children {
      min-width: 0;
      flex: 1;
    }

.milkdown .milkdown-list-item-block li {
      gap: 10px;
    }

.milkdown .milkdown-list-item-block li .label-wrapper {
        color: var(--crepe-color-outline);
      }

.milkdown .milkdown-list-item-block li .label-wrapper svg {
          fill: var(--crepe-color-outline);
        }

.milkdown .milkdown-list-item-block li .label-wrapper {
        height: 32px;
        width: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
}

.milkdown .milkdown-list-item-block li .label-wrapper .label {
          height: 32px;
          padding: 4px 0;
          width: 24px;
          text-align: right;
        }

.milkdown .milkdown-list-item-block li .label-wrapper .checked,
        .milkdown .milkdown-list-item-block li .label-wrapper .unchecked {
          cursor: pointer;
        }

.milkdown .milkdown-list-item-block li .label-wrapper .readonly {
          cursor: not-allowed;
        }

.milkdown .crepe-placeholder::before {
    position: absolute;
    color: color-mix(
      in srgb,
      var(--crepe-color-on-background),
      transparent 60%
    );
    pointer-events: none;
    height: 0;
    content: attr(data-placeholder);
  }

.milkdown:has(.milkdown-link-preview[data-show='true']) .milkdown-toolbar,
  .milkdown:has(.milkdown-link-edit[data-show='true']) .milkdown-toolbar {
    display: none;
  }

.milkdown .milkdown-toolbar[data-show='false'] {
      display: none;
    }

.milkdown .milkdown-toolbar {
    z-index: 10;
    position: absolute;
    display: flex;
    background: var(--crepe-color-surface);
    box-shadow: var(--crepe-shadow-1);
    border-radius: 8px;
    overflow: hidden;
}

.milkdown .milkdown-toolbar .divider {
      width: 1px;
      background: color-mix(
        in srgb,
        var(--crepe-color-outline),
        transparent 80%
      );
      height: 24px;
      margin: 10px;
    }

.milkdown .milkdown-toolbar .toolbar-item {
      width: 32px;
      height: 32px;
      margin: 6px;
      padding: 4px;
      cursor: pointer;
      border-radius: 4px;
    }

.milkdown .milkdown-toolbar .toolbar-item:hover {
        background: var(--crepe-color-hover);
      }

.milkdown .milkdown-toolbar .toolbar-item:active {
        background: var(--crepe-color-selected);
      }

.milkdown .milkdown-toolbar .toolbar-item svg {
        height: 24px;
        width: 24px;
        color: var(--crepe-color-outline);
        fill: var(--crepe-color-outline);
      }

.milkdown .milkdown-toolbar .toolbar-item.active svg {
          color: var(--crepe-color-primary);
          fill: var(--crepe-color-primary);
        }

.ProseMirror .tableWrapper {
  overflow-x: auto;
}

.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  overflow: hidden;
}

.ProseMirror td,
.ProseMirror th {
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}

.ProseMirror td:not([data-colwidth]):not(.column-resize-dragging),
.ProseMirror th:not([data-colwidth]):not(.column-resize-dragging) {
  /* if there's no explicit width set and the column is not being resized, set a default width */
  min-width: var(--default-cell-min-width);
}

.ProseMirror .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: 0;
  width: 4px;
  z-index: 20;
  background-color: #adf;
  pointer-events: none;
}

.ProseMirror.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

/* Give selected cells a blue overlay */

.ProseMirror .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: '';
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(200, 200, 255, 0.4);
  pointer-events: none;
}

.milkdown .milkdown-table-block {
    display: block;
    margin: 4px 0;
  }

.milkdown .milkdown-table-block th,
    .milkdown .milkdown-table-block td {
      border: 1px solid
        color-mix(in srgb, var(--crepe-color-outline), transparent 80%);
      padding: 4px 16px;
    }

.milkdown .milkdown-table-block th .ProseMirror-selectednode, .milkdown .milkdown-table-block td .ProseMirror-selectednode {
        background-color: transparent !important;
      }

.milkdown .milkdown-table-block th:has(.ProseMirror-selectednode), .milkdown .milkdown-table-block td:has(.ProseMirror-selectednode) {
        outline: 1px solid var(--crepe-color-primary);
        outline-offset: -1px;
      }

.milkdown .milkdown-table-block .selectedCell::after {
        background-color: var(--crepe-color-selected);
        opacity: 0.4;
      }

.milkdown .milkdown-table-block .selectedCell ::-moz-selection {
        background: transparent;
      }

.milkdown .milkdown-table-block .selectedCell ::selection {
        background: transparent;
      }

.milkdown .milkdown-table-block .drag-preview {
      background-color: var(--crepe-color-surface);
      opacity: 0.4;
      position: absolute;
      z-index: 100;
      display: flex;
      flex-direction: column;
      outline: 1px solid var(--crepe-color-primary);
      outline-offset: -1px;
    }

.milkdown .milkdown-table-block .drag-preview[data-show='false'] {
        display: none;
      }

.milkdown .milkdown-table-block .drag-preview th:has(.ProseMirror-selectednode), .milkdown .milkdown-table-block .drag-preview td:has(.ProseMirror-selectednode) {
          outline: none;
        }

.milkdown .milkdown-table-block .handle {
      position: absolute;
      font-size: 14px;
      transition: opacity ease-in-out 0.2s;
    }

.milkdown .milkdown-table-block .handle[data-show='false'] {
      opacity: 0;
    }

.milkdown .milkdown-table-block svg {
      fill: var(--crepe-color-outline);
    }

.milkdown .milkdown-table-block .cell-handle {
      z-index: 50;
      left: -999px;
      top: -999px;
      cursor: grab;
      background-color: var(--crepe-color-surface);
      color: var(--crepe-color-outline);
      border-radius: 100px;
      box-shadow: var(--crepe-shadow-1);
      transition: background-color 0.2s ease-in-out;
    }

.milkdown .milkdown-table-block .cell-handle:hover {
        background-color: var(--crepe-color-hover);
      }

.milkdown .milkdown-table-block .cell-handle:has(.button-group:hover) {
        background-color: var(--crepe-color-surface);
      }

.milkdown .milkdown-table-block .cell-handle[data-role='col-drag-handle'] {
        transform: translateY(50%);
        padding: 0 6px;
        width: 28px;
        height: 16px;
      }

.milkdown .milkdown-table-block .cell-handle[data-role='row-drag-handle'] {
        transform: translateX(50%);
        padding: 6px 0;
        width: 16px;
        height: 28px;
      }

.milkdown .milkdown-table-block .cell-handle .button-group {
        position: absolute;
        transform: translateX(-50%);
        left: 50%;
        top: -52px;
        display: flex;
        background-color: var(--crepe-color-surface);
        border-radius: 8px;
        box-shadow: var(--crepe-shadow-1);
      }

.milkdown .milkdown-table-block .cell-handle .button-group::after {
          content: '';
          position: absolute;
          bottom: -8px;
          height: 8px;
          background-color: transparent;
          width: 100%;
        }

.milkdown .milkdown-table-block .cell-handle .button-group[data-show='false'] {
          display: none;
        }

.milkdown .milkdown-table-block .cell-handle .button-group button {
          cursor: pointer;
          margin: 6px;
          padding: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 4px;
        }

.milkdown .milkdown-table-block .cell-handle .button-group button svg {
            width: 24px;
            height: 24px;
          }

.milkdown .milkdown-table-block .cell-handle .button-group button:hover {
            border-radius: 8px;
            background-color: var(--crepe-color-hover);
          }

.milkdown .milkdown-table-block .cell-handle .button-group button:active {
            background: var(--crepe-color-selected);
          }

.milkdown .milkdown-table-block .cell-handle:hover {
        opacity: 1;
      }

.milkdown .milkdown-table-block .line-handle {
      z-index: 20;
      background-color: var(--crepe-color-primary);
    }

.milkdown .milkdown-table-block .line-handle:hover {
        opacity: 1;
      }

.milkdown .milkdown-table-block .line-handle .add-button {
        cursor: pointer;
        background-color: var(--crepe-color-surface);
        color: var(--crepe-color-outline);
        border-radius: 100px;
        box-shadow: var(--crepe-shadow-1);
        transition: background-color 0.2s ease-in-out;
      }

.milkdown .milkdown-table-block .line-handle .add-button svg {
          width: 16px;
          height: 16px;
        }

.milkdown .milkdown-table-block .line-handle .add-button:hover {
          background-color: var(--crepe-color-hover);
        }

.milkdown .milkdown-table-block .line-handle .add-button:active {
          background: var(--crepe-color-selected);
        }

.milkdown .milkdown-table-block .line-handle[data-role='x-line-drag-handle'] {
        height: 1px;
        z-index: 2;
      }

.milkdown .milkdown-table-block .line-handle[data-role='x-line-drag-handle'] .add-button {
          position: absolute;
          transform: translateX(-50%) translateY(-50%);
          padding: 6px 0;
          width: 16px;
          height: 28px;
        }

.milkdown .milkdown-table-block .line-handle[data-role='y-line-drag-handle'] {
        width: 1px;
        z-index: 1;
      }

.milkdown .milkdown-table-block .line-handle[data-role='y-line-drag-handle'] .add-button {
          position: absolute;
          transform: translateY(-50%) translateX(-50%);
          padding: 0 6px;
          width: 28px;
          height: 16px;
        }

.milkdown .milkdown-table-block .line-handle[data-display-type='indicator'] .add-button {
          display: none;
        }

.milkdown .milkdown-table-block.readonly .handle {
      display: none;
    }

@font-face{font-family:KaTeX_AMS;font-style:normal;font-weight:400;src:url(./KaTeX_AMS-Regular-BQhdFMY1.woff2) format("woff2"),url(./KaTeX_AMS-Regular-DMm9YOAa.woff) format("woff"),url(./KaTeX_AMS-Regular-DRggAlZN.ttf) format("truetype")}

@font-face{font-family:KaTeX_Caligraphic;font-style:normal;font-weight:700;src:url(./KaTeX_Caligraphic-Bold-Dq_IR9rO.woff2) format("woff2"),url(./KaTeX_Caligraphic-Bold-BEiXGLvX.woff) format("woff"),url(./KaTeX_Caligraphic-Bold-ATXxdsX0.ttf) format("truetype")}

@font-face{font-family:KaTeX_Caligraphic;font-style:normal;font-weight:400;src:url(./KaTeX_Caligraphic-Regular-Di6jR-x-.woff2) format("woff2"),url(./KaTeX_Caligraphic-Regular-CTRA-rTL.woff) format("woff"),url(./KaTeX_Caligraphic-Regular-wX97UBjC.ttf) format("truetype")}

@font-face{font-family:KaTeX_Fraktur;font-style:normal;font-weight:700;src:url(./KaTeX_Fraktur-Bold-CL6g_b3V.woff2) format("woff2"),url(./KaTeX_Fraktur-Bold-BsDP51OF.woff) format("woff"),url(./KaTeX_Fraktur-Bold-BdnERNNW.ttf) format("truetype")}

@font-face{font-family:KaTeX_Fraktur;font-style:normal;font-weight:400;src:url(./KaTeX_Fraktur-Regular-CTYiF6lA.woff2) format("woff2"),url(./KaTeX_Fraktur-Regular-Dxdc4cR9.woff) format("woff"),url(./KaTeX_Fraktur-Regular-CB_wures.ttf) format("truetype")}

@font-face{font-family:KaTeX_Main;font-style:normal;font-weight:700;src:url(./KaTeX_Main-Bold-Cx986IdX.woff2) format("woff2"),url(./KaTeX_Main-Bold-Jm3AIy58.woff) format("woff"),url(./KaTeX_Main-Bold-waoOVXN0.ttf) format("truetype")}

@font-face{font-family:KaTeX_Main;font-style:italic;font-weight:700;src:url(./KaTeX_Main-BoldItalic-DxDJ3AOS.woff2) format("woff2"),url(./KaTeX_Main-BoldItalic-SpSLRI95.woff) format("woff"),url(./KaTeX_Main-BoldItalic-DzxPMmG6.ttf) format("truetype")}

@font-face{font-family:KaTeX_Main;font-style:italic;font-weight:400;src:url(./KaTeX_Main-Italic-NWA7e6Wa.woff2) format("woff2"),url(./KaTeX_Main-Italic-BMLOBm91.woff) format("woff"),url(./KaTeX_Main-Italic-3WenGoN9.ttf) format("truetype")}

@font-face{font-family:KaTeX_Main;font-style:normal;font-weight:400;src:url(./KaTeX_Main-Regular-B22Nviop.woff2) format("woff2"),url(./KaTeX_Main-Regular-Dr94JaBh.woff) format("woff"),url(./KaTeX_Main-Regular-ypZvNtVU.ttf) format("truetype")}

@font-face{font-family:KaTeX_Math;font-style:italic;font-weight:700;src:url(./KaTeX_Math-BoldItalic-CZnvNsCZ.woff2) format("woff2"),url(./KaTeX_Math-BoldItalic-iY-2wyZ7.woff) format("woff"),url(./KaTeX_Math-BoldItalic-B3XSjfu4.ttf) format("truetype")}

@font-face{font-family:KaTeX_Math;font-style:italic;font-weight:400;src:url(./KaTeX_Math-Italic-t53AETM-.woff2) format("woff2"),url(./KaTeX_Math-Italic-DA0__PXp.woff) format("woff"),url(./KaTeX_Math-Italic-flOr_0UB.ttf) format("truetype")}

@font-face{font-family:"KaTeX_SansSerif";font-style:normal;font-weight:700;src:url(./KaTeX_SansSerif-Bold-D1sUS0GD.woff2) format("woff2"),url(./KaTeX_SansSerif-Bold-DbIhKOiC.woff) format("woff"),url(./KaTeX_SansSerif-Bold-CFMepnvq.ttf) format("truetype")}

@font-face{font-family:"KaTeX_SansSerif";font-style:italic;font-weight:400;src:url(./KaTeX_SansSerif-Italic-C3H0VqGB.woff2) format("woff2"),url(./KaTeX_SansSerif-Italic-DN2j7dab.woff) format("woff"),url(./KaTeX_SansSerif-Italic-YYjJ1zSn.ttf) format("truetype")}

@font-face{font-family:"KaTeX_SansSerif";font-style:normal;font-weight:400;src:url(./KaTeX_SansSerif-Regular-DDBCnlJ7.woff2) format("woff2"),url(./KaTeX_SansSerif-Regular-CS6fqUqJ.woff) format("woff"),url(./KaTeX_SansSerif-Regular-BNo7hRIc.ttf) format("truetype")}

@font-face{font-family:KaTeX_Script;font-style:normal;font-weight:400;src:url(./KaTeX_Script-Regular-D3wIWfF6.woff2) format("woff2"),url(./KaTeX_Script-Regular-D5yQViql.woff) format("woff"),url(./KaTeX_Script-Regular-C5JkGWo-.ttf) format("truetype")}

@font-face{font-family:KaTeX_Size1;font-style:normal;font-weight:400;src:url(./KaTeX_Size1-Regular-mCD8mA8B.woff2) format("woff2"),url(./KaTeX_Size1-Regular-C195tn64.woff) format("woff"),url(./KaTeX_Size1-Regular-Dbsnue_I.ttf) format("truetype")}

@font-face{font-family:KaTeX_Size2;font-style:normal;font-weight:400;src:url(./KaTeX_Size2-Regular-Dy4dx90m.woff2) format("woff2"),url(./KaTeX_Size2-Regular-oD1tc_U0.woff) format("woff"),url(./KaTeX_Size2-Regular-B7gKUWhC.ttf) format("truetype")}

@font-face{font-family:KaTeX_Size3;font-style:normal;font-weight:400;src:url(data:font/woff2;base64,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) format("woff2"),url(./KaTeX_Size3-Regular-CTq5MqoE.woff) format("woff"),url(./KaTeX_Size3-Regular-DgpXs0kz.ttf) format("truetype")}

@font-face{font-family:KaTeX_Size4;font-style:normal;font-weight:400;src:url(./KaTeX_Size4-Regular-Dl5lxZxV.woff2) format("woff2"),url(./KaTeX_Size4-Regular-BF-4gkZK.woff) format("woff"),url(./KaTeX_Size4-Regular-DWFBv043.ttf) format("truetype")}

@font-face{font-family:KaTeX_Typewriter;font-style:normal;font-weight:400;src:url(./KaTeX_Typewriter-Regular-CO6r4hn1.woff2) format("woff2"),url(./KaTeX_Typewriter-Regular-C0xS9mPB.woff) format("woff"),url(./KaTeX_Typewriter-Regular-D3Ib7_Hf.ttf) format("truetype")}

.katex{font:normal 1.21em KaTeX_Main,Times New Roman,serif;line-height:1.2;text-indent:0;text-rendering:auto}

.katex *{-ms-high-contrast-adjust:none!important;border-color:currentColor}

.katex .katex-version:after{content:"0.16.22"}

.katex .katex-mathml{clip:rect(1px,1px,1px,1px);border:0;height:1px;overflow:hidden;padding:0;position:absolute;width:1px}

.katex .katex-html>.newline{display:block}

.katex .base{position:relative;white-space:nowrap;width:-moz-min-content;width:min-content}

.katex .base,.katex .strut{display:inline-block}

.katex .textbf{font-weight:700}

.katex .textit{font-style:italic}

.katex .textrm{font-family:KaTeX_Main}

.katex .textsf{font-family:KaTeX_SansSerif}

.katex .texttt{font-family:KaTeX_Typewriter}

.katex .mathnormal{font-family:KaTeX_Math;font-style:italic}

.katex .mathit{font-family:KaTeX_Main;font-style:italic}

.katex .mathrm{font-style:normal}

.katex .mathbf{font-family:KaTeX_Main;font-weight:700}

.katex .boldsymbol{font-family:KaTeX_Math;font-style:italic;font-weight:700}

.katex .amsrm,.katex .mathbb,.katex .textbb{font-family:KaTeX_AMS}

.katex .mathcal{font-family:KaTeX_Caligraphic}

.katex .mathfrak,.katex .textfrak{font-family:KaTeX_Fraktur}

.katex .mathboldfrak,.katex .textboldfrak{font-family:KaTeX_Fraktur;font-weight:700}

.katex .mathtt{font-family:KaTeX_Typewriter}

.katex .mathscr,.katex .textscr{font-family:KaTeX_Script}

.katex .mathsf,.katex .textsf{font-family:KaTeX_SansSerif}

.katex .mathboldsf,.katex .textboldsf{font-family:KaTeX_SansSerif;font-weight:700}

.katex .mathitsf,.katex .mathsfit,.katex .textitsf{font-family:KaTeX_SansSerif;font-style:italic}

.katex .mainrm{font-family:KaTeX_Main;font-style:normal}

.katex .vlist-t{border-collapse:collapse;display:inline-table;table-layout:fixed}

.katex .vlist-r{display:table-row}

.katex .vlist{display:table-cell;position:relative;vertical-align:bottom}

.katex .vlist>span{display:block;height:0;position:relative}

.katex .vlist>span>span{display:inline-block}

.katex .vlist>span>.pstrut{overflow:hidden;width:0}

.katex .vlist-t2{margin-right:-2px}

.katex .vlist-s{display:table-cell;font-size:1px;min-width:2px;vertical-align:bottom;width:2px}

.katex .vbox{align-items:baseline;display:inline-flex;flex-direction:column}

.katex .hbox{width:100%}

.katex .hbox,.katex .thinbox{display:inline-flex;flex-direction:row}

.katex .thinbox{max-width:0;width:0}

.katex .msupsub{text-align:left}

.katex .mfrac>span>span{text-align:center}

.katex .mfrac .frac-line{border-bottom-style:solid;display:inline-block;width:100%}

.katex .hdashline,.katex .hline,.katex .mfrac .frac-line,.katex .overline .overline-line,.katex .rule,.katex .underline .underline-line{min-height:1px}

.katex .mspace{display:inline-block}

.katex .clap,.katex .llap,.katex .rlap{position:relative;width:0}

.katex .clap>.inner,.katex .llap>.inner,.katex .rlap>.inner{position:absolute}

.katex .clap>.fix,.katex .llap>.fix,.katex .rlap>.fix{display:inline-block}

.katex .llap>.inner{right:0}

.katex .clap>.inner,.katex .rlap>.inner{left:0}

.katex .clap>.inner>span{margin-left:-50%;margin-right:50%}

.katex .rule{border:0 solid;display:inline-block;position:relative}

.katex .hline,.katex .overline .overline-line,.katex .underline .underline-line{border-bottom-style:solid;display:inline-block;width:100%}

.katex .hdashline{border-bottom-style:dashed;display:inline-block;width:100%}

.katex .sqrt>.root{margin-left:.2777777778em;margin-right:-.5555555556em}

.katex .fontsize-ensurer.reset-size1.size1,.katex .sizing.reset-size1.size1{font-size:1em}

.katex .fontsize-ensurer.reset-size1.size2,.katex .sizing.reset-size1.size2{font-size:1.2em}

.katex .fontsize-ensurer.reset-size1.size3,.katex .sizing.reset-size1.size3{font-size:1.4em}

.katex .fontsize-ensurer.reset-size1.size4,.katex .sizing.reset-size1.size4{font-size:1.6em}

.katex .fontsize-ensurer.reset-size1.size5,.katex .sizing.reset-size1.size5{font-size:1.8em}

.katex .fontsize-ensurer.reset-size1.size6,.katex .sizing.reset-size1.size6{font-size:2em}

.katex .fontsize-ensurer.reset-size1.size7,.katex .sizing.reset-size1.size7{font-size:2.4em}

.katex .fontsize-ensurer.reset-size1.size8,.katex .sizing.reset-size1.size8{font-size:2.88em}

.katex .fontsize-ensurer.reset-size1.size9,.katex .sizing.reset-size1.size9{font-size:3.456em}

.katex .fontsize-ensurer.reset-size1.size10,.katex .sizing.reset-size1.size10{font-size:4.148em}

.katex .fontsize-ensurer.reset-size1.size11,.katex .sizing.reset-size1.size11{font-size:4.976em}

.katex .fontsize-ensurer.reset-size2.size1,.katex .sizing.reset-size2.size1{font-size:.8333333333em}

.katex .fontsize-ensurer.reset-size2.size2,.katex .sizing.reset-size2.size2{font-size:1em}

.katex .fontsize-ensurer.reset-size2.size3,.katex .sizing.reset-size2.size3{font-size:1.1666666667em}

.katex .fontsize-ensurer.reset-size2.size4,.katex .sizing.reset-size2.size4{font-size:1.3333333333em}

.katex .fontsize-ensurer.reset-size2.size5,.katex .sizing.reset-size2.size5{font-size:1.5em}

.katex .fontsize-ensurer.reset-size2.size6,.katex .sizing.reset-size2.size6{font-size:1.6666666667em}

.katex .fontsize-ensurer.reset-size2.size7,.katex .sizing.reset-size2.size7{font-size:2em}

.katex .fontsize-ensurer.reset-size2.size8,.katex .sizing.reset-size2.size8{font-size:2.4em}

.katex .fontsize-ensurer.reset-size2.size9,.katex .sizing.reset-size2.size9{font-size:2.88em}

.katex .fontsize-ensurer.reset-size2.size10,.katex .sizing.reset-size2.size10{font-size:3.4566666667em}

.katex .fontsize-ensurer.reset-size2.size11,.katex .sizing.reset-size2.size11{font-size:4.1466666667em}

.katex .fontsize-ensurer.reset-size3.size1,.katex .sizing.reset-size3.size1{font-size:.7142857143em}

.katex .fontsize-ensurer.reset-size3.size2,.katex .sizing.reset-size3.size2{font-size:.8571428571em}

.katex .fontsize-ensurer.reset-size3.size3,.katex .sizing.reset-size3.size3{font-size:1em}

.katex .fontsize-ensurer.reset-size3.size4,.katex .sizing.reset-size3.size4{font-size:1.1428571429em}

.katex .fontsize-ensurer.reset-size3.size5,.katex .sizing.reset-size3.size5{font-size:1.2857142857em}

.katex .fontsize-ensurer.reset-size3.size6,.katex .sizing.reset-size3.size6{font-size:1.4285714286em}

.katex .fontsize-ensurer.reset-size3.size7,.katex .sizing.reset-size3.size7{font-size:1.7142857143em}

.katex .fontsize-ensurer.reset-size3.size8,.katex .sizing.reset-size3.size8{font-size:2.0571428571em}

.katex .fontsize-ensurer.reset-size3.size9,.katex .sizing.reset-size3.size9{font-size:2.4685714286em}

.katex .fontsize-ensurer.reset-size3.size10,.katex .sizing.reset-size3.size10{font-size:2.9628571429em}

.katex .fontsize-ensurer.reset-size3.size11,.katex .sizing.reset-size3.size11{font-size:3.5542857143em}

.katex .fontsize-ensurer.reset-size4.size1,.katex .sizing.reset-size4.size1{font-size:.625em}

.katex .fontsize-ensurer.reset-size4.size2,.katex .sizing.reset-size4.size2{font-size:.75em}

.katex .fontsize-ensurer.reset-size4.size3,.katex .sizing.reset-size4.size3{font-size:.875em}

.katex .fontsize-ensurer.reset-size4.size4,.katex .sizing.reset-size4.size4{font-size:1em}

.katex .fontsize-ensurer.reset-size4.size5,.katex .sizing.reset-size4.size5{font-size:1.125em}

.katex .fontsize-ensurer.reset-size4.size6,.katex .sizing.reset-size4.size6{font-size:1.25em}

.katex .fontsize-ensurer.reset-size4.size7,.katex .sizing.reset-size4.size7{font-size:1.5em}

.katex .fontsize-ensurer.reset-size4.size8,.katex .sizing.reset-size4.size8{font-size:1.8em}

.katex .fontsize-ensurer.reset-size4.size9,.katex .sizing.reset-size4.size9{font-size:2.16em}

.katex .fontsize-ensurer.reset-size4.size10,.katex .sizing.reset-size4.size10{font-size:2.5925em}

.katex .fontsize-ensurer.reset-size4.size11,.katex .sizing.reset-size4.size11{font-size:3.11em}

.katex .fontsize-ensurer.reset-size5.size1,.katex .sizing.reset-size5.size1{font-size:.5555555556em}

.katex .fontsize-ensurer.reset-size5.size2,.katex .sizing.reset-size5.size2{font-size:.6666666667em}

.katex .fontsize-ensurer.reset-size5.size3,.katex .sizing.reset-size5.size3{font-size:.7777777778em}

.katex .fontsize-ensurer.reset-size5.size4,.katex .sizing.reset-size5.size4{font-size:.8888888889em}

.katex .fontsize-ensurer.reset-size5.size5,.katex .sizing.reset-size5.size5{font-size:1em}

.katex .fontsize-ensurer.reset-size5.size6,.katex .sizing.reset-size5.size6{font-size:1.1111111111em}

.katex .fontsize-ensurer.reset-size5.size7,.katex .sizing.reset-size5.size7{font-size:1.3333333333em}

.katex .fontsize-ensurer.reset-size5.size8,.katex .sizing.reset-size5.size8{font-size:1.6em}

.katex .fontsize-ensurer.reset-size5.size9,.katex .sizing.reset-size5.size9{font-size:1.92em}

.katex .fontsize-ensurer.reset-size5.size10,.katex .sizing.reset-size5.size10{font-size:2.3044444444em}

.katex .fontsize-ensurer.reset-size5.size11,.katex .sizing.reset-size5.size11{font-size:2.7644444444em}

.katex .fontsize-ensurer.reset-size6.size1,.katex .sizing.reset-size6.size1{font-size:.5em}

.katex .fontsize-ensurer.reset-size6.size2,.katex .sizing.reset-size6.size2{font-size:.6em}

.katex .fontsize-ensurer.reset-size6.size3,.katex .sizing.reset-size6.size3{font-size:.7em}

.katex .fontsize-ensurer.reset-size6.size4,.katex .sizing.reset-size6.size4{font-size:.8em}

.katex .fontsize-ensurer.reset-size6.size5,.katex .sizing.reset-size6.size5{font-size:.9em}

.katex .fontsize-ensurer.reset-size6.size6,.katex .sizing.reset-size6.size6{font-size:1em}

.katex .fontsize-ensurer.reset-size6.size7,.katex .sizing.reset-size6.size7{font-size:1.2em}

.katex .fontsize-ensurer.reset-size6.size8,.katex .sizing.reset-size6.size8{font-size:1.44em}

.katex .fontsize-ensurer.reset-size6.size9,.katex .sizing.reset-size6.size9{font-size:1.728em}

.katex .fontsize-ensurer.reset-size6.size10,.katex .sizing.reset-size6.size10{font-size:2.074em}

.katex .fontsize-ensurer.reset-size6.size11,.katex .sizing.reset-size6.size11{font-size:2.488em}

.katex .fontsize-ensurer.reset-size7.size1,.katex .sizing.reset-size7.size1{font-size:.4166666667em}

.katex .fontsize-ensurer.reset-size7.size2,.katex .sizing.reset-size7.size2{font-size:.5em}

.katex .fontsize-ensurer.reset-size7.size3,.katex .sizing.reset-size7.size3{font-size:.5833333333em}

.katex .fontsize-ensurer.reset-size7.size4,.katex .sizing.reset-size7.size4{font-size:.6666666667em}

.katex .fontsize-ensurer.reset-size7.size5,.katex .sizing.reset-size7.size5{font-size:.75em}

.katex .fontsize-ensurer.reset-size7.size6,.katex .sizing.reset-size7.size6{font-size:.8333333333em}

.katex .fontsize-ensurer.reset-size7.size7,.katex .sizing.reset-size7.size7{font-size:1em}

.katex .fontsize-ensurer.reset-size7.size8,.katex .sizing.reset-size7.size8{font-size:1.2em}

.katex .fontsize-ensurer.reset-size7.size9,.katex .sizing.reset-size7.size9{font-size:1.44em}

.katex .fontsize-ensurer.reset-size7.size10,.katex .sizing.reset-size7.size10{font-size:1.7283333333em}

.katex .fontsize-ensurer.reset-size7.size11,.katex .sizing.reset-size7.size11{font-size:2.0733333333em}

.katex .fontsize-ensurer.reset-size8.size1,.katex .sizing.reset-size8.size1{font-size:.3472222222em}

.katex .fontsize-ensurer.reset-size8.size2,.katex .sizing.reset-size8.size2{font-size:.4166666667em}

.katex .fontsize-ensurer.reset-size8.size3,.katex .sizing.reset-size8.size3{font-size:.4861111111em}

.katex .fontsize-ensurer.reset-size8.size4,.katex .sizing.reset-size8.size4{font-size:.5555555556em}

.katex .fontsize-ensurer.reset-size8.size5,.katex .sizing.reset-size8.size5{font-size:.625em}

.katex .fontsize-ensurer.reset-size8.size6,.katex .sizing.reset-size8.size6{font-size:.6944444444em}

.katex .fontsize-ensurer.reset-size8.size7,.katex .sizing.reset-size8.size7{font-size:.8333333333em}

.katex .fontsize-ensurer.reset-size8.size8,.katex .sizing.reset-size8.size8{font-size:1em}

.katex .fontsize-ensurer.reset-size8.size9,.katex .sizing.reset-size8.size9{font-size:1.2em}

.katex .fontsize-ensurer.reset-size8.size10,.katex .sizing.reset-size8.size10{font-size:1.4402777778em}

.katex .fontsize-ensurer.reset-size8.size11,.katex .sizing.reset-size8.size11{font-size:1.7277777778em}

.katex .fontsize-ensurer.reset-size9.size1,.katex .sizing.reset-size9.size1{font-size:.2893518519em}

.katex .fontsize-ensurer.reset-size9.size2,.katex .sizing.reset-size9.size2{font-size:.3472222222em}

.katex .fontsize-ensurer.reset-size9.size3,.katex .sizing.reset-size9.size3{font-size:.4050925926em}

.katex .fontsize-ensurer.reset-size9.size4,.katex .sizing.reset-size9.size4{font-size:.462962963em}

.katex .fontsize-ensurer.reset-size9.size5,.katex .sizing.reset-size9.size5{font-size:.5208333333em}

.katex .fontsize-ensurer.reset-size9.size6,.katex .sizing.reset-size9.size6{font-size:.5787037037em}

.katex .fontsize-ensurer.reset-size9.size7,.katex .sizing.reset-size9.size7{font-size:.6944444444em}

.katex .fontsize-ensurer.reset-size9.size8,.katex .sizing.reset-size9.size8{font-size:.8333333333em}

.katex .fontsize-ensurer.reset-size9.size9,.katex .sizing.reset-size9.size9{font-size:1em}

.katex .fontsize-ensurer.reset-size9.size10,.katex .sizing.reset-size9.size10{font-size:1.2002314815em}

.katex .fontsize-ensurer.reset-size9.size11,.katex .sizing.reset-size9.size11{font-size:1.4398148148em}

.katex .fontsize-ensurer.reset-size10.size1,.katex .sizing.reset-size10.size1{font-size:.2410800386em}

.katex .fontsize-ensurer.reset-size10.size2,.katex .sizing.reset-size10.size2{font-size:.2892960463em}

.katex .fontsize-ensurer.reset-size10.size3,.katex .sizing.reset-size10.size3{font-size:.337512054em}

.katex .fontsize-ensurer.reset-size10.size4,.katex .sizing.reset-size10.size4{font-size:.3857280617em}

.katex .fontsize-ensurer.reset-size10.size5,.katex .sizing.reset-size10.size5{font-size:.4339440694em}

.katex .fontsize-ensurer.reset-size10.size6,.katex .sizing.reset-size10.size6{font-size:.4821600771em}

.katex .fontsize-ensurer.reset-size10.size7,.katex .sizing.reset-size10.size7{font-size:.5785920926em}

.katex .fontsize-ensurer.reset-size10.size8,.katex .sizing.reset-size10.size8{font-size:.6943105111em}

.katex .fontsize-ensurer.reset-size10.size9,.katex .sizing.reset-size10.size9{font-size:.8331726133em}

.katex .fontsize-ensurer.reset-size10.size10,.katex .sizing.reset-size10.size10{font-size:1em}

.katex .fontsize-ensurer.reset-size10.size11,.katex .sizing.reset-size10.size11{font-size:1.1996142719em}

.katex .fontsize-ensurer.reset-size11.size1,.katex .sizing.reset-size11.size1{font-size:.2009646302em}

.katex .fontsize-ensurer.reset-size11.size2,.katex .sizing.reset-size11.size2{font-size:.2411575563em}

.katex .fontsize-ensurer.reset-size11.size3,.katex .sizing.reset-size11.size3{font-size:.2813504823em}

.katex .fontsize-ensurer.reset-size11.size4,.katex .sizing.reset-size11.size4{font-size:.3215434084em}

.katex .fontsize-ensurer.reset-size11.size5,.katex .sizing.reset-size11.size5{font-size:.3617363344em}

.katex .fontsize-ensurer.reset-size11.size6,.katex .sizing.reset-size11.size6{font-size:.4019292605em}

.katex .fontsize-ensurer.reset-size11.size7,.katex .sizing.reset-size11.size7{font-size:.4823151125em}

.katex .fontsize-ensurer.reset-size11.size8,.katex .sizing.reset-size11.size8{font-size:.578778135em}

.katex .fontsize-ensurer.reset-size11.size9,.katex .sizing.reset-size11.size9{font-size:.6945337621em}

.katex .fontsize-ensurer.reset-size11.size10,.katex .sizing.reset-size11.size10{font-size:.8336012862em}

.katex .fontsize-ensurer.reset-size11.size11,.katex .sizing.reset-size11.size11{font-size:1em}

.katex .delimsizing.size1{font-family:KaTeX_Size1}

.katex .delimsizing.size2{font-family:KaTeX_Size2}

.katex .delimsizing.size3{font-family:KaTeX_Size3}

.katex .delimsizing.size4{font-family:KaTeX_Size4}

.katex .delimsizing.mult .delim-size1>span{font-family:KaTeX_Size1}

.katex .delimsizing.mult .delim-size4>span{font-family:KaTeX_Size4}

.katex .nulldelimiter{display:inline-block;width:.12em}

.katex .delimcenter,.katex .op-symbol{position:relative}

.katex .op-symbol.small-op{font-family:KaTeX_Size1}

.katex .op-symbol.large-op{font-family:KaTeX_Size2}

.katex .accent>.vlist-t,.katex .op-limits>.vlist-t{text-align:center}

.katex .accent .accent-body{position:relative}

.katex .accent .accent-body:not(.accent-full){width:0}

.katex .overlay{display:block}

.katex .mtable .vertical-separator{display:inline-block;min-width:1px}

.katex .mtable .arraycolsep{display:inline-block}

.katex .mtable .col-align-c>.vlist-t{text-align:center}

.katex .mtable .col-align-l>.vlist-t{text-align:left}

.katex .mtable .col-align-r>.vlist-t{text-align:right}

.katex .svg-align{text-align:left}

.katex svg{fill:currentColor;stroke:currentColor;fill-rule:nonzero;fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;display:block;height:inherit;position:absolute;width:100%}

.katex svg path{stroke:none}

.katex img{border-style:none;max-height:none;max-width:none;min-height:0;min-width:0}

.katex .stretchy{display:block;overflow:hidden;position:relative;width:100%}

.katex .stretchy:after,.katex .stretchy:before{content:""}

.katex .hide-tail{overflow:hidden;position:relative;width:100%}

.katex .halfarrow-left{left:0;overflow:hidden;position:absolute;width:50.2%}

.katex .halfarrow-right{overflow:hidden;position:absolute;right:0;width:50.2%}

.katex .brace-left{left:0;overflow:hidden;position:absolute;width:25.1%}

.katex .brace-center{left:25%;overflow:hidden;position:absolute;width:50%}

.katex .brace-right{overflow:hidden;position:absolute;right:0;width:25.1%}

.katex .x-arrow-pad{padding:0 .5em}

.katex .cd-arrow-pad{padding:0 .55556em 0 .27778em}

.katex .mover,.katex .munder,.katex .x-arrow{text-align:center}

.katex .boxpad{padding:0 .3em}

.katex .fbox,.katex .fcolorbox{border:.04em solid;box-sizing:border-box}

.katex .cancel-pad{padding:0 .2em}

.katex .cancel-lap{margin-left:-.2em;margin-right:-.2em}

.katex .sout{border-bottom-style:solid;border-bottom-width:.08em}

.katex .angl{border-right:.049em solid;border-top:.049em solid;box-sizing:border-box;margin-right:.03889em}

.katex .anglpad{padding:0 .03889em}

.katex .eqn-num:before{content:"(" counter(katexEqnNo) ")";counter-increment:katexEqnNo}

.katex .mml-eqn-num:before{content:"(" counter(mmlEqnNo) ")";counter-increment:mmlEqnNo}

.katex .mtr-glue{width:50%}

.katex .cd-vert-arrow{display:inline-block;position:relative}

.katex .cd-label-left{display:inline-block;position:absolute;right:calc(50% + .3em);text-align:left}

.katex .cd-label-right{display:inline-block;left:calc(50% + .3em);position:absolute;text-align:right}

.katex-display{display:block;margin:1em 0;text-align:center}

.katex-display>.katex{display:block;text-align:center;white-space:nowrap}

.katex-display>.katex>.katex-html{display:block;position:relative}

.katex-display>.katex>.katex-html>.tag{position:absolute;right:0}

.katex-display.leqno>.katex>.katex-html>.tag{left:0;right:auto}

.katex-display.fleqn>.katex{padding-left:2em;text-align:left}

body{counter-reset:katexEqnNo mmlEqnNo}

.milkdown span[data-type='math_inline'] {
    padding: 0 4px;
    display: inline-block;
    vertical-align: bottom;
    color: var(--crepe-color-primary);
  }

.milkdown .milkdown-latex-inline-edit[data-show='false'] {
      display: none;
    }

.milkdown .milkdown-latex-inline-edit{
    position: absolute;
    background: var(--crepe-color-surface);
    box-shadow: var(--crepe-shadow-1);
    border-radius: 8px;
    padding: 2px 6px 2px 12px;
}

.milkdown .milkdown-latex-inline-edit .container {
      display: flex;
      gap: 6px;
      align-items: flex-start;
    }

.milkdown .milkdown-latex-inline-edit .container button {
        width: 24px;
        height: 24px;
        cursor: pointer;
        border-radius: 8px;
      }

.milkdown .milkdown-latex-inline-edit .container button:hover {
          background: var(--crepe-color-hover);
        }

.milkdown .milkdown-latex-inline-edit .ProseMirror {
      padding: 0;
      min-width: 174px;
      max-width: 294px;
      font-family: var(--crepe-font-code);
    }
.milkdown {
  --crepe-color-background: #ffffff;
  --crepe-color-on-background: #000000;
  --crepe-color-surface: #f7f7f7;
  --crepe-color-surface-low: #ededed;
  --crepe-color-on-surface: #1c1c1c;
  --crepe-color-on-surface-variant: #4d4d4d;
  --crepe-color-outline: #a8a8a8;
  --crepe-color-primary: #333333;
  --crepe-color-secondary: #cfcfcf;
  --crepe-color-on-secondary: #000000;
  --crepe-color-inverse: #f0f0f0;
  --crepe-color-on-inverse: #1a1a1a;
  --crepe-color-inline-code: #ba1a1a;
  --crepe-color-error: #ba1a1a;
  --crepe-color-hover: #e0e0e0;
  --crepe-color-selected: #d5d5d5;
  --crepe-color-inline-area: #cacaca;

  --crepe-font-title: 'Noto Serif', Cambria, 'Times New Roman', Times, serif;
  --crepe-font-default: 'Noto Sans', Arial, Helvetica, sans-serif;
  --crepe-font-code:
    'Space Mono', Fira Code, Menlo, Monaco, 'Courier New', Courier, monospace;

  --crepe-shadow-1:
    0px 1px 3px 1px rgba(0, 0, 0, 0.15), 0px 1px 2px 0px rgba(0, 0, 0, 0.3);
  --crepe-shadow-2:
    0px 2px 6px 2px rgba(0, 0, 0, 0.15), 0px 1px 2px 0px rgba(0, 0, 0, 0.3);
}
/* WikiLink 插件样式 */

/* 基础 WikiLink 样式 */
.wikilink {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
  cursor: pointer;
  padding: 1px 2px;
  border-radius: 2px;
}

.wikilink:hover {
  background-color: #eff6ff;
  border-bottom-color: #3b82f6;
}

/* 有效链接样式 */
.wikilink-valid {
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.05);
}

.wikilink-valid:hover {
  background-color: #eff6ff;
  color: #1d4ed8;
}

/* 无效链接样式 */
.wikilink-invalid {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.05);
  text-decoration: line-through;
}

.wikilink-invalid:hover {
  background-color: #fef2f2;
  color: #dc2626;
}

/* 自动补全弹出窗口样式 */
.wikilink-autocomplete-popup {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-height: 200px;
  overflow-y: auto;
  min-width: 200px;
  z-index: 1000;
}

.autocomplete-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.15s ease;
}

.autocomplete-item:last-child {
  border-bottom: none;
}

.autocomplete-item:hover,
.autocomplete-item.selected {
  background-color: #f3f4f6;
}

.autocomplete-no-results {
  padding: 8px 12px;
  color: #6b7280;
  font-style: italic;
  text-align: center;
}

/* 预览提示框样式 */
.wikilink-preview-tooltip {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-width: 400px;
  max-height: 300px;
  overflow: auto;
  padding: 16px;
  font-size: 14px;
  line-height: 1.5;
  z-index: 1000;
}

.wikilink-preview-tooltip h1,
.wikilink-preview-tooltip h2,
.wikilink-preview-tooltip h3,
.wikilink-preview-tooltip h4,
.wikilink-preview-tooltip h5,
.wikilink-preview-tooltip h6 {
  margin: 0 0 8px 0;
  font-weight: 600;
  color: #1f2937;
}

.wikilink-preview-tooltip h1 { font-size: 18px; }
.wikilink-preview-tooltip h2 { font-size: 16px; }
.wikilink-preview-tooltip h3 { font-size: 15px; }
.wikilink-preview-tooltip h4 { font-size: 14px; }
.wikilink-preview-tooltip h5 { font-size: 13px; }
.wikilink-preview-tooltip h6 { font-size: 12px; }

.wikilink-preview-tooltip p {
  margin: 4px 0;
  color: #374151;
}

.wikilink-preview-tooltip code {
  background: #f3f4f6;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.wikilink-preview-tooltip strong {
  font-weight: 600;
  color: #1f2937;
}

.wikilink-preview-tooltip em {
  font-style: italic;
  color: #4b5563;
}

.preview-more {
  color: #6b7280;
  font-style: italic;
  margin-top: 8px;
}

.preview-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: #6b7280;
}

.preview-error {
  color: #ef4444;
  text-align: center;
  padding: 8px;
}

/* 编辑器内的 WikiLink 输入提示 */
.ProseMirror .wikilink-input {
  background-color: #fef3c7;
  padding: 1px 2px;
  border-radius: 2px;
  border: 1px dashed #f59e0b;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .wikilink-autocomplete-popup {
    max-width: calc(100vw - 20px);
    min-width: 150px;
  }
  
  .wikilink-preview-tooltip {
    max-width: calc(100vw - 20px);
    max-height: 250px;
    padding: 12px;
    font-size: 13px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .wikilink {
    color: #60a5fa;
  }
  
  .wikilink:hover {
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  .wikilink-valid {
    color: #60a5fa;
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  .wikilink-valid:hover {
    background-color: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
  }
  
  .wikilink-invalid {
    color: #f87171;
    background-color: rgba(239, 68, 68, 0.1);
  }
  
  .wikilink-invalid:hover {
    background-color: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
  }
  
  .wikilink-autocomplete-popup {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .autocomplete-item:hover,
  .autocomplete-item.selected {
    background-color: #374151;
  }
  
  .autocomplete-no-results {
    color: #9ca3af;
  }
  
  .wikilink-preview-tooltip {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .wikilink-preview-tooltip h1,
  .wikilink-preview-tooltip h2,
  .wikilink-preview-tooltip h3,
  .wikilink-preview-tooltip h4,
  .wikilink-preview-tooltip h5,
  .wikilink-preview-tooltip h6 {
    color: #f9fafb;
  }
  
  .wikilink-preview-tooltip p {
    color: #d1d5db;
  }
  
  .wikilink-preview-tooltip code {
    background: #374151;
    color: #f9fafb;
  }
  
  .wikilink-preview-tooltip strong {
    color: #f9fafb;
  }
  
  .wikilink-preview-tooltip em {
    color: #9ca3af;
  }
  
  .preview-more {
    color: #9ca3af;
  }
  
  .preview-loading {
    color: #9ca3af;
  }
  
  .preview-error {
    color: #f87171;
  }
}
