import React from 'react'
import { cn } from '../../lib/utils'
import { Button } from '../ui/button'
import { Card, CardContent } from '../ui/card'
import { useLanguage } from '../../contexts/LanguageContext'

interface EmptyStateProps {
  title: string
  description?: string
  icon?: React.ReactNode
  action?: {
    label: string
    onClick: () => void
    variant?: 'default' | 'outline' | 'secondary'
  }
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

const sizeClasses = {
  sm: 'py-8',
  md: 'py-12',
  lg: 'py-16'
}

const iconSizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-12 h-12',
  lg: 'w-16 h-16'
}

export function EmptyState({
  title,
  description,
  icon,
  action,
  className,
  size = 'md'
}: EmptyStateProps) {
  return (
    <div className={cn(
      'relative group',
      'bg-gradient-to-br from-card/50 to-card/80 backdrop-blur-sm',
      'border border-border/50 rounded-xl',
      'shadow-lg hover:shadow-xl transition-all duration-500',
      'hover:scale-[1.02] hover:border-border/80',
      className
    )}>
      {/* 背景光效 */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

      {/* 内容 */}
      <div className={cn(
        'relative flex flex-col items-center justify-center text-center',
        'backdrop-blur-sm rounded-xl',
        sizeClasses[size]
      )}>
        {icon && (
          <div className={cn(
            'mb-6 relative',
            'text-muted-foreground/60 group-hover:text-muted-foreground/80',
            'transition-all duration-500',
            iconSizeClasses[size]
          )}>
            {/* 图标光晕效果 */}
            <div className="absolute inset-0 bg-primary/20 rounded-full blur-xl opacity-0 group-hover:opacity-50 transition-opacity duration-500" />

            {React.cloneElement(icon as React.ReactElement, {
              className: cn(
                'relative z-10 drop-shadow-sm',
                iconSizeClasses[size],
                (icon as React.ReactElement).props.className
              )
            })}
          </div>
        )}

        <h3 className={cn(
          'font-semibold mb-3 text-foreground/90 group-hover:text-foreground',
          'transition-colors duration-300',
          size === 'sm' ? 'text-base' : size === 'lg' ? 'text-xl' : 'text-lg'
        )}>
          {title}
        </h3>

        {description && (
          <p className={cn(
            'text-muted-foreground/70 group-hover:text-muted-foreground/90',
            'mb-6 max-w-sm leading-relaxed transition-colors duration-300',
            size === 'sm' ? 'text-xs' : 'text-sm'
          )}>
            {description}
          </p>
        )}

        {action && (
          <Button
            onClick={action.onClick}
            variant={action.variant || 'default'}
            size={size === 'sm' ? 'sm' : 'default'}
            className={cn(
              'shadow-md hover:shadow-lg transition-all duration-300',
              'hover:scale-105 active:scale-95',
              'bg-gradient-to-r from-primary to-primary/90',
              'hover:from-primary/90 hover:to-primary'
            )}
          >
            {action.label}
          </Button>
        )}
      </div>
    </div>
  )
}

// Preset empty states for common scenarios
export const EmptyStates = {
  Projects: ({ onCreate }: { onCreate: () => void }) => {
    const { t } = useLanguage()
    return (
      <EmptyState
        title={t('pages.projects.noProjects', '暂无项目')}
        description={t('pages.projects.createFirst', '创建您的第一个项目，开始管理任务和资源')}
        action={{
          label: t('pages.projects.newProject', '新建项目'),
          onClick: onCreate
        }}
        icon={
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
        }
        size="md"
      />
    )
  },

  Areas: ({ onCreate }: { onCreate: () => void }) => {
    const { t } = useLanguage()
    return (
      <EmptyState
        title={t('pages.areas.noAreas', '暂无领域')}
        description={t('pages.areas.createFirst', '创建您的第一个生活领域，开始规划和管理')}
        action={{
          label: t('pages.areas.newArea', '新建领域'),
          onClick: onCreate
        }}
        icon={
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
        }
        size="md"
      />
    )
  },

  Tasks: ({ onCreate }: { onCreate: () => void }) => (
    <EmptyState
      title="No tasks found"
      description="Add your first task to start tracking your work and progress."
      action={{
        label: 'Add Task',
        onClick: onCreate
      }}
      icon={
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
          />
        </svg>
      }
    />
  ),

  Resources: ({ onCreate }: { onCreate: () => void }) => (
    <EmptyState
      title="No resources saved"
      description="Save important documents, links, and references for future use."
      action={{
        label: 'Add Resource',
        onClick: onCreate
      }}
      icon={
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      }
    />
  ),

  Search: ({ query, onClear }: { query: string; onClear: () => void }) => (
    <EmptyState
      title={`No results for "${query}"`}
      description="Try adjusting your search terms or filters to find what you're looking for."
      action={{
        label: 'Clear Search',
        onClick: onClear,
        variant: 'outline'
      }}
      icon={
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      }
    />
  ),

  Error: ({ onRetry }: { onRetry: () => void }) => (
    <EmptyState
      title="Something went wrong"
      description="We couldn't load the content. Please try again."
      action={{
        label: 'Try Again',
        onClick: onRetry,
        variant: 'outline'
      }}
      icon={
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
      }
    />
  )
}

// Container component for consistent empty state layout
export function EmptyStateContainer({
  children,
  className
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={cn(
      'flex-1 flex items-center justify-center',
      'min-h-[400px] p-8',
      'relative overflow-hidden',
      className
    )}>
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/10" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.1),transparent)]" />

      {/* 内容区域 */}
      <div className="relative w-full max-w-md z-10">
        {children}
      </div>
    </div>
  )
}

export default EmptyState
