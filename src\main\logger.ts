import { app } from 'electron'
import fs from 'fs'
import path from 'path'

// Simple file logger that mirrors console.* to a daily log file under userData/logs
const origLog = console.log
const origWarn = console.warn
const origError = console.error
const origInfo = console.info

function getLogFile() {
  try {
    // app.getPath('userData') can be used early; if not ready, fallback to process.cwd()
    const baseDir = app?.getPath?.('userData') || process.cwd()
    const logDir = path.join(baseDir, 'logs')
    if (!fs.existsSync(logDir)) fs.mkdirSync(logDir, { recursive: true })
    const now = new Date()
    const y = now.getFullYear()
    const m = String(now.getMonth() + 1).padStart(2, '0')
    const d = String(now.getDate()).padStart(2, '0')
    const file = path.join(logDir, `paolife-${y}${m}${d}.log`)
    return file
  } catch {
    return null
  }
}

function write(file: string | null, level: string, args: any[]) {
  try {
    if (!file) return
    const line = `[${new Date().toISOString()}] [${level}] ${args.map(String).join(' ')}\n`
    fs.appendFileSync(file, line)
  } catch {
    // ignore
  }
}

export function installFileLogger() {
  const file = getLogFile()
  console.log = (...args: any[]) => {
    write(file, 'LOG', args)
    origLog(...args)
  }
  console.warn = (...args: any[]) => {
    write(file, 'WARN', args)
    origWarn(...args)
  }
  console.error = (...args: any[]) => {
    write(file, 'ERROR', args)
    origError(...args)
  }
  console.info = (...args: any[]) => {
    write(file, 'INFO', args)
    origInfo(...args)
  }

  console.log('File logger installed. Logs at:', file)
}

// Auto-install on import
try {
  installFileLogger()
} catch (e) {
  // Fallback to console only
  origWarn('Failed to install file logger:', e)
}

