# PaoLife项目详细技术文档

## 第八部分：数据分析和统计页面详细分析

### 页面概览

数据分析和统计模块是PaoLife应用的智能洞察核心，通过多维度数据聚合、趋势分析和预测建议，为用户提供深度的个人效能洞察。包含仪表盘统计、领域分析、项目分析、引用分析和回顾分析等多个子系统。

### 核心架构设计

#### 1. 数据聚合服务 (ReviewDataAggregator)

**数据聚合接口**:
```typescript
interface AggregatedData {
  projects: {
    completed: number
    inProgress: number
    total: number
    completedTasks: number
    totalTasks: number
    completionRate: number
    recentCompletions: Array<{
      name: string
      completedAt: Date
    }>
  }
  areas: {
    total: number
    habitsTracked: number
    habitsCompleted: number
    habitCompletionRate: number
    kpiChanges: Array<{
      areaName: string
      kpiName: string
      previousValue: number
      currentValue: number
      change: number
      changePercent: number
    }>
    recentHabits: Array<{
      areaName: string
      habitName: string
      completedDays: number
      totalDays: number
    }>
  }
  tasks: {
    completed: number
    created: number
    overdue: number
    completionRate: number
    avgCompletionTime: number
    recentCompletions: Array<{
      title: string
      projectName?: string
      completedAt: Date
    }>
  }
  insights: {
    mostActiveArea?: string
    topProject?: string
    achievements: string[]
    improvementAreas: string[]
  }
}
```

**项目数据聚合**:
```typescript
export class ReviewDataAggregator {
  /**
   * 聚合项目数据
   */
  private async aggregateProjectData(period: ReviewPeriod): Promise<AggregatedData['projects']> {
    const { startDate, endDate } = period

    // 获取所有项目及其任务
    const allProjects = await this.prisma.project.findMany({
      include: {
        tasks: true
      }
    })

    // 筛选时间范围内完成的项目
    const completedProjects = allProjects.filter(p => 
      p.completedAt && p.completedAt >= startDate && p.completedAt < endDate
    )

    const inProgressProjects = allProjects.filter(p => 
      p.status === 'In Progress' && p.createdAt < endDate
    )

    // 计算任务统计
    const allTasks = allProjects.flatMap(p => p.tasks)
    const completedTasks = allTasks.filter(t => 
      t.completedAt && t.completedAt >= startDate && t.completedAt < endDate
    )

    const completionRate = allTasks.length > 0 ? (completedTasks.length / allTasks.length) * 100 : 0

    // 获取最近完成的项目
    const recentCompletions = completedProjects
      .sort((a, b) => (b.completedAt?.getTime() || 0) - (a.completedAt?.getTime() || 0))
      .slice(0, 5)
      .map(p => ({
        name: p.name,
        completedAt: p.completedAt!
      }))

    return {
      completed: completedProjects.length,
      inProgress: inProgressProjects.length,
      total: allProjects.length,
      completedTasks: completedTasks.length,
      totalTasks: allTasks.length,
      completionRate: Math.round(completionRate),
      recentCompletions
    }
  }

  /**
   * 聚合领域和习惯数据
   */
  private async aggregateAreaData(period: ReviewPeriod): Promise<AggregatedData['areas']> {
    const { startDate, endDate } = period

    // 获取所有领域及其习惯和KPI
    const areas = await this.prisma.area.findMany({
      include: {
        habits: {
          include: {
            completions: {
              where: {
                date: { gte: startDate, lt: endDate }
              }
            }
          }
        },
        kpis: {
          include: {
            values: {
              where: {
                date: { gte: startDate, lt: endDate }
              },
              orderBy: { date: 'desc' }
            }
          }
        }
      }
    })

    // 计算习惯统计
    let totalHabitsTracked = 0
    let totalHabitsCompleted = 0
    const recentHabits: AggregatedData['areas']['recentHabits'] = []

    areas.forEach(area => {
      area.habits.forEach(habit => {
        const daysInPeriod = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
        const completedDays = habit.completions.length
        
        totalHabitsTracked += daysInPeriod
        totalHabitsCompleted += completedDays

        recentHabits.push({
          areaName: area.name,
          habitName: habit.name,
          completedDays,
          totalDays: daysInPeriod
        })
      })
    })

    const habitCompletionRate = totalHabitsTracked > 0 ? (totalHabitsCompleted / totalHabitsTracked) * 100 : 0

    // 计算KPI变化
    const kpiChanges: AggregatedData['areas']['kpiChanges'] = []
    areas.forEach(area => {
      area.kpis.forEach(kpi => {
        if (kpi.values.length >= 2) {
          const latest = kpi.values[0]
          const previous = kpi.values[kpi.values.length - 1]
          const change = latest.value - previous.value
          const changePercent = previous.value !== 0 ? (change / previous.value) * 100 : 0

          kpiChanges.push({
            areaName: area.name,
            kpiName: kpi.name,
            previousValue: previous.value,
            currentValue: latest.value,
            change,
            changePercent: Math.round(changePercent * 100) / 100
          })
        }
      })
    })

    return {
      total: areas.length,
      habitsTracked: totalHabitsTracked,
      habitsCompleted: totalHabitsCompleted,
      habitCompletionRate: Math.round(habitCompletionRate),
      kpiChanges,
      recentHabits
    }
  }

  /**
   * 生成洞察和建议
   */
  private generateInsights(data: Omit<AggregatedData, 'insights'>): AggregatedData['insights'] {
    const achievements: string[] = []
    const improvementAreas: string[] = []

    // 项目成就
    if (data.projects.completionRate > 75) {
      achievements.push(`High project completion rate of ${data.projects.completionRate}%`)
    }

    // 习惯成就
    if (data.areas.habitCompletionRate > 80) {
      achievements.push(`Excellent habit consistency of ${data.areas.habitCompletionRate}%`)
    }

    // 任务成就
    if (data.tasks.completionRate > 75) {
      achievements.push(`High task completion rate of ${data.tasks.completionRate}%`)
    }

    // 改进领域
    if (data.projects.completionRate < 50) {
      improvementAreas.push('Focus on completing more projects')
    }
    if (data.areas.habitCompletionRate < 50) {
      improvementAreas.push('Improve habit consistency')
    }
    if (data.tasks.overdue > 5) {
      improvementAreas.push('Reduce overdue tasks')
    }

    // 找到最活跃的领域
    const mostActiveArea = data.areas.recentHabits.length > 0
      ? data.areas.recentHabits.reduce((prev, current) => 
          current.completedDays > prev.completedDays ? current : prev
        ).areaName
      : undefined

    // 找到顶级项目
    const topProject = data.tasks.recentCompletions.length > 0
      ? data.tasks.recentCompletions[0].projectName
      : undefined

    return {
      mostActiveArea,
      topProject,
      achievements,
      improvementAreas
    }
  }

  /**
   * 聚合所有数据
   */
  async aggregateReviewData(type: string, period: string): Promise<AggregatedData> {
    const reviewPeriod = this.parsePeriod(type, period)

    const [projects, areas, tasks] = await Promise.all([
      this.aggregateProjectData(reviewPeriod),
      this.aggregateAreaData(reviewPeriod),
      this.aggregateTaskData(reviewPeriod)
    ])

    const insights = this.generateInsights({ projects, areas, tasks })

    return {
      projects,
      areas,
      tasks,
      insights
    }
  }
}
```

#### 2. 回顾分析器 (ReviewAnalyzer)

**分析结果接口**:
```typescript
interface AnalysisResult {
  trends: {
    projectCompletion: TrendData
    taskProductivity: TrendData
    habitConsistency: TrendData
  }
  predictions: {
    nextPeriodProjections: {
      expectedProjects: number
      expectedTasks: number
      riskFactors: string[]
    }
    goalAchievementProbability: number
    recommendedFocus: string[]
  }
  recommendations: string[]
  goalProgress: {
    overallScore: number
    categoryScores: {
      projects: number
      habits: number
      kpis: number
    }
    achievements: string[]
    gaps: string[]
  }
}

interface TrendData {
  current: number
  previous: number
  trend: 'up' | 'down' | 'stable'
  change: number
}
```

**趋势分析**:
```typescript
export class ReviewAnalyzer {
  /**
   * 分析趋势：比较当前周期与上一周期
   */
  private async analyzeTrends(type: string, currentPeriod: string): Promise<AnalysisResult['trends']> {
    const previousPeriod = this.getPreviousPeriod(type, currentPeriod)

    // 获取当前和上一周期数据
    const currentData = await this.getAggregatedData(type, currentPeriod)
    const previousData = await this.getAggregatedData(type, previousPeriod)

    const calculateTrend = (current: number, previous: number) => {
      const change = current - previous
      const changePercent = previous > 0 ? (change / previous) * 100 : 0

      let trend: 'up' | 'down' | 'stable' = 'stable'
      if (Math.abs(changePercent) > 5) {
        trend = changePercent > 0 ? 'up' : 'down'
      }

      return { current, previous, trend, change: Math.round(changePercent * 100) / 100 }
    }

    return {
      projectCompletion: calculateTrend(
        currentData.projects?.completionRate || 0,
        previousData.projects?.completionRate || 0
      ),
      taskProductivity: calculateTrend(
        currentData.tasks?.completionRate || 0,
        previousData.tasks?.completionRate || 0
      ),
      habitConsistency: calculateTrend(
        currentData.areas?.habitCompletionRate || 0,
        previousData.areas?.habitCompletionRate || 0
      )
    }
  }

  /**
   * 生成预测和建议
   */
  private async generatePredictions(type: string, currentPeriod: string): Promise<AnalysisResult['predictions']> {
    // 获取历史数据（最近6个周期）
    const historicalData = await this.getHistoricalData(type, currentPeriod, 6)

    // 计算平均值
    const avgProjects = historicalData.reduce((sum, data) => sum + (data.projects?.completed || 0), 0) / historicalData.length
    const avgTasks = historicalData.reduce((sum, data) => sum + (data.tasks?.completed || 0), 0) / historicalData.length
    const avgHabitRate = historicalData.reduce((sum, data) => sum + (data.areas?.habitCompletionRate || 0), 0) / historicalData.length

    // 识别风险因素
    const riskFactors: string[] = []
    const currentData = historicalData[0] // 最新数据

    if ((currentData.tasks?.overdue || 0) > 5) {
      riskFactors.push('High number of overdue tasks')
    }
    if ((currentData.projects?.completionRate || 0) < 50) {
      riskFactors.push('Low project completion rate')
    }
    if ((currentData.areas?.habitCompletionRate || 0) < 50) {
      riskFactors.push('Declining habit consistency')
    }

    // 计算目标达成概率
    const goalAchievementProbability = Math.min(100, (avgProjects * 10 + avgHabitRate + avgTasks * 2) / 3)

    // 推荐关注领域
    const recommendedFocus: string[] = []
    if (avgProjects < 2) recommendedFocus.push('Increase project completion')
    if (avgTasks < 10) recommendedFocus.push('Improve task productivity')
    if (avgHabitRate < 70) recommendedFocus.push('Strengthen habit consistency')

    return {
      nextPeriodProjections: {
        expectedProjects: Math.round(avgProjects),
        expectedTasks: Math.round(avgTasks),
        riskFactors
      },
      goalAchievementProbability: Math.round(goalAchievementProbability),
      recommendedFocus
    }
  }

  /**
   * 评估目标进度
   */
  private async evaluateGoalProgress(type: string, currentPeriod: string): Promise<AnalysisResult['goalProgress']> {
    const data = await this.getAggregatedData(type, currentPeriod)

    // 计算分类得分
    const projectScore = Math.min(100, (data.projects?.completionRate || 0))
    const habitScore = Math.min(100, (data.areas?.habitCompletionRate || 0))
    const kpiScore = this.calculateKpiScore(data.areas?.kpiChanges || [])

    const overallScore = Math.round((projectScore + habitScore + kpiScore) / 3)

    // 识别成就
    const achievements: string[] = []
    if (projectScore > 80) achievements.push('Excellent project completion rate')
    if (habitScore > 80) achievements.push('Outstanding habit consistency')
    if (kpiScore > 80) achievements.push('Strong KPI performance')
    if ((data.tasks?.completed || 0) > 20) achievements.push('High task completion volume')

    // 识别差距
    const gaps: string[] = []
    if (projectScore < 50) gaps.push('Project completion needs improvement')
    if (habitScore < 50) gaps.push('Habit consistency requires attention')
    if (kpiScore < 50) gaps.push('KPI performance is below expectations')
    if ((data.tasks?.overdue || 0) > 5) gaps.push('Too many overdue tasks')

    return {
      overallScore,
      categoryScores: {
        projects: Math.round(projectScore),
        habits: Math.round(habitScore),
        kpis: Math.round(kpiScore)
      },
      achievements,
      gaps
    }
  }

  /**
   * 执行完整分析
   */
  async analyzeReview(type: string, period: string): Promise<AnalysisResult> {
    const [trends, predictions, goalProgress] = await Promise.all([
      this.analyzeTrends(type, period),
      this.generatePredictions(type, period),
      this.evaluateGoalProgress(type, period)
    ])

    const recommendations = this.generateRecommendations(trends, predictions)

    return {
      trends,
      predictions,
      recommendations,
      goalProgress
    }
  }
}
```

#### 3. KPI进度计算器 (KPIProgressCalculator)

**通用KPI接口**:
```typescript
interface GenericKPI {
  name: string
  value: string
  target?: string
  unit?: string
  direction?: string
}

interface NumericKPI {
  id: string
  name: string
  value: number
  target?: number
  unit?: string
  direction: 'increase' | 'decrease'
  frequency?: string
  updatedAt: Date
}
```

**双向进度计算**:
```typescript
/**
 * 计算KPI进度，支持增长型和减少型指标
 */
export function calculateKPIProgress(kpi: GenericKPI): number {
  if (!kpi.target) return 0

  const current = parseFloat(kpi.value)
  const target = parseFloat(kpi.target)

  if (isNaN(current) || isNaN(target)) return 0

  const direction = getKPIDirection(kpi)

  if (direction === 'decrease') {
    // 减少型指标：需要从当前值减少到目标值
    if (target >= current) {
      // 已经达到或超过目标
      return 100
    }

    // 计算减少的进度
    // 使用智能的起始值估算
    let estimatedStart: number

    // 如果目标是0，起始值设为当前值的2倍
    if (target === 0) {
      estimatedStart = current * 2
    } else {
      // 否则，起始值设为目标值和当前值之间的合理值
      estimatedStart = Math.max(current * 1.5, target * 2)
    }

    const totalReduction = estimatedStart - target
    const currentReduction = estimatedStart - current

    if (totalReduction <= 0) return 100

    return Math.min((currentReduction / totalReduction) * 100, 100)
  } else {
    // 增长型指标：需要从当前值增长到目标值
    if (target === 0) return 0
    return Math.min((current / target) * 100, 100)
  }
}

/**
 * 获取KPI状态
 */
export function getKPIStatus(progress: number): {
  status: 'achieved' | 'on-track' | 'at-risk' | 'behind'
  color: string
  label: string
} {
  if (progress >= 100) {
    return {
      status: 'achieved',
      color: 'text-green-600',
      label: 'Achieved'
    }
  } else if (progress >= 75) {
    return {
      status: 'on-track',
      color: 'text-blue-600',
      label: 'On Track'
    }
  } else if (progress >= 50) {
    return {
      status: 'at-risk',
      color: 'text-yellow-600',
      label: 'At Risk'
    }
  } else {
    return {
      status: 'behind',
      color: 'text-red-600',
      label: 'Behind'
    }
  }
}

/**
 * 批量计算KPI统计
 */
export function calculateKPIStatistics(kpis: GenericKPI[]): {
  total: number
  achieved: number
  onTrack: number
  atRisk: number
  behind: number
  averageProgress: number
  withTargets: number
} {
  const kpisWithTargets = kpis.filter(kpi => kpi.target)

  if (kpisWithTargets.length === 0) {
    return {
      total: kpis.length,
      achieved: 0,
      onTrack: 0,
      atRisk: 0,
      behind: 0,
      averageProgress: 0,
      withTargets: 0
    }
  }

  let achieved = 0
  let onTrack = 0
  let atRisk = 0
  let behind = 0
  let totalProgress = 0

  kpisWithTargets.forEach(kpi => {
    const progress = calculateKPIProgress(kpi)
    totalProgress += progress

    if (progress >= 100) achieved++
    else if (progress >= 75) onTrack++
    else if (progress >= 50) atRisk++
    else behind++
  })

  return {
    total: kpis.length,
    achieved,
    onTrack,
    atRisk,
    behind,
    averageProgress: Math.round(totalProgress / kpisWithTargets.length),
    withTargets: kpisWithTargets.length
  }
}
```

#### 4. 领域分析组件 (AreaAnalytics)

**分析数据接口**:
```typescript
interface AnalyticsData {
  totalMetrics: number
  activeMetrics: number
  habitMetrics: number
  standardMetrics: number
  averageProgress: number
  topPerformers: AreaMetric[]
  needsAttention: AreaMetric[]
  weeklyTrends: { [key: string]: number }
  categoryDistribution: { [key: string]: number }
  priorityDistribution: { [key: string]: number }
  insights: string[]
  recommendations: string[]
}
```

**分析逻辑实现**:
```typescript
export function AreaAnalytics({ areaId, metrics, className }: AreaAnalyticsProps) {
  const { t } = useLanguage()
  const [records, setRecords] = useState<{ [metricId: string]: AreaMetricRecord[] }>({})
  const [loading, setLoading] = useState(true)

  // 计算分析数据
  const analyticsData = useMemo((): AnalyticsData => {
    if (metrics.length === 0) {
      return {
        totalMetrics: 0,
        activeMetrics: 0,
        habitMetrics: 0,
        standardMetrics: 0,
        averageProgress: 0,
        topPerformers: [],
        needsAttention: [],
        weeklyTrends: {},
        categoryDistribution: {},
        priorityDistribution: {},
        insights: [],
        recommendations: []
      }
    }

    // 基础统计
    const activeMetrics = metrics.filter(m => (m as any).isActive !== false)
    const habitMetrics = metrics.filter(m => (m as any).trackingType === 'habit')
    const standardMetrics = metrics.filter(m => (m as any).trackingType === 'standard')

    // 进度计算，支持增长型和减少型指标
    const metricsWithProgress = metrics.map(metric => {
      if (!metric.target) return { metric, progress: 0 }
      return { metric, progress: calculateAreaMetricProgress(metric) }
    })

    const averageProgress = metricsWithProgress.length > 0
      ? metricsWithProgress.reduce((sum, item) => sum + item.progress, 0) / metricsWithProgress.length
      : 0

    // 识别表现优秀和需要关注的指标
    const topPerformers = metricsWithProgress
      .filter(item => item.progress >= 80)
      .sort((a, b) => b.progress - a.progress)
      .slice(0, 3)
      .map(item => item.metric)

    const needsAttention = metricsWithProgress
      .filter(item => item.progress < 50 && item.metric.target)
      .sort((a, b) => a.progress - b.progress)
      .slice(0, 3)
      .map(item => item.metric)

    // 生成洞察
    const insights: string[] = []
    const recommendations: string[] = []

    if (habitMetrics.length > 0) {
      const habitProgress = habitMetrics.reduce((sum, metric) => {
        return sum + calculateAreaMetricProgress(metric)
      }, 0) / habitMetrics.length

      if (habitProgress > 80) {
        insights.push(t('pages.areas.detail.kpiManagement.areaAnalytics.excellentHabitConsistency', { count: habitMetrics.length }))
      } else if (habitProgress < 50) {
        insights.push(t('pages.areas.detail.kpiManagement.areaAnalytics.habitFormationNeedsAttention'))
        recommendations.push(t('pages.areas.detail.kpiManagement.areaAnalytics.focusOnCoreHabits'))
      }
    }

    if (topPerformers.length > 0) {
      insights.push(`${topPerformers.length} metrics are performing excellently`)
    }

    if (needsAttention.length > 0) {
      recommendations.push(`Focus on improving ${needsAttention.length} underperforming metrics`)
    }

    return {
      totalMetrics: metrics.length,
      activeMetrics: activeMetrics.length,
      habitMetrics: habitMetrics.length,
      standardMetrics: standardMetrics.length,
      averageProgress: Math.round(averageProgress),
      topPerformers,
      needsAttention,
      weeklyTrends: {},
      categoryDistribution: {},
      priorityDistribution: {},
      insights,
      recommendations
    }
  }, [metrics, records])

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          {t('pages.areas.detail.kpiManagement.areaAnalytics.title')}
        </CardTitle>
        <CardDescription>
          {t('pages.areas.detail.kpiManagement.areaAnalytics.description')}
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="performance">表现</TabsTrigger>
            <TabsTrigger value="trends">趋势</TabsTrigger>
            <TabsTrigger value="insights">洞察</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* 统计卡片 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold">{analyticsData.totalMetrics}</div>
                  <div className="text-sm text-muted-foreground">总指标</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">{analyticsData.activeMetrics}</div>
                  <div className="text-sm text-muted-foreground">活跃指标</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">{analyticsData.averageProgress}%</div>
                  <div className="text-sm text-muted-foreground">平均进度</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">{analyticsData.habitMetrics}</div>
                  <div className="text-sm text-muted-foreground">习惯指标</div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="insights" className="space-y-4">
            {/* 洞察和建议 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Lightbulb className="h-4 w-4" />
                    洞察
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {analyticsData.insights.length > 0 ? (
                    <ul className="space-y-2">
                      {analyticsData.insights.map((insight, index) => (
                        <li key={index} className="flex items-start gap-2 text-sm">
                          <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" />
                          {insight}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">暂无洞察</p>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    建议
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {analyticsData.recommendations.length > 0 ? (
                    <ul className="space-y-2">
                      {analyticsData.recommendations.map((recommendation, index) => (
                        <li key={index} className="flex items-start gap-2 text-sm">
                          <div className="w-1.5 h-1.5 rounded-full bg-orange-500 mt-2 flex-shrink-0" />
                          {recommendation}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">暂无建议</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
```

#### 5. 引用分析仪表盘 (ReferenceAnalyticsDashboard)

**引用分析数据结构**:
```typescript
interface ReferenceAnalyticsData {
  references: UnifiedReference[]
  trends: {
    daily: { date: string; count: number }[]
    weekly: { week: string; count: number }[]
    monthly: { month: string; count: number }[]
  }
  topReferences: {
    mostReferenced: UnifiedReference[]
    strongestConnections: UnifiedReference[]
    recentlyActive: UnifiedReference[]
  }
  insights: {
    totalConnections: number
    averageStrength: number
    diversityIndex: number
    growthRate: number
    recommendations: string[]
  }
}
```

**引用分析组件**:
```typescript
export function ReferenceAnalyticsDashboard({ documentPath, className }: ReferenceAnalyticsDashboardProps) {
  const [analyticsData, setAnalyticsData] = useState<ReferenceAnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d')

  const loadAnalyticsData = async () => {
    setLoading(true)
    try {
      console.log('📊 加载引用分析数据...')

      // 获取引用数据
      const references = await unifiedReferenceService.getAllReferences(documentPath)

      // 生成趋势数据
      const trends = generateTrendData(references)

      // 分析顶级引用
      const topReferences = analyzeTopReferences(references)

      // 生成洞察
      const insights = generateInsights(references)

      setAnalyticsData({
        references,
        trends,
        topReferences,
        insights
      })

      console.log('✅ 分析数据加载完成')
    } catch (error) {
      console.error('❌ 加载分析数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 生成趋势数据
  const generateTrendData = (references: UnifiedReference[]) => {
    const now = new Date()
    const days = parseInt(timeRange.replace('d', ''))

    const daily = Array.from({ length: days }, (_, i) => {
      const date = new Date(now)
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]

      const count = references.filter(ref => {
        const refDate = new Date(ref.createdAt).toISOString().split('T')[0]
        return refDate === dateStr
      }).length

      return { date: dateStr, count }
    }).reverse()

    return {
      daily,
      weekly: [], // 可以根据需要实现
      monthly: [] // 可以根据需要实现
    }
  }

  // 分析顶级引用
  const analyzeTopReferences = (references: UnifiedReference[]) => {
    // 按引用强度排序
    const strongestConnections = [...references]
      .sort((a, b) => b.strength - a.strength)
      .slice(0, 5)

    // 按创建时间排序（最近活跃）
    const recentlyActive = [...references]
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5)

    // 按引用频次排序（模拟数据）
    const mostReferenced = [...references]
      .sort((a, b) => b.strength - a.strength)
      .slice(0, 5)

    return {
      mostReferenced,
      strongestConnections,
      recentlyActive
    }
  }

  // 生成洞察
  const generateInsights = (references: UnifiedReference[]) => {
    const wikiLinks = references.filter(ref => ref.referenceType === 'wikilink').length
    const projectRefs = references.filter(ref => ref.referenceType === 'task' || ref.referenceType === 'description').length
    const areaRefs = references.filter(ref => ref.referenceType === 'note').length

    const total = references.length
    const avgStrength = total > 0 ? references.reduce((sum, ref) => sum + ref.strength, 0) / total : 0

    // 计算多样性指数（基于引用类型分布）
    const diversityIndex = total > 0 ?
      1 - ((wikiLinks/total)**2 + (projectRefs/total)**2 + (areaRefs/total)**2) : 0

    // 生成建议
    const recommendations: string[] = []

    if (total < 5) {
      recommendations.push('考虑增加更多的文档链接来建立知识网络')
    }

    if (avgStrength < 0.5) {
      recommendations.push('提高引用质量，增加更有意义的连接')
    }

    if (diversityIndex < 0.3) {
      recommendations.push('尝试连接不同类型的内容以增加多样性')
    }

    if (wikiLinks === 0) {
      recommendations.push('使用WikiLink语法创建文档间的双向链接')
    }

    return {
      totalConnections: total,
      averageStrength: Math.round(avgStrength * 100) / 100,
      diversityIndex: Math.round(diversityIndex * 100) / 100,
      growthRate: 0, // 可以根据历史数据计算
      recommendations
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              引用分析
            </CardTitle>
            <CardDescription>
              文档引用网络的深度分析和洞察
            </CardDescription>
          </div>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7天</SelectItem>
              <SelectItem value="30d">30天</SelectItem>
              <SelectItem value="90d">90天</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        {loading ? (
          <div className="text-center py-8">
            <Activity className="h-8 w-8 mx-auto mb-2 animate-pulse" />
            <p>分析引用数据中...</p>
          </div>
        ) : analyticsData ? (
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">概览</TabsTrigger>
              <TabsTrigger value="trends">趋势</TabsTrigger>
              <TabsTrigger value="insights">洞察</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              {/* 统计概览 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold">{analyticsData.insights.totalConnections}</div>
                    <div className="text-sm text-muted-foreground">总连接数</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{analyticsData.insights.averageStrength}</div>
                    <div className="text-sm text-muted-foreground">平均强度</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{analyticsData.insights.diversityIndex}</div>
                    <div className="text-sm text-muted-foreground">多样性指数</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">{analyticsData.insights.growthRate}%</div>
                    <div className="text-sm text-muted-foreground">增长率</div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="insights" className="space-y-4">
              {/* 建议和洞察 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Lightbulb className="h-4 w-4" />
                    智能建议
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {analyticsData.insights.recommendations.length > 0 ? (
                    <ul className="space-y-2">
                      {analyticsData.insights.recommendations.map((recommendation, index) => (
                        <li key={index} className="flex items-start gap-2 text-sm">
                          <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" />
                          {recommendation}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">引用网络状态良好，无特殊建议</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">📊</div>
            <p>暂无分析数据</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
```

### 总结

数据分析和统计模块作为PaoLife应用的智能洞察核心，具有以下特点：

1. **多维度数据聚合**: 项目、领域、任务、习惯的全方位数据收集
2. **智能趋势分析**: 历史对比、变化趋势、预测建议
3. **双向KPI计算**: 支持增长型和减少型指标的准确进度计算
4. **深度洞察生成**: 自动识别成就、问题和改进机会
5. **可视化分析**: 图表展示、进度跟踪、分布统计
6. **个性化建议**: 基于数据模式的智能推荐系统

这个设计为用户提供了专业级的数据分析能力，支持基于数据驱动的个人效能优化决策。
