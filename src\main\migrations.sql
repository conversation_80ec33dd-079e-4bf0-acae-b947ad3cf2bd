-- Database migration script for <PERSON><PERSON><PERSON>
-- Creates all tables matching the Prisma schema

-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- User table
CREATE TABLE IF NOT EXISTS "User" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "username" TEXT NOT NULL UNIQUE,
  "password" TEXT NOT NULL,
  "settings" TEXT
);

-- Area table
CREATE TABLE IF NOT EXISTS "Area" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "standard" TEXT,
  "description" TEXT,
  "icon" TEXT,
  "color" TEXT,
  "archived" INTEGER NOT NULL DEFAULT 0,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Project table
CREATE TABLE IF NOT EXISTS "Project" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "status" TEXT NOT NULL DEFAULT 'Not Started',
  "progress" INTEGER NOT NULL DEFAULT 0,
  "goal" TEXT,
  "deliverable" TEXT,
  "startDate" TEXT,
  "deadline" TEXT,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "archived" INTEGER NOT NULL DEFAULT 0,
  "areaId" TEXT,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- ProjectKPI table
CREATE TABLE IF NOT EXISTS "ProjectKPI" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "value" TEXT NOT NULL,
  "target" TEXT,
  "unit" TEXT,
  "frequency" TEXT,
  "direction" TEXT NOT NULL DEFAULT 'increase',
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "projectId" TEXT NOT NULL,
  FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- KPIRecord table
CREATE TABLE IF NOT EXISTS "KPIRecord" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "value" TEXT NOT NULL,
  "note" TEXT,
  "recordedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "kpiId" TEXT NOT NULL,
  FOREIGN KEY ("kpiId") REFERENCES "ProjectKPI" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- Deliverable table
CREATE TABLE IF NOT EXISTS "Deliverable" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "title" TEXT NOT NULL,
  "description" TEXT,
  "type" TEXT NOT NULL DEFAULT 'document',
  "status" TEXT NOT NULL DEFAULT 'planned',
  "content" TEXT,
  "url" TEXT,
  "filePath" TEXT,
  "acceptanceCriteria" TEXT,
  "plannedDate" TEXT,
  "actualDate" TEXT,
  "projectId" TEXT NOT NULL,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- DeliverableResource table
CREATE TABLE IF NOT EXISTS "DeliverableResource" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "deliverableId" TEXT NOT NULL,
  "resourcePath" TEXT NOT NULL,
  "resourceType" TEXT NOT NULL,
  "title" TEXT,
  "description" TEXT,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY ("deliverableId") REFERENCES "Deliverable" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- AreaMetric table
CREATE TABLE IF NOT EXISTS "AreaMetric" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "value" TEXT NOT NULL DEFAULT '',
  "target" TEXT,
  "unit" TEXT,
  "trackingType" TEXT NOT NULL DEFAULT 'manual',
  "direction" TEXT NOT NULL DEFAULT 'higher_better',
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "areaId" TEXT NOT NULL,
  "relatedHabits" TEXT,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- AreaMetricRecord table
CREATE TABLE IF NOT EXISTS "AreaMetricRecord" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "value" TEXT NOT NULL,
  "note" TEXT,
  "recordedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "source" TEXT,
  "confidence" REAL,
  "tags" TEXT,
  "context" TEXT,
  "metricId" TEXT NOT NULL,
  FOREIGN KEY ("metricId") REFERENCES "AreaMetric" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- Habit table
CREATE TABLE IF NOT EXISTS "Habit" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "frequency" TEXT NOT NULL,
  "target" INTEGER NOT NULL,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "areaId" TEXT NOT NULL,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- HabitRecord table
CREATE TABLE IF NOT EXISTS "HabitRecord" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "date" TEXT NOT NULL,
  "completed" INTEGER NOT NULL DEFAULT 1,
  "value" REAL,
  "note" TEXT,
  "habitId" TEXT NOT NULL,
  FOREIGN KEY ("habitId") REFERENCES "Habit" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
  UNIQUE ("habitId", "date")
);

-- Checklist table
CREATE TABLE IF NOT EXISTS "Checklist" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "template" TEXT NOT NULL,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "areaId" TEXT NOT NULL,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- ChecklistInstance table
CREATE TABLE IF NOT EXISTS "ChecklistInstance" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "status" TEXT NOT NULL,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "completedAt" TEXT,
  "checklistId" TEXT NOT NULL,
  FOREIGN KEY ("checklistId") REFERENCES "Checklist" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- Tag table
CREATE TABLE IF NOT EXISTS "Tag" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL UNIQUE,
  "color" TEXT,
  "icon" TEXT
);

-- ResourceLink table
CREATE TABLE IF NOT EXISTS "ResourceLink" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "resourcePath" TEXT NOT NULL,
  "title" TEXT,
  "projectId" TEXT,
  "areaId" TEXT,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- Task table
CREATE TABLE IF NOT EXISTS "Task" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "content" TEXT NOT NULL,
  "description" TEXT,
  "completed" INTEGER NOT NULL DEFAULT 0,
  "priority" TEXT,
  "dueDate" TEXT,
  "completedAt" TEXT,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "parentId" TEXT,
  "projectId" TEXT,
  "areaId" TEXT,
  "position" REAL NOT NULL DEFAULT 0,
  "sourceType" TEXT,
  "sourceId" TEXT,
  "sourceContext" TEXT,
  "resourceLinkId" TEXT,
  FOREIGN KEY ("resourceLinkId") REFERENCES "ResourceLink" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
  FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
  FOREIGN KEY ("parentId") REFERENCES "Task" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- TaskTag table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS "TaskTag" (
  "taskId" TEXT NOT NULL,
  "tagId" TEXT NOT NULL,
  PRIMARY KEY ("taskId", "tagId"),
  FOREIGN KEY ("tagId") REFERENCES "Tag" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY ("taskId") REFERENCES "Task" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- RecurringTask table
CREATE TABLE IF NOT EXISTS "RecurringTask" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "title" TEXT NOT NULL,
  "description" TEXT,
  "repeatRule" TEXT NOT NULL,
  "repeatInterval" INTEGER NOT NULL DEFAULT 1,
  "nextDueDate" TEXT,
  "lastCompletedAt" TEXT,
  "isActive" INTEGER NOT NULL DEFAULT 1,
  "areaId" TEXT NOT NULL,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- ReviewTemplate table
CREATE TABLE IF NOT EXISTS "ReviewTemplate" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "type" TEXT NOT NULL,
  "structure" TEXT NOT NULL,
  "isDefault" INTEGER NOT NULL DEFAULT 0,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Review table
CREATE TABLE IF NOT EXISTS "Review" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "type" TEXT NOT NULL,
  "period" TEXT NOT NULL,
  "title" TEXT,
  "content" TEXT NOT NULL,
  "status" TEXT NOT NULL DEFAULT 'draft',
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "completedAt" TEXT,
  "templateId" TEXT,
  FOREIGN KEY ("templateId") REFERENCES "ReviewTemplate" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- InboxNote table
CREATE TABLE IF NOT EXISTS "InboxNote" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "title" TEXT,
  "content" TEXT NOT NULL,
  "isDaily" INTEGER NOT NULL DEFAULT 0,
  "processed" INTEGER NOT NULL DEFAULT 0,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- DocumentLink table
CREATE TABLE IF NOT EXISTS "DocumentLink" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "sourceDocPath" TEXT NOT NULL,
  "sourceDocTitle" TEXT,
  "targetDocPath" TEXT NOT NULL,
  "targetDocTitle" TEXT,
  "linkText" TEXT,
  "contextBefore" TEXT,
  "contextAfter" TEXT,
  "lineNumber" INTEGER,
  "linkType" TEXT NOT NULL DEFAULT 'wikilink',
  "isValid" INTEGER NOT NULL DEFAULT 1,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS "idx_KPIRecord_kpiId_recordedAt" ON "KPIRecord" ("kpiId", "recordedAt");
CREATE INDEX IF NOT EXISTS "idx_AreaMetricRecord_metricId_recordedAt" ON "AreaMetricRecord" ("metricId", "recordedAt");
CREATE INDEX IF NOT EXISTS "idx_DocumentLink_sourceDocPath" ON "DocumentLink" ("sourceDocPath");
CREATE INDEX IF NOT EXISTS "idx_DocumentLink_targetDocPath" ON "DocumentLink" ("targetDocPath");
CREATE INDEX IF NOT EXISTS "idx_DocumentLink_sourceDocPath_targetDocPath" ON "DocumentLink" ("sourceDocPath", "targetDocPath");
