# 引用解析测试文档

这个文档用于测试各种引用类型的解析功能。

## WikiLink 测试
- [[测试文档1]]
- [[测试文档2|显示名称]]

## 项目引用测试
- @project:测试项目
- @project:PaoLife
- @project:知识管理系统

## 任务引用测试
- @task:测试任务
- @task:实现功能
- @task:修复Bug

## 领域标签测试
这个项目涉及 #前端开发 #后端开发 #数据库设计 等技术领域。

## 显式领域引用测试
- @area:软件工程
- @area:用户体验
- @area:系统架构

## 混合引用测试
在 @project:PaoLife 项目中，我们需要完成 @task:双向链接功能，这涉及 #前端开发 和 #数据结构 等领域。详细设计请参考 [[系统设计文档]]，技术实现方案在 @area:软件工程 中有详细说明。

## 边界情况测试
- 普通的 # 标题不应该被识别为领域引用
- 代码中的 `@project:` 不应该被识别
- 链接中的 [不是WikiLink] 不应该被识别

这是一个测试文档，包含了各种类型的引用。
