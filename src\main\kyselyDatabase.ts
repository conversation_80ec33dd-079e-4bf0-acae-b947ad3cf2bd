// Kysely Database Service
// Replaces the Prisma-based database.ts

import type { Kysely } from 'kysely'
import { app } from 'electron'
import path from 'path'
import fs from 'fs'
import type { DatabaseConfig, DatabaseResult } from '../shared/types'
import type { Database } from '../shared/database-types'
import { configManager } from './configManager'
import {
  getKyselyClient,
  createKyselyClient,
  closeKyselyClient,
  serializeJson,
  deserializeJson,
  booleanToInt,
  intToBoolean,
  getCurrentTimestamp,
  generateId,
  withTransaction
} from './kyselyClient'
import { kyselyDatabaseInitializer } from './kyselyDatabaseInitializer'
import documentLinkService from './documentLinkService'
import { seedReviewTemplates } from './seedReviewTemplates'
import ReviewDataAggregator from './reviewDataAggregator'
import ReviewAnalyzer from './reviewAnalyzer'
import { KyselyDatabaseExtensions } from './kyselyDatabaseExtensions'
import { KyselyDatabaseExtensions2 } from './kyselyDatabaseExtensions2'
import { KyselyDatabaseExtensions3 } from './kyselyDatabaseExtensions3'
import { KyselyDatabaseExtensions4 } from './kyselyDatabaseExtensions4'

class KyselyDatabaseService {
  private db: Kysely<Database> | null = null
  private config: DatabaseConfig | null = null
  private dataAggregator: ReviewDataAggregator | null = null
  private reviewAnalyzer: ReviewAnalyzer | null = null
  private extensions: KyselyDatabaseExtensions | null = null
  private extensions2: KyselyDatabaseExtensions2 | null = null
  private extensions3: KyselyDatabaseExtensions3 | null = null
  private extensions4: KyselyDatabaseExtensions4 | null = null
  private customWorkspaceDirectory: string | null = null

  /**
   * 设置自定义工作目录
   * 注意：此方法只能在数据库初始化之前调用
   */
  setWorkspaceDirectory(directory: string): void {
    if (this.db) {
      throw new Error('Cannot change workspace directory after database is initialized')
    }
    this.customWorkspaceDirectory = directory
    console.log('Custom workspace directory set:', directory)
  }

  /**
   * 从持久化存储中读取用户设置
   * 尝试从Electron的localStorage存储位置读取Zustand持久化数据
   */
  private loadUserSettingsFromStorage(): string | null {
    try {
      const userDataPath = app.getPath('userData')
      console.log('🔍 [loadUserSettingsFromStorage] Searching for user settings in:', userDataPath)

      // Electron的localStorage通常存储在这些位置
      const possiblePaths = [
        // Chrome/Electron的localStorage位置
        path.join(userDataPath, 'Local Storage', 'leveldb'),
        // 备用位置
        path.join(userDataPath, 'IndexedDB'),
        path.join(userDataPath, 'Session Storage')
      ]

      for (const storagePath of possiblePaths) {
        console.log('🔍 [loadUserSettingsFromStorage] Checking path:', storagePath)
        if (fs.existsSync(storagePath)) {
          console.log('✅ [loadUserSettingsFromStorage] Found storage directory:', storagePath)
          try {
            const files = fs.readdirSync(storagePath)

            // 查找可能包含用户设置的文件
            for (const file of files) {
              const filePath = path.join(storagePath, file)
              try {
                // 尝试读取文件内容
                const content = fs.readFileSync(filePath, 'utf8')

                // 查找user-settings相关的数据
                if (content.includes('user-settings') && content.includes('workspaceDirectory')) {
                  console.log('✅ [loadUserSettingsFromStorage] Found user-settings data in file:', file)

                  // 尝试提取workspaceDirectory - 支持Zustand persist格式
                  const patterns = [
                    // 标准JSON格式
                    /"workspaceDirectory":"([^"]+)"/,
                    /'workspaceDirectory':'([^']+)'/,
                    // URL编码格式
                    /workspaceDirectory%22%3A%22([^%]+)%22/,
                    // Zustand persist格式 - 嵌套在state.settings中
                    /"state"[^}]*"settings"[^}]*"workspaceDirectory":"([^"]+)"/,
                    // 直接在settings中
                    /"settings"[^}]*"workspaceDirectory":"([^"]+)"/,
                    // 更宽松的匹配
                    /workspaceDirectory['":\s]+([^'",\s}]+)/
                  ]

                  for (const pattern of patterns) {
                    const match = content.match(pattern)
                    if (match && match[1]) {
                      let workspaceDir = match[1]

                      // 处理URL编码
                      try {
                        workspaceDir = decodeURIComponent(workspaceDir)
                      } catch (e) {
                        // 如果解码失败，使用原始值
                      }

                      // 处理JSON转义
                      workspaceDir = workspaceDir.replace(/\\\\/g, '\\')
                      workspaceDir = workspaceDir.replace(/\\"/g, '"')

                      console.log('✅ [loadUserSettingsFromStorage] Extracted workspaceDirectory:', workspaceDir)
                      return workspaceDir
                    }
                  }

                  // 尝试直接解析JSON
                  try {
                    const jsonData = JSON.parse(content)
                    const possiblePaths = [
                      jsonData?.state?.settings?.workspaceDirectory,
                      jsonData?.settings?.workspaceDirectory,
                      jsonData?.workspaceDirectory
                    ]

                    for (const path of possiblePaths) {
                      if (path && typeof path === 'string' && path.length > 3) {
                        console.log('Found workspaceDirectory in JSON:', path)
                        return path
                      }
                    }
                  } catch (e) {
                    // 不是有效的JSON，继续使用正则表达式
                  }
                }
              } catch (e) {
                // 文件可能是二进制格式或无法读取，跳过
                continue
              }
            }
          } catch (error) {
            console.warn('Failed to read storage directory:', storagePath, error)
          }
        }
      }

      console.log('No user settings found in localStorage')
      return null
    } catch (error) {
      console.warn('Failed to load user settings from storage:', error)
      return null
    }
  }

  /**
   * 获取用户设置的工作目录
   */
  private getUserWorkspaceDirectory(): string {
    console.log('🔍 [getUserWorkspaceDirectory] Starting workspace directory resolution...')
    console.log('🔍 [getUserWorkspaceDirectory] customWorkspaceDirectory:', this.customWorkspaceDirectory)

    if (this.customWorkspaceDirectory) {
      console.log('✅ [getUserWorkspaceDirectory] Using custom workspace directory for database:', this.customWorkspaceDirectory)
      return this.customWorkspaceDirectory
    }

    // 使用配置管理器获取工作目录
    console.log('📖 [getUserWorkspaceDirectory] Attempting to load from config manager...')
    const savedWorkspaceDir = configManager.getWorkspaceDirectory()
    console.log('📖 [getUserWorkspaceDirectory] Loaded from config:', savedWorkspaceDir)

    if (savedWorkspaceDir) {
      console.log('✅ [getUserWorkspaceDirectory] Using saved workspace directory for database:', savedWorkspaceDir)
      return savedWorkspaceDir
    }

    const defaultPath = app.getPath('userData')
    console.log('⚠️ [getUserWorkspaceDirectory] Using default userData directory for database:', defaultPath)
    return defaultPath
  }

  /**
   * Initialize database service with user data directory
   */
  async initialize(): Promise<DatabaseResult<DatabaseConfig>> {
    try {
      console.log('🚀 [kyselyDatabase] Starting database initialization...')

      // Initialize Kysely database first
      console.log('📦 [kyselyDatabase] Calling kyselyDatabaseInitializer...')
      await kyselyDatabaseInitializer().initialize()

      console.log('🔗 [kyselyDatabase] Getting Kysely client...')
      this.db = getKyselyClient()
      console.log('✅ [kyselyDatabase] Kysely client obtained:', !!this.db)

      // Initialize extensions after database is ready
      console.log('🔧 [kyselyDatabase] Initializing extensions...')
      this.extensions = new KyselyDatabaseExtensions(this.db)
      this.extensions2 = new KyselyDatabaseExtensions2(this.db)
      this.extensions3 = new KyselyDatabaseExtensions3(this.db)
      this.extensions4 = new KyselyDatabaseExtensions4(this.db)
      this.dataAggregator = new ReviewDataAggregator(this as any)
      this.reviewAnalyzer = new ReviewAnalyzer(this as any)
      console.log('✅ [kyselyDatabase] Extensions initialized successfully')

      // Determine paths
      const isDevelopment = process.env.NODE_ENV === 'development' || !app.isPackaged
      console.log('🔍 [kyselyDatabase] Environment check - isDevelopment:', isDevelopment)
      let databasePath: string
      let userDataPath: string

      // 始终使用用户设置的工作目录
      console.log('📁 [kyselyDatabase] Getting user workspace directory...')
      userDataPath = this.getUserWorkspaceDirectory()
      console.log('📁 [kyselyDatabase] User workspace directory:', userDataPath)

      // 确保paolife_data目录存在
      const dataDir = path.join(userDataPath, 'paolife_data')
      console.log('📂 [kyselyDatabase] Data directory path:', dataDir)
      console.log('📂 [kyselyDatabase] Data directory exists:', fs.existsSync(dataDir))

      if (!fs.existsSync(dataDir)) {
        console.log('📂 [kyselyDatabase] Creating paolife_data directory...')
        fs.mkdirSync(dataDir, { recursive: true })
        console.log('✅ [kyselyDatabase] Created paolife_data directory:', dataDir)
      }

      if (isDevelopment) {
        // 开发环境：使用用户工作目录下的paolife_data/dev.db
        databasePath = path.join(dataDir, 'dev.db')
        console.log('🔧 [kyselyDatabase] Development mode: using user workspace directory for dev.db')
      } else {
        // 生产环境：使用用户工作目录下的paolife_data/paolife.db
        databasePath = path.join(dataDir, 'paolife.db')
        console.log('🚀 [kyselyDatabase] Production mode: using user workspace directory for paolife.db')
      }

      console.log('💾 [kyselyDatabase] Final database path:', databasePath)
      console.log('💾 [kyselyDatabase] Database file exists:', fs.existsSync(databasePath))

      // Create config
      this.config = {
        userDataPath,
        databasePath,
        isDevelopment
      }

      // Extensions already initialized above

      // Initialize document link service
      // Note: documentLinkService still uses Prisma, so we skip initialization for now
      // TODO: Migrate documentLinkService to Kysely
      console.log('⚠️ DocumentLinkService still uses Prisma, skipping initialization')

      console.log('✅ Kysely Database service initialized successfully')
      console.log(`Database path: ${databasePath}`)

      return {
        success: true,
        data: this.config
      }
    } catch (error) {
      console.error('❌ Failed to initialize Kysely database service:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to initialize database'
      }
    }
  }

  /**
   * Ensure extensions are initialized
   */
  private ensureExtensionsInitialized(): void {
    console.log('🔍 [ensureExtensionsInitialized] Checking extensions initialization...')
    console.log('🔍 [ensureExtensionsInitialized] Database exists:', !!this.db)
    console.log('🔍 [ensureExtensionsInitialized] Extensions status:', {
      extensions: !!this.extensions,
      extensions2: !!this.extensions2,
      extensions3: !!this.extensions3,
      extensions4: !!this.extensions4
    })

    if (!this.db) {
      console.error('❌ [ensureExtensionsInitialized] Database not initialized')
      throw new Error('Database not initialized')
    }

    if (!this.extensions) {
      console.log('🔧 [ensureExtensionsInitialized] Lazy initializing extensions...')
      this.extensions = new KyselyDatabaseExtensions(this.db)
    }

    if (!this.extensions2) {
      console.log('🔧 [ensureExtensionsInitialized] Lazy initializing extensions2...')
      this.extensions2 = new KyselyDatabaseExtensions2(this.db)
    }

    if (!this.extensions3) {
      console.log('🔧 [ensureExtensionsInitialized] Lazy initializing extensions3...')
      this.extensions3 = new KyselyDatabaseExtensions3(this.db)
    }

    if (!this.extensions4) {
      console.log('🔧 [ensureExtensionsInitialized] Lazy initializing extensions4...')
      this.extensions4 = new KyselyDatabaseExtensions4(this.db)
    }

    if (!this.dataAggregator) {
      console.log('🔧 [ensureExtensionsInitialized] Lazy initializing dataAggregator...')
      this.dataAggregator = new ReviewDataAggregator(this as any)
    }

    if (!this.reviewAnalyzer) {
      console.log('🔧 [ensureExtensionsInitialized] Lazy initializing reviewAnalyzer...')
      this.reviewAnalyzer = new ReviewAnalyzer(this as any)
    }

    console.log('✅ [ensureExtensionsInitialized] All extensions ensured')
  }

  /**
   * Get extensions with lazy initialization
   */
  private getExtensions(): KyselyDatabaseExtensions {
    this.ensureExtensionsInitialized()
    return this.extensions!
  }

  private getExtensions2(): KyselyDatabaseExtensions2 {
    this.ensureExtensionsInitialized()
    return this.extensions2!
  }

  private getExtensions3(): KyselyDatabaseExtensions3 {
    this.ensureExtensionsInitialized()
    return this.extensions3!
  }

  private getExtensions4(): KyselyDatabaseExtensions4 {
    this.ensureExtensionsInitialized()
    return this.extensions4!
  }

  /**
   * Close database connection
   */
  async close(): Promise<void> {
    await closeKyselyClient()
    this.db = null
    this.extensions = null
    this.extensions2 = null
    this.extensions3 = null
    this.extensions4 = null
    this.dataAggregator = null
    this.reviewAnalyzer = null
    console.log('Database connection closed')
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<DatabaseResult<boolean>> {
    try {
      if (!this.db) {
        throw new Error('Database not initialized')
      }

      // Test with a simple query
      await this.db.selectFrom('User').select('id').limit(1).execute()
      
      return {
        success: true,
        data: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Connection test failed'
      }
    }
  }

  /**
   * Get database schema information (for debugging)
   */
  async getDatabaseInfo(): Promise<DatabaseResult<any>> {
    try {
      if (!this.db) {
        throw new Error('Database not initialized')
      }

      const status = await kyselyDatabaseInitializer().getStatus()
      
      return {
        success: true,
        data: {
          type: 'SQLite with Kysely',
          path: this.config?.databasePath,
          initialized: status.initialized,
          tableCount: status.tableCount,
          recordCounts: status.recordCounts,
          integrity: status.integrity
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get database info'
      }
    }
  }

  // Helper method to ensure database is initialized
  private ensureInitialized(): Kysely<Database> {
    if (!this.db) {
      throw new Error('Database not initialized')
    }
    return this.db
  }

  // Helper method to handle database errors
  private handleError(error: unknown, operation: string): DatabaseResult<never> {
    console.error(`Database operation failed: ${operation}`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : `Failed to ${operation}`
    }
  }

  /**
   * PROJECT OPERATIONS
   */

  async createProject(data: {
    name: string
    description?: string
    status?: string
    areaId?: string
    goal?: string
    deliverable?: string
    startDate?: string
    deadline?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const db = this.ensureInitialized()
      
      const project = await db.insertInto('Project')
        .values({
          id: generateId(),
          name: data.name,
          description: data.description || null,
          status: data.status || 'Not Started',
          progress: 0,
          goal: data.goal || null,
          deliverable: data.deliverable || null,
          startDate: data.startDate || null,
          deadline: data.deadline || null,
          areaId: data.areaId || null,
          archived: booleanToInt(false),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: project
      }
    } catch (error) {
      return this.handleError(error, 'create project')
    }
  }

  async getProjects(): Promise<DatabaseResult<any[]>> {
    try {
      const db = this.ensureInitialized()
      
      const projects = await db.selectFrom('Project')
        .selectAll()
        .where('archived', '=', booleanToInt(false))
        .orderBy('updatedAt', 'desc')
        .execute()

      return {
        success: true,
        data: projects.map(project => ({
          ...project,
          archived: intToBoolean(project.archived)
        }))
      }
    } catch (error) {
      return this.handleError(error, 'get projects')
    }
  }

  async getProjectById(id: string, includeArchived: boolean = false): Promise<DatabaseResult<any>> {
    try {
      const db = this.ensureInitialized()
      
      let query = db.selectFrom('Project')
        .selectAll()
        .where('id', '=', id)

      if (!includeArchived) {
        query = query.where('archived', '=', booleanToInt(false))
      }

      const project = await query.executeTakeFirst()

      if (!project) {
        throw new Error('Project not found')
      }

      return {
        success: true,
        data: {
          ...project,
          archived: intToBoolean(project.archived)
        }
      }
    } catch (error) {
      return this.handleError(error, 'get project by id')
    }
  }

  async updateProject(id: string, data: {
    name?: string
    description?: string
    status?: string
    progress?: number
    goal?: string
    deliverable?: string
    startDate?: string | Date
    deadline?: string | Date
    areaId?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const db = this.ensureInitialized()

      const updateData: any = {
        updatedAt: getCurrentTimestamp()
      }

      // Only include fields that are provided and convert Date objects to strings
      if (data.name !== undefined) updateData.name = data.name
      if (data.description !== undefined) updateData.description = data.description
      if (data.status !== undefined) updateData.status = data.status
      if (data.progress !== undefined) updateData.progress = data.progress
      if (data.goal !== undefined) updateData.goal = data.goal
      if (data.deliverable !== undefined) updateData.deliverable = data.deliverable
      if (data.startDate !== undefined) {
        updateData.startDate = data.startDate instanceof Date ? data.startDate.toISOString() : data.startDate
      }
      if (data.deadline !== undefined) {
        updateData.deadline = data.deadline instanceof Date ? data.deadline.toISOString() : data.deadline
      }
      if (data.areaId !== undefined) updateData.areaId = data.areaId

      const project = await db.updateTable('Project')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...project,
          archived: intToBoolean(project.archived)
        }
      }
    } catch (error) {
      return this.handleError(error, 'update project')
    }
  }

  async deleteProject(id: string): Promise<DatabaseResult<void>> {
    try {
      const db = this.ensureInitialized()
      
      await db.deleteFrom('Project')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return this.handleError(error, 'delete project')
    }
  }

  async getArchivedProjects(): Promise<DatabaseResult<any[]>> {
    try {
      const db = this.ensureInitialized()
      
      const projects = await db.selectFrom('Project')
        .selectAll()
        .where('archived', '=', booleanToInt(true))
        .orderBy('updatedAt', 'desc')
        .execute()

      return {
        success: true,
        data: projects.map(project => ({
          ...project,
          archived: intToBoolean(project.archived)
        }))
      }
    } catch (error) {
      return this.handleError(error, 'get archived projects')
    }
  }

  async archiveProject(id: string): Promise<DatabaseResult<void>> {
    try {
      const db = this.ensureInitialized()
      
      await db.updateTable('Project')
        .set({ 
          archived: booleanToInt(true),
          updatedAt: getCurrentTimestamp()
        })
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return this.handleError(error, 'archive project')
    }
  }

  async restoreProject(id: string): Promise<DatabaseResult<void>> {
    try {
      const db = this.ensureInitialized()
      
      await db.updateTable('Project')
        .set({ 
          archived: booleanToInt(false),
          updatedAt: getCurrentTimestamp()
        })
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return this.handleError(error, 'restore project')
    }
  }

  /**
   * AREA OPERATIONS
   */

  async createArea(data: {
    name: string
    description?: string
    standard?: string
    icon?: string
    color?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const db = this.ensureInitialized()

      const area = await db.insertInto('Area')
        .values({
          id: generateId(),
          name: data.name,
          description: data.description || null,
          standard: data.standard || null,
          icon: data.icon || null,
          color: data.color || null,
          archived: booleanToInt(false),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...area,
          archived: intToBoolean(area.archived)
        }
      }
    } catch (error) {
      return this.handleError(error, 'create area')
    }
  }

  async getAreas(): Promise<DatabaseResult<any[]>> {
    try {
      const db = this.ensureInitialized()

      const areas = await db.selectFrom('Area')
        .selectAll()
        .where('archived', '=', booleanToInt(false))
        .orderBy('updatedAt', 'desc')
        .execute()

      return {
        success: true,
        data: areas.map(area => ({
          ...area,
          archived: intToBoolean(area.archived)
        }))
      }
    } catch (error) {
      return this.handleError(error, 'get areas')
    }
  }

  async getAreaById(id: string, includeArchived: boolean = false): Promise<DatabaseResult<any>> {
    try {
      const db = this.ensureInitialized()

      let query = db.selectFrom('Area')
        .selectAll()
        .where('id', '=', id)

      if (!includeArchived) {
        query = query.where('archived', '=', booleanToInt(false))
      }

      const area = await query.executeTakeFirst()

      if (!area) {
        throw new Error('Area not found')
      }

      // Get related data
      const [projects, habits, metrics] = await Promise.all([
        db.selectFrom('Project')
          .selectAll()
          .where('areaId', '=', id)
          .where('archived', '=', booleanToInt(false))
          .execute(),
        db.selectFrom('Habit')
          .selectAll()
          .where('areaId', '=', id)
          .execute(),
        db.selectFrom('AreaMetric')
          .selectAll()
          .where('areaId', '=', id)
          .execute()
      ])

      return {
        success: true,
        data: {
          ...area,
          archived: intToBoolean(area.archived),
          projects: projects.map(p => ({ ...p, archived: intToBoolean(p.archived) })),
          habits,
          metrics
        }
      }
    } catch (error) {
      return this.handleError(error, 'get area by id')
    }
  }

  async updateArea(id: string, data: {
    name?: string
    description?: string
    standard?: string
    icon?: string
    color?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const db = this.ensureInitialized()

      const updateData: any = {
        updatedAt: getCurrentTimestamp()
      }

      if (data.name !== undefined) updateData.name = data.name
      if (data.description !== undefined) updateData.description = data.description
      if (data.standard !== undefined) updateData.standard = data.standard
      if (data.icon !== undefined) updateData.icon = data.icon
      if (data.color !== undefined) updateData.color = data.color

      const area = await db.updateTable('Area')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...area,
          archived: intToBoolean(area.archived)
        }
      }
    } catch (error) {
      return this.handleError(error, 'update area')
    }
  }

  async deleteArea(id: string): Promise<DatabaseResult<void>> {
    try {
      const db = this.ensureInitialized()

      await db.deleteFrom('Area')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return this.handleError(error, 'delete area')
    }
  }

  async getArchivedAreas(): Promise<DatabaseResult<any[]>> {
    try {
      const db = this.ensureInitialized()

      const areas = await db.selectFrom('Area')
        .selectAll()
        .where('archived', '=', booleanToInt(true))
        .orderBy('updatedAt', 'desc')
        .execute()

      return {
        success: true,
        data: areas.map(area => ({
          ...area,
          archived: intToBoolean(area.archived)
        }))
      }
    } catch (error) {
      return this.handleError(error, 'get archived areas')
    }
  }

  async archiveArea(id: string): Promise<DatabaseResult<void>> {
    try {
      const db = this.ensureInitialized()

      await db.updateTable('Area')
        .set({
          archived: booleanToInt(true),
          updatedAt: getCurrentTimestamp()
        })
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return this.handleError(error, 'archive area')
    }
  }

  async restoreArea(id: string): Promise<DatabaseResult<void>> {
    try {
      const db = this.ensureInitialized()

      await db.updateTable('Area')
        .set({
          archived: booleanToInt(false),
          updatedAt: getCurrentTimestamp()
        })
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return this.handleError(error, 'restore area')
    }
  }

  /**
   * PROJECT KPI OPERATIONS (delegated to extensions)
   */

  async createProjectKPI(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.createProjectKPI(data)
  }

  async getProjectKPIs(projectId: string): Promise<DatabaseResult<any[]>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.getProjectKPIs(projectId)
  }

  async updateProjectKPI(id: string, data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.updateProjectKPI(id, data)
  }

  async deleteProjectKPI(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.deleteProjectKPI(id)
  }

  /**
   * KPI RECORD OPERATIONS (delegated to extensions)
   */

  async createKPIRecord(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.createKPIRecord(data)
  }

  async getKPIRecords(kpiId: string, limit?: number): Promise<DatabaseResult<any[]>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.getKPIRecords(kpiId, limit)
  }

  async updateKPIRecord(id: string, data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.updateKPIRecord(id, data)
  }

  async deleteKPIRecord(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.deleteKPIRecord(id)
  }

  /**
   * AREA METRIC OPERATIONS (delegated to extensions)
   */

  async createAreaMetric(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.createAreaMetric(data)
  }

  async getAreaMetrics(areaId: string): Promise<DatabaseResult<any[]>> {
    return this.getExtensions().getAreaMetrics(areaId)
  }

  async updateAreaMetric(id: string, data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.updateAreaMetric(id, data)
  }

  async deleteAreaMetric(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.deleteAreaMetric(id)
  }

  /**
   * AREA METRIC RECORD OPERATIONS (delegated to extensions)
   */

  async createAreaMetricRecord(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.createAreaMetricRecord(data)
  }

  async getAreaMetricRecords(metricId: string, limit?: number): Promise<DatabaseResult<any[]>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.getAreaMetricRecords(metricId, limit)
  }

  async updateAreaMetricRecord(id: string, data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.updateAreaMetricRecord(id, data)
  }

  async deleteAreaMetricRecord(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.deleteAreaMetricRecord(id)
  }

  /**
   * HABIT OPERATIONS (delegated to extensions)
   */

  async createHabit(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.createHabit(data)
  }

  async getHabitsByArea(areaId: string): Promise<DatabaseResult<any[]>> {
    return this.getExtensions().getHabitsByArea(areaId)
  }

  async updateHabit(id: string, data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.updateHabit(id, data)
  }

  async deleteHabit(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions) throw new Error('Extensions not initialized')
    return this.extensions.deleteHabit(id)
  }

  /**
   * HABIT RECORD OPERATIONS (delegated to extensions2)
   */

  async createHabitRecord(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions2) throw new Error('Extensions2 not initialized')
    return this.extensions2.createHabitRecord(data)
  }

  async updateHabitRecord(id: string, data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions2) throw new Error('Extensions2 not initialized')
    return this.extensions2.updateHabitRecord(id, data)
  }

  async getHabitRecords(habitId: string): Promise<DatabaseResult<any[]>> {
    if (!this.extensions2) throw new Error('Extensions2 not initialized')
    return this.extensions2.getHabitRecords(habitId)
  }

  async deleteHabitRecord(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions2) throw new Error('Extensions2 not initialized')
    return this.extensions2.deleteHabitRecord(id)
  }

  /**
   * RECURRING TASK OPERATIONS (delegated to extensions2)
   */

  async createRecurringTask(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions2) throw new Error('Extensions2 not initialized')
    return this.extensions2.createRecurringTask(data)
  }

  async getRecurringTasks(areaId: string): Promise<DatabaseResult<any[]>> {
    return this.getExtensions2().getRecurringTasks(areaId)
  }

  async updateRecurringTask(id: string, data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions2) throw new Error('Extensions2 not initialized')
    return this.extensions2.updateRecurringTask(id, data)
  }

  async deleteRecurringTask(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions2) throw new Error('Extensions2 not initialized')
    return this.extensions2.deleteRecurringTask(id)
  }

  /**
   * DELIVERABLE OPERATIONS (delegated to extensions2)
   */

  async createDeliverable(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions2) throw new Error('Extensions2 not initialized')
    return this.extensions2.createDeliverable(data)
  }

  async getProjectDeliverables(projectId: string): Promise<DatabaseResult<any[]>> {
    if (!this.extensions2) throw new Error('Extensions2 not initialized')
    return this.extensions2.getProjectDeliverables(projectId)
  }

  async updateDeliverable(id: string, data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions2) throw new Error('Extensions2 not initialized')
    return this.extensions2.updateDeliverable(id, data)
  }

  async deleteDeliverable(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions2) throw new Error('Extensions2 not initialized')
    return this.extensions2.deleteDeliverable(id)
  }

  // TODO: Add remaining operations: Task, Review, Checklist, ResourceLink, etc.

  /**
   * COMPATIBILITY METHODS FOR GRADUAL MIGRATION
   * These methods provide compatibility with the existing IPC layer
   */

  // Getter for backward compatibility
  get db() {
    return this.ensureInitialized()
  }

  get config() {
    return this.config
  }

  // Prisma-style client getter for compatibility
  getClient() {
    return {
      // Provide a minimal compatibility layer
      // This will be removed once all IPC methods are updated
      $disconnect: () => this.close()
    }
  }

  // Document link service getter
  getDocumentLinkService() {
    return documentLinkService
  }

  // Review operations (delegated to extensions3)
  async createReview(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.createReview(data)
  }

  async getReviews(filters: any): Promise<DatabaseResult<any[]>> {
    return this.getExtensions3().getReviews(filters)
  }

  async getReviewById(id: string): Promise<DatabaseResult<any>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.getReviewById(id)
  }

  async updateReview(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.updateReview(data.id, data)
  }

  async deleteReview(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.deleteReview(id)
  }

  async createReviewTemplate(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.createReviewTemplate(data)
  }

  async getReviewTemplates(type?: string): Promise<DatabaseResult<any[]>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.getReviewTemplates(type)
  }

  async getReviewTemplateById(id: string): Promise<DatabaseResult<any>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.getReviewTemplateById(id)
  }

  async updateReviewTemplate(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.updateReviewTemplate(data.id, data)
  }

  async deleteReviewTemplate(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.deleteReviewTemplate(id)
  }

  async getReviewAggregatedData(type: string, period: string): Promise<DatabaseResult<any>> {
    return { success: false, error: 'Not yet implemented in Kysely version' }
  }

  async getReviewAnalysis(type: string, period: string): Promise<DatabaseResult<any>> {
    return { success: false, error: 'Not yet implemented in Kysely version' }
  }

  // Resource operations (delegated to extensions3)
  async getProjectResources(projectId: string): Promise<DatabaseResult<any[]>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.getProjectResources(projectId)
  }

  async linkResourceToProject(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.linkResourceToProject(data)
  }

  async unlinkResourceFromProject(resourceId: string, projectId: string): Promise<DatabaseResult<void>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.unlinkResourceFromProject(resourceId, projectId)
  }

  async getAreaResources(areaId: string): Promise<DatabaseResult<any[]>> {
    return this.getExtensions3().getAreaResources(areaId)
  }

  async createResource(data: {
    resourcePath: string
    title?: string
    projectId?: string
    areaId?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const db = this.ensureInitialized()

      // Check if resource link already exists
      const existingLink = await db.selectFrom('ResourceLink')
        .selectAll()
        .where('resourcePath', '=', data.resourcePath)
        .where((eb) => {
          if (data.projectId) {
            return eb('projectId', '=', data.projectId)
          } else if (data.areaId) {
            return eb('areaId', '=', data.areaId)
          }
          return eb('projectId', 'is', null).and('areaId', 'is', null)
        })
        .executeTakeFirst()

      if (existingLink) {
        return {
          success: true,
          data: existingLink
        }
      }

      // Create new resource link
      const resourceLink = await db.insertInto('ResourceLink')
        .values({
          id: generateId(),
          resourcePath: data.resourcePath,
          title: data.title || null,
          projectId: data.projectId || null,
          areaId: data.areaId || null
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: resourceLink
      }
    } catch (error) {
      return this.handleError(error, 'create resource')
    }
  }

  async getResources(filters?: any): Promise<DatabaseResult<any[]>> {
    try {
      const db = this.ensureInitialized()

      let query = db.selectFrom('ResourceLink').selectAll()

      if (filters?.projectId) {
        query = query.where('projectId', '=', filters.projectId)
      }

      if (filters?.areaId) {
        query = query.where('areaId', '=', filters.areaId)
      }

      const resources = await query.execute()

      return {
        success: true,
        data: resources
      }
    } catch (error) {
      return this.handleError(error, 'get resources')
    }
  }

  async getResourceReferences(resourcePath: string): Promise<DatabaseResult<any>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.getResourceReferences(resourcePath)
  }

  async unlinkResourceFromArea(resourceId: string, areaId: string): Promise<DatabaseResult<void>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.unlinkResourceFromArea(resourceId, areaId)
  }

  // Checklist operations (delegated to extensions3)
  async createChecklist(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.createChecklist(data)
  }

  async getChecklists(areaId?: string): Promise<DatabaseResult<any[]>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.getChecklists(areaId)
  }

  async updateChecklist(id: string, data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.updateChecklist(id, data)
  }

  async deleteChecklist(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.deleteChecklist(id)
  }

  async createChecklistInstance(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.createChecklistInstance(data)
  }

  async getChecklistInstances(areaId?: string): Promise<DatabaseResult<any[]>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.getChecklistInstances(areaId)
  }

  async updateChecklistInstance(id: string, data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.updateChecklistInstance(id, data)
  }

  async deleteChecklistInstance(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions3) throw new Error('Extensions3 not initialized')
    return this.extensions3.deleteChecklistInstance(id)
  }

  /**
   * TASK OPERATIONS (delegated to extensions4)
   */

  async createTask(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions4) throw new Error('Extensions4 not initialized')
    return this.extensions4.createTask(data)
  }

  async getTasks(filters: any = {}): Promise<DatabaseResult<any[]>> {
    if (!this.extensions4) throw new Error('Extensions4 not initialized')
    return this.extensions4.getTasks(filters)
  }

  async getTaskById(id: string): Promise<DatabaseResult<any>> {
    if (!this.extensions4) throw new Error('Extensions4 not initialized')
    return this.extensions4.getTaskById(id)
  }

  async updateTask(id: string, data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions4) throw new Error('Extensions4 not initialized')
    return this.extensions4.updateTask(id, data)
  }

  async deleteTask(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions4) throw new Error('Extensions4 not initialized')
    return this.extensions4.deleteTask(id)
  }

  /**
   * TASK TAG OPERATIONS (delegated to extensions4)
   */

  async createTaskTag(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions4) throw new Error('Extensions4 not initialized')
    return this.extensions4.createTaskTag(data)
  }

  async getTaskTags(): Promise<DatabaseResult<any[]>> {
    if (!this.extensions4) throw new Error('Extensions4 not initialized')
    return this.extensions4.getTaskTags()
  }

  async updateTaskTag(id: string, data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions4) throw new Error('Extensions4 not initialized')
    return this.extensions4.updateTaskTag(id, data)
  }

  async deleteTaskTag(id: string): Promise<DatabaseResult<void>> {
    if (!this.extensions4) throw new Error('Extensions4 not initialized')
    return this.extensions4.deleteTaskTag(id)
  }

  /**
   * USER SETTINGS OPERATIONS (delegated to extensions4)
   */

  async getUserSettings(): Promise<DatabaseResult<any>> {
    if (!this.extensions4) throw new Error('Extensions4 not initialized')
    return this.extensions4.getUserSettings()
  }

  async updateUserSettings(data: any): Promise<DatabaseResult<any>> {
    if (!this.extensions4) throw new Error('Extensions4 not initialized')
    return this.extensions4.updateUserSettings(data)
  }
}

// Export singleton instance
export const kyselyDatabaseService = new KyselyDatabaseService()
export default kyselyDatabaseService
