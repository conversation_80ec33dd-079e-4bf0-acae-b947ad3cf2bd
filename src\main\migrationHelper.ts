/**
 * 数据库迁移助手
 * 检查和执行必要的数据库迁移
 */

import type { PrismaClient } from '@prisma/client'
import fs from 'fs'
import path from 'path'

export class MigrationHelper {
  private prisma: PrismaClient

  constructor(prisma: PrismaClient) {
    this.prisma = prisma
  }

  /**
   * 检查是否需要执行领域指标增强迁移
   */
  async needsAreaMetricEnhancement(): Promise<boolean> {
    try {
      // 检查多个关键字段，确保所有字段都存在
      await this.prisma.$queryRaw`SELECT trackingType, direction FROM AreaMetric LIMIT 1`
      return false // 字段存在，不需要迁移
    } catch (error) {
      return true // 字段不存在，需要迁移
    }
  }

  /**
   * 执行领域指标增强迁移
   */
  async executeAreaMetricEnhancement(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('开始执行领域指标增强迁移...')

      // 为AreaMetric表添加新字段，逐个检查避免重复添加
      const fieldsToAdd = [
        { name: 'trackingType', sql: `ALTER TABLE "AreaMetric" ADD COLUMN "trackingType" TEXT NOT NULL DEFAULT 'metric'` },
        { name: 'habitConfig', sql: `ALTER TABLE "AreaMetric" ADD COLUMN "habitConfig" TEXT` },
        { name: 'standardConfig', sql: `ALTER TABLE "AreaMetric" ADD COLUMN "standardConfig" TEXT` },
        { name: 'isActive', sql: `ALTER TABLE "AreaMetric" ADD COLUMN "isActive" BOOLEAN NOT NULL DEFAULT true` },
        { name: 'priority', sql: `ALTER TABLE "AreaMetric" ADD COLUMN "priority" TEXT` },
        { name: 'category', sql: `ALTER TABLE "AreaMetric" ADD COLUMN "category" TEXT` },
        { name: 'description', sql: `ALTER TABLE "AreaMetric" ADD COLUMN "description" TEXT` },
        { name: 'direction', sql: `ALTER TABLE "AreaMetric" ADD COLUMN "direction" TEXT NOT NULL DEFAULT 'increase'` }
      ]

      for (const field of fieldsToAdd) {
        try {
          await this.prisma.$queryRaw`SELECT ${field.name} FROM AreaMetric LIMIT 1`
          console.log(`${field.name} 字段已存在，跳过`)
        } catch (error) {
          console.log(`添加 ${field.name} 字段...`)
          await this.prisma.$executeRaw(field.sql as any)
        }
      }

      // 为AreaMetricRecord表添加新字段，逐个检查避免重复添加
      const recordFieldsToAdd = [
        { name: 'mood', sql: `ALTER TABLE "AreaMetricRecord" ADD COLUMN "mood" TEXT` },
        { name: 'energy', sql: `ALTER TABLE "AreaMetricRecord" ADD COLUMN "energy" TEXT` },
        { name: 'context', sql: `ALTER TABLE "AreaMetricRecord" ADD COLUMN "context" TEXT` },
        { name: 'tags', sql: `ALTER TABLE "AreaMetricRecord" ADD COLUMN "tags" TEXT` },
        { name: 'quality', sql: `ALTER TABLE "AreaMetricRecord" ADD COLUMN "quality" TEXT` },
        { name: 'duration', sql: `ALTER TABLE "AreaMetricRecord" ADD COLUMN "duration" INTEGER` },
        { name: 'difficulty', sql: `ALTER TABLE "AreaMetricRecord" ADD COLUMN "difficulty" TEXT` }
      ]

      for (const field of recordFieldsToAdd) {
        try {
          await this.prisma.$queryRaw`SELECT ${field.name} FROM AreaMetricRecord LIMIT 1`
          console.log(`AreaMetricRecord.${field.name} 字段已存在，跳过`)
        } catch (error) {
          console.log(`添加 AreaMetricRecord.${field.name} 字段...`)
          await this.prisma.$executeRaw(field.sql as any)
        }
      }

      // 更新现有数据：将包含习惯关键词的指标标记为habit类型
      await this.prisma.$executeRaw`
        UPDATE "AreaMetric"
        SET "trackingType" = 'habit'
        WHERE LOWER("name") LIKE '%daily%'
           OR LOWER("name") LIKE '%每日%'
           OR LOWER("name") LIKE '%habit%'
           OR LOWER("name") LIKE '%习惯%'
           OR LOWER("name") LIKE '%routine%'
           OR LOWER("name") LIKE '%例行%'
           OR LOWER("frequency") LIKE '%daily%'
           OR LOWER("frequency") LIKE '%每日%'
      `

      // 为习惯类型的指标设置默认配置
      await this.prisma.$executeRaw`
        UPDATE "AreaMetric"
        SET "habitConfig" = '{"targetFrequency": 7, "weeklyTarget": 5, "reminderTime": "09:00", "streakGoal": 30}'
        WHERE "trackingType" = 'habit'
      `

      // 创建索引以提高查询性能
      try {
        await this.prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_area_metric_tracking_type" ON "AreaMetric"("trackingType")`
        await this.prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_area_metric_category" ON "AreaMetric"("category")`
        await this.prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_area_metric_priority" ON "AreaMetric"("priority")`
        await this.prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_area_metric_active" ON "AreaMetric"("isActive")`
        await this.prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_area_metric_record_recorded_at" ON "AreaMetricRecord"("recordedAt")`
      } catch (indexError) {
        console.warn('索引创建失败，但不影响功能:', indexError)
      }

      console.log('领域指标增强迁移完成')
      return { success: true }
    } catch (error) {
      console.error('迁移执行失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown migration error'
      }
    }
  }

  /**
   * 检查是否需要执行ProjectKPI direction字段迁移
   */
  async needsProjectKPIDirectionMigration(): Promise<boolean> {
    try {
      // 尝试查询direction字段，如果失败说明需要迁移
      await this.prisma.$queryRaw`SELECT direction FROM ProjectKPI LIMIT 1`
      return false // 字段存在，不需要迁移
    } catch (error) {
      return true // 字段不存在，需要迁移
    }
  }

  /**
   * 执行ProjectKPI字段迁移（包括frequency和direction字段）
   */
  async executeProjectKPIDirectionMigration(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('开始执行ProjectKPI字段迁移...')

      // 检查并添加frequency字段
      try {
        await this.prisma.$queryRaw`SELECT frequency FROM ProjectKPI LIMIT 1`
        console.log('frequency字段已存在')
      } catch (error) {
        console.log('添加frequency字段...')
        await this.prisma.$executeRaw`ALTER TABLE "ProjectKPI" ADD COLUMN "frequency" TEXT`
      }

      // 检查并添加direction字段
      try {
        await this.prisma.$queryRaw`SELECT direction FROM ProjectKPI LIMIT 1`
        console.log('direction字段已存在')
      } catch (error) {
        console.log('添加direction字段...')
        await this.prisma.$executeRaw`ALTER TABLE "ProjectKPI" ADD COLUMN "direction" TEXT NOT NULL DEFAULT 'increase'`
      }

      console.log('ProjectKPI字段迁移完成')
      return { success: true }
    } catch (error) {
      console.error('ProjectKPI字段迁移失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown migration error'
      }
    }
  }
  /**
   * 检查并修复 ResourceLink 表缺少 resourcePath 列的问题
   */
  private async ensureResourceLinkResourcePath(): Promise<void> {
    // 检查列是否存在
    try {
      await this.prisma.$queryRaw`SELECT resourcePath FROM ResourceLink LIMIT 1`
      return // 列已存在
    } catch {}

    // 列不存在，尝试添加
    try {
      await this.prisma.$executeRaw`ALTER TABLE "ResourceLink" ADD COLUMN "resourcePath" TEXT`
    } catch (e) {
      console.warn('添加 ResourceLink.resourcePath 列失败:', e)
    }

    // 尝试从旧列 url 迁移数据（如果存在）
    try {
      await this.prisma.$queryRaw`SELECT url FROM ResourceLink LIMIT 1`
      await this.prisma.$executeRaw`UPDATE "ResourceLink" SET "resourcePath" = COALESCE("resourcePath", "url")`
    } catch {}

    // 设置非空约束无法直接通过 ALTER TABLE 添加，这里先保证数据存在
    // 后续如需严格约束可通过重建表实现
  }
  /**
   * 确保 Project.progress 列存在
   */
  private async ensureProjectProgress(): Promise<void> {
    try {
      await this.prisma.$queryRaw`SELECT progress FROM Project LIMIT 1`
    } catch {
      await this.prisma.$executeRaw`ALTER TABLE "Project" ADD COLUMN "progress" INTEGER NOT NULL DEFAULT 0`
    }
  }

  /** 确保 Area.standard 列存在 */
  private async ensureAreaStandard(): Promise<void> {
    try {
      await this.prisma.$queryRaw`SELECT standard FROM Area LIMIT 1`
    } catch {
      await this.prisma.$executeRaw`ALTER TABLE "Area" ADD COLUMN "standard" TEXT`
    }
  }

  /** 确保 Habit.target 列存在 */
  private async ensureHabitTarget(): Promise<void> {
    try {
      await this.prisma.$queryRaw`SELECT target FROM Habit LIMIT 1`
    } catch {
      await this.prisma.$executeRaw`ALTER TABLE "Habit" ADD COLUMN "target" INTEGER`
    }
  }

  /** 确保 AreaMetric.value 列存在 */
  private async ensureAreaMetricValue(): Promise<void> {
    try {
      await this.prisma.$queryRaw`SELECT value FROM AreaMetric LIMIT 1`
    } catch {
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" ADD COLUMN "value" TEXT NOT NULL DEFAULT ''`
    }
  }

  /** 确保 RecurringTask.repeatRule 列存在 */
  private async ensureRecurringTaskRepeatRule(): Promise<void> {
    let added = false
    try {
      await this.prisma.$queryRaw`SELECT repeatRule FROM RecurringTask LIMIT 1`
    } catch {
      await this.prisma.$executeRaw`ALTER TABLE "RecurringTask" ADD COLUMN "repeatRule" TEXT`
      added = true
    }
    if (added) {
      try {
        await this.prisma.$executeRaw`UPDATE "RecurringTask" SET "repeatRule" = COALESCE("repeatRule", 'weekly')`
      } catch {}
    }
  }

  /** 确保 Project.goal 列存在 */
  private async ensureProjectGoal(): Promise<void> {
    try {
      await this.prisma.$queryRaw`SELECT goal FROM Project LIMIT 1`
    } catch {
      await this.prisma.$executeRaw`ALTER TABLE "Project" ADD COLUMN "goal" TEXT`
    }
  }

  /** 确保 AreaMetric.target 列存在 */
  private async ensureAreaMetricTarget(): Promise<void> {
    try {
      await this.prisma.$queryRaw`SELECT target FROM AreaMetric LIMIT 1`
    } catch {
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" ADD COLUMN "target" TEXT`
    }
  }

  /** 确保 RecurringTask.repeatInterval 列存在 */
  private async ensureRecurringTaskRepeatInterval(): Promise<void> {
    try {
      await this.prisma.$queryRaw`SELECT repeatInterval FROM RecurringTask LIMIT 1`
    } catch {
      await this.prisma.$executeRaw`ALTER TABLE "RecurringTask" ADD COLUMN "repeatInterval" INTEGER`
    }
  }

  /** 确保 ReviewTemplate 表存在并包含所有必需字段 */
  private async ensureReviewTemplateTable(): Promise<void> {
    try {
      await this.prisma.$queryRaw`SELECT id FROM ReviewTemplate LIMIT 1`
      // 表存在，检查并添加缺失字段
      await this.ensureReviewTemplateFields()
    } catch {
      // 表不存在，创建完整表
      await this.prisma.$executeRaw`CREATE TABLE IF NOT EXISTS "ReviewTemplate" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "description" TEXT,
        "type" TEXT NOT NULL,
        "structure" TEXT NOT NULL,
        "isDefault" BOOLEAN NOT NULL DEFAULT false,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
      )`
    }
  }

  /** 确保 ReviewTemplate 表包含所有必需字段 */
  private async ensureReviewTemplateFields(): Promise<void> {
    const fieldsToCheck = [
      { name: 'description', type: 'TEXT' },
      { name: 'structure', type: 'TEXT NOT NULL DEFAULT \'[]\'', oldName: 'questions' }
    ]

    for (const field of fieldsToCheck) {
      try {
        await this.prisma.$queryRaw`SELECT ${field.name} FROM ReviewTemplate LIMIT 1`
      } catch {
        if (field.oldName) {
          // 尝试重命名旧字段
          try {
            await this.prisma.$executeRaw`ALTER TABLE "ReviewTemplate" RENAME COLUMN "${field.oldName}" TO "${field.name}"`
          } catch {
            // 如果重命名失败，添加新字段
            await this.prisma.$executeRaw`ALTER TABLE "ReviewTemplate" ADD COLUMN "${field.name}" ${field.type}`
          }
        } else {
          await this.prisma.$executeRaw`ALTER TABLE "ReviewTemplate" ADD COLUMN "${field.name}" ${field.type}`
        }
      }
    }
  }

  /** 确保 AreaMetric 表存在并包含所有必需字段 */
  private async ensureAreaMetricTable(): Promise<void> {
    try {
      await this.prisma.$queryRaw`SELECT id FROM AreaMetric LIMIT 1`
      // 表存在，检查并添加缺失字段
      await this.ensureAreaMetricFields()
    } catch {
      // 表不存在，创建完整表
      await this.prisma.$executeRaw`CREATE TABLE IF NOT EXISTS "AreaMetric" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "value" TEXT NOT NULL DEFAULT '',
        "target" TEXT,
        "unit" TEXT,
        "trackingType" TEXT NOT NULL DEFAULT 'manual',
        "direction" TEXT NOT NULL DEFAULT 'higher_better',
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "areaId" TEXT NOT NULL,
        "relatedHabits" TEXT,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
      )`
    }
  }

  /** 确保 AreaMetric 表包含所有必需字段 */
  private async ensureAreaMetricFields(): Promise<void> {
    const fieldsToCheck = [
      { name: 'trackingType', type: 'TEXT NOT NULL DEFAULT \'manual\'' },
      { name: 'direction', type: 'TEXT NOT NULL DEFAULT \'higher_better\'' },
      { name: 'relatedHabits', type: 'TEXT' }
    ]

    for (const field of fieldsToCheck) {
      try {
        await this.prisma.$queryRaw`SELECT ${field.name} FROM AreaMetric LIMIT 1`
      } catch {
        await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" ADD COLUMN "${field.name}" ${field.type}`
      }
    }

    // 移除 description 字段（如果存在）
    try {
      await this.prisma.$queryRaw`SELECT description FROM AreaMetric LIMIT 1`
      // 如果字段存在，尝试删除
      await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" DROP COLUMN "description"`
    } catch {
      // 字段不存在，忽略错误
    }
  }

  /** 确保 RecurringTask 表存在并包含所有必需字段 */
  private async ensureRecurringTaskTable(): Promise<void> {
    try {
      await this.prisma.$queryRaw`SELECT id FROM RecurringTask LIMIT 1`
      // 表存在，检查并添加缺失字段
      await this.ensureRecurringTaskFields()
    } catch {
      // 表不存在，创建完整表
      await this.prisma.$executeRaw`CREATE TABLE IF NOT EXISTS "RecurringTask" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "title" TEXT NOT NULL,
        "description" TEXT,
        "repeatRule" TEXT NOT NULL,
        "repeatInterval" INTEGER NOT NULL DEFAULT 1,
        "nextDueDate" DATETIME,
        "lastCompletedAt" DATETIME,
        "isActive" BOOLEAN NOT NULL DEFAULT true,
        "areaId" TEXT NOT NULL,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
      )`
    }
  }

  /** 确保 RecurringTask 表包含所有必需字段 */
  private async ensureRecurringTaskFields(): Promise<void> {
    const fieldsToCheck = [
      { name: 'isActive', type: 'BOOLEAN NOT NULL DEFAULT true' }
    ]

    for (const field of fieldsToCheck) {
      try {
        await this.prisma.$queryRaw`SELECT ${field.name} FROM RecurringTask LIMIT 1`
      } catch {
        await this.prisma.$executeRaw`ALTER TABLE "RecurringTask" ADD COLUMN "${field.name}" ${field.type}`
      }
    }

    // 确保 areaId 不为空
    try {
      await this.prisma.$executeRaw`UPDATE "RecurringTask" SET "areaId" = 'default-area' WHERE "areaId" IS NULL`
    } catch {
      // 忽略错误
    }
  }

  /**
   * 检查并执行所有必要的迁移
   */
  async checkAndExecuteMigrations(): Promise<{ success: boolean; migrations: string[]; errors: string[] }> {
    const executedMigrations: string[] = []
    const errors: string[] = []

    try {
      // 检查领域指标增强迁移
      if (await this.needsAreaMetricEnhancement()) {
        console.log('检测到需要执行领域指标增强迁移')
        const result = await this.executeAreaMetricEnhancement()

        if (result.success) {
          executedMigrations.push('AreaMetricEnhancement')
        } else {
          errors.push(`AreaMetricEnhancement: ${result.error}`)
        }
      }

      // 修复 ResourceLink.resourcePath 缺失
      try {
        await this.ensureResourceLinkResourcePath()
        executedMigrations.push('ResourceLinkEnsureResourcePath')
      } catch (e) {
        errors.push(`ResourceLinkEnsureResourcePath: ${e instanceof Error ? e.message : String(e)}`)
      }

      // 补齐其它缺失列以匹配当前 Prisma 模型
      try { await this.ensureReviewTemplateTable(); executedMigrations.push('ReviewTemplateEnsureTable') } catch (e) { errors.push(`ReviewTemplateEnsureTable: ${e instanceof Error ? e.message : String(e)}`) }
      try { await this.ensureAreaMetricTable(); executedMigrations.push('AreaMetricEnsureTable') } catch (e) { errors.push(`AreaMetricEnsureTable: ${e instanceof Error ? e.message : String(e)}`) }
      try { await this.ensureRecurringTaskTable(); executedMigrations.push('RecurringTaskEnsureTable') } catch (e) { errors.push(`RecurringTaskEnsureTable: ${e instanceof Error ? e.message : String(e)}`) }
      try { await this.ensureProjectProgress(); executedMigrations.push('ProjectEnsureProgress') } catch (e) { errors.push(`ProjectEnsureProgress: ${e instanceof Error ? e.message : String(e)}`) }
      try { await this.ensureProjectGoal(); executedMigrations.push('ProjectEnsureGoal') } catch (e) { errors.push(`ProjectEnsureGoal: ${e instanceof Error ? e.message : String(e)}`) }
      try { await this.ensureAreaStandard(); executedMigrations.push('AreaEnsureStandard') } catch (e) { errors.push(`AreaEnsureStandard: ${e instanceof Error ? e.message : String(e)}`) }
      try { await this.ensureHabitTarget(); executedMigrations.push('HabitEnsureTarget') } catch (e) { errors.push(`HabitEnsureTarget: ${e instanceof Error ? e.message : String(e)}`) }
      try { await this.ensureAreaMetricValue(); executedMigrations.push('AreaMetricEnsureValue') } catch (e) { errors.push(`AreaMetricEnsureValue: ${e instanceof Error ? e.message : String(e)}`) }
      try { await this.ensureAreaMetricTarget(); executedMigrations.push('AreaMetricEnsureTarget') } catch (e) { errors.push(`AreaMetricEnsureTarget: ${e instanceof Error ? e.message : String(e)}`) }
      try { await this.ensureRecurringTaskRepeatRule(); executedMigrations.push('RecurringTaskEnsureRepeatRule') } catch (e) { errors.push(`RecurringTaskEnsureRepeatRule: ${e instanceof Error ? e.message : String(e)}`) }
      try { await this.ensureRecurringTaskRepeatInterval(); executedMigrations.push('RecurringTaskEnsureRepeatInterval') } catch (e) { errors.push(`RecurringTaskEnsureRepeatInterval: ${e instanceof Error ? e.message : String(e)}`) }

      // 检查ProjectKPI字段迁移
      if (await this.needsProjectKPIDirectionMigration()) {
        console.log('检测到需要执行ProjectKPI字段迁移')
        const result = await this.executeProjectKPIDirectionMigration()

        if (result.success) {
          executedMigrations.push('ProjectKPIFieldsMigration')
        } else {
          errors.push(`ProjectKPIFieldsMigration: ${result.error}`)
        }
      }

      return {
        success: errors.length === 0,
        migrations: executedMigrations,
        errors
      }
    } catch (error) {
      console.error('迁移检查失败:', error)
      return {
        success: false,
        migrations: executedMigrations,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      }
    }
  }

  /**
   * 回滚领域指标增强迁移（仅用于开发测试）
   */
  async rollbackAreaMetricEnhancement(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('开始回滚领域指标增强迁移...')

      // 删除AreaMetric表的新字段
      const areaMetricColumns = [
        'trackingType', 'habitConfig', 'standardConfig',
        'isActive', 'priority', 'category', 'description'
      ]

      for (const column of areaMetricColumns) {
        try {
          await this.prisma.$executeRaw`ALTER TABLE "AreaMetric" DROP COLUMN "${column}"`
        } catch (error) {
          console.warn(`删除列 ${column} 失败:`, error)
        }
      }

      // 删除AreaMetricRecord表的新字段
      const recordColumns = [
        'mood', 'energy', 'context', 'tags',
        'quality', 'duration', 'difficulty'
      ]

      for (const column of recordColumns) {
        try {
          await this.prisma.$executeRaw`ALTER TABLE "AreaMetricRecord" DROP COLUMN "${column}"`
        } catch (error) {
          console.warn(`删除列 ${column} 失败:`, error)
        }
      }

      console.log('领域指标增强迁移回滚完成')
      return { success: true }
    } catch (error) {
      console.error('迁移回滚失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown rollback error'
      }
    }
  }

  /**
   * 强制重新检查并执行所有迁移（用于开发调试）
   */
  async forceCheckAndExecuteMigrations(): Promise<{ success: boolean; migrations: string[]; errors: string[] }> {
    const executedMigrations: string[] = []
    const errors: string[] = []

    try {
      console.log('强制执行所有迁移检查...')

      // 强制检查领域指标增强迁移
      console.log('检查领域指标增强迁移...')
      const areaResult = await this.executeAreaMetricEnhancement()

      if (areaResult.success) {
        executedMigrations.push('AreaMetricEnhancement')
      } else {
        errors.push(`AreaMetricEnhancement: ${areaResult.error}`)
      }

      // 强制检查ProjectKPI字段迁移
      console.log('检查ProjectKPI字段迁移...')
      const kpiResult = await this.executeProjectKPIDirectionMigration()

      if (kpiResult.success) {
        executedMigrations.push('ProjectKPIFieldsMigration')
      } else {
        errors.push(`ProjectKPIFieldsMigration: ${kpiResult.error}`)
      }

      return {
        success: errors.length === 0,
        migrations: executedMigrations,
        errors
      }
    } catch (error) {
      console.error('强制迁移检查失败:', error)
      return {
        success: false,
        migrations: executedMigrations,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      }
    }
  }

  /**
   * 获取数据库schema信息
   */
  async getDatabaseInfo(): Promise<{
    areaMetricColumns: string[]
    areaMetricRecordColumns: string[]
    projectKPIColumns: string[]
    hasEnhancedFields: boolean
  }> {
    try {
      // 获取AreaMetric表的列信息
      const areaMetricInfo = await this.prisma.$queryRaw<Array<{ name: string }>>`
        PRAGMA table_info(AreaMetric)
      `

      const areaMetricRecordInfo = await this.prisma.$queryRaw<Array<{ name: string }>>`
        PRAGMA table_info(AreaMetricRecord)
      `

      const projectKPIInfo = await this.prisma.$queryRaw<Array<{ name: string }>>`
        PRAGMA table_info(ProjectKPI)
      `

      const areaMetricColumns = areaMetricInfo.map(col => col.name)
      const areaMetricRecordColumns = areaMetricRecordInfo.map(col => col.name)
      const projectKPIColumns = projectKPIInfo.map(col => col.name)

      const hasEnhancedFields = areaMetricColumns.includes('trackingType') &&
                               areaMetricColumns.includes('direction') &&
                               projectKPIColumns.includes('direction')

      return {
        areaMetricColumns,
        areaMetricRecordColumns,
        projectKPIColumns,
        hasEnhancedFields
      }
    } catch (error) {
      console.error('获取数据库信息失败:', error)
      return {
        areaMetricColumns: [],
        areaMetricRecordColumns: [],
        projectKPIColumns: [],
        hasEnhancedFields: false
      }
    }
  }
}

export default MigrationHelper
