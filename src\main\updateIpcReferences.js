// <PERSON>ript to update IPC references from databaseService to kyselyDatabaseService
// This is a temporary script to help with the migration

const fs = require('fs')
const path = require('path')

const ipcFilePath = path.join(__dirname, 'ipc.ts')

// Read the file
let content = fs.readFileSync(ipcFilePath, 'utf-8')

// Replace all occurrences of databaseService with kyselyDatabaseService
content = content.replace(/databaseService\./g, 'kyselyDatabaseService.')

// Handle special cases that need different treatment
const specialReplacements = [
  // Remove Prisma-specific methods that don't exist in Kysely service
  {
    from: /kyselyDatabaseService\.prisma/g,
    to: 'kyselyDatabaseService.db'
  },
  {
    from: /kyselyDatabaseService\.getClient\(\)/g,
    to: 'kyselyDatabaseService.db'
  },
  {
    from: /kyselyDatabaseService\.getDocumentLinkService\(\)/g,
    to: 'documentLinkService'
  },
  {
    from: /kyselyDatabaseService\.config/g,
    to: 'kyselyDatabaseService.config'
  }
]

// Apply special replacements
specialReplacements.forEach(replacement => {
  content = content.replace(replacement.from, replacement.to)
})

// Write the updated content back
fs.writeFileSync(ipcFilePath, content, 'utf-8')

console.log('IPC references updated successfully!')
console.log('Note: Some manual adjustments may still be needed for complex Prisma queries.')
