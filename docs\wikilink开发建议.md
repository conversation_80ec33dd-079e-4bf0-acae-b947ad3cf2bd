Milkdown Wikilink 插件开发实践指南
本指南将带你从零开始，为 Milkdown 开发一个功能完善的 Wikilink ([[链接内容]]) 插件。我们将实现以下核心功能：

语法解析: 正确解析 Markdown 中的 [[链接内容]] 语法。

自定义渲染: 在编辑器中将 Wikilink 渲染成一个可点击的、样式独特的链接。

交互处理: 允许用户点击链接，并触发自定义的回调函数（例如，跳转到新页面或显示预览）。

序列化: 将编辑器中的 Wikilink 正确地转换回 [[链接内容]] 的 Markdown 文本。

第一步：设计与决策 - Node 还是 Mark？
对于 Wikilink，我们首先要决定它是 ProseMirror 中的 Node 还是 Mark。

Mark: 通常用于给文本添加样式，如加粗或斜体。一个 Mark 可以跨越多个文本节点。

Node: 代表文档中的一个结构化元素，如段落、标题或图片。

Wikilink ([[Page Name]]) 的行为更像一个独立的、原子化的实体，它有自己的数据（页面名称），并且通常不包含其他格式。因此，将其实现为一个行内节点 (inline node) 是更合适的选择。这给了我们更大的灵活性来控制它的渲染和行为。

第二步：Schema 定义
我们需要定义一个新的 wikilink行内节点。

// a-node-factory.ts
import { defineNode } from '@milkdown/core';

export const wikilinkNode = defineNode({
    id: 'wikilink',
    schema: () => ({
        group: 'inline', // 这是一个行内节点
        inline: true,
        atom: true, // 原子节点，意味着它不可被分割，其内容由 ProseMirror 管理
        attrs: {
            // 定义节点的属性
            pageName: { default: '' },
        },
        parseDOM: [{
            // 从 DOM 解析，例如处理粘贴的内容
            tag: 'a[data-type="wikilink"]',
            getAttrs: (dom) => ({
                pageName: (dom as HTMLElement).innerText,
            }),
        }],
        toDOM: (node) => [
            // 渲染到 DOM
            'a',
            {
                'data-type': 'wikilink',
                'class': 'wikilink', // 自定义 class 用于 CSS 样式
                'href': `#${node.attrs.pageName}`,
            },
            `[[${node.attrs.pageName}]]`, // 在编辑器中显示的文本
        ],
    }),
    // ... 接下来的解析器和序列化器部分
});

关键点:

group: 'inline' 和 inline: true 定义了它是一个行内节点。

atom: true 告诉 ProseMirror 将其视为一个不可分割的整体，用户不能将光标置于其内部。

attrs 存储了我们的核心数据：pageName。

toDOM 定义了它在编辑器中会被渲染成一个带有特定 class 和 data-type 的 <a> 标签。

第三步：Parser - 解析 [[...]] 语法
这是最核心的一步。我们需要一个 Remark 插件来识别 [[...]] 语法，并将其转换成 Milkdown 可以理解的 MDAST（Markdown 抽象语法树）节点。

1. 编写 Remark 插件

你需要创建一个 remark-wikilink 插件。幸运的是，社区已经有类似的实现可以参考，例如 remark-gfm 中处理链接的逻辑。基本思路是利用正则表达式 \[\[([^\]]+)\]\] 来匹配语法。

一个简化的 remark-wikilink 插件可能看起来像这样：

// remark-wikilink-plugin.js
import { visit } from 'unist-util-visit';

const wikilinkRegex = /\[\[([^\]]+)\]\]/g;

export function remarkWikilink() {
    return (tree) => {
        visit(tree, 'text', (node, index, parent) => {
            if (!parent || index == null) return;

            const text = node.value;
            const newNodes = [];
            let lastIndex = 0;
            let match;

            while ((match = wikilinkRegex.exec(text)) !== null) {
                const [fullMatch, pageName] = match;
                const matchStart = match.index;
                const matchEnd = matchStart + fullMatch.length;

                // 添加匹配前的文本
                if (matchStart > lastIndex) {
                    newNodes.push({ type: 'text', value: text.slice(lastIndex, matchStart) });
                }

                // 添加 wikilink 节点
                newNodes.push({
                    type: 'wikilink', // 自定义的 MDAST 节点类型
                    value: pageName,
                });

                lastIndex = matchEnd;
            }

            // 添加匹配后的剩余文本
            if (lastIndex < text.length) {
                newNodes.push({ type: 'text', value: text.slice(lastIndex) });
            }

            if (newNodes.length > 0) {
                // 用新节点替换旧的文本节点
                parent.children.splice(index, 1, ...newNodes);
            }
        });
    };
}

2. 配置 Milkdown 解析器

现在，将这个 Remark 插件和我们定义的 wikilink 节点连接起来。

// in wikilinkNode definition
import { defineNode } from '@milkdown/core';
import { remarkWikilink } from './remark-wikilink-plugin'; // 引入你的 remark 插件

export const wikilinkNode = defineNode({
    id: 'wikilink',
    schema: () => ({ /* ... a-schema-definition.ts ... */ }),
    parser: {
        // 配置如何将 MDAST 节点转换为 ProseMirror 节点
        fromMdast: {
            type: 'wikilink', // 匹配 remark 插件生成的 MDAST 节点类型
            getAttrs: (node) => ({
                pageName: node.value as string,
            }),
        },
        // 告诉 Milkdown 的解析器使用你的 remark 插件
        remarkPlugins: () => [remarkWikilink],
    },
    // ...
});

第四步：Serializer - 将节点转换回 Markdown
当编辑器内容被导出为 Markdown 时，我们需要将 wikilink 节点转换回 [[...]] 文本。

// in wikilinkNode definition
import { defineNode } from '@milkdown/core';

export const wikilinkNode = defineNode({
    // ... id, schema, parser ...
    serializer: {
        // 配置如何将 ProseMirror 节点转换为 MDAST 节点
        toMdast: (node) => ({
            type: 'text', // 最终输出为纯文本
            value: `[[${node.attrs.pageName}]]`,
        }),
    },
});

这里我们直接将其序列化为 text 节点，其值就是 [[Page Name]]。

第五步：交互 - 处理点击事件
为了让 Wikilink 可交互，我们需要一个 ProseMirror 插件来监听点击事件。

// b-prosemirror-plugin.ts
import { Plugin, PluginKey } from '@milkdown/prose/state';
import { EditorView } from '@milkdown/prose/view';

export const createWikilinkClickListener = (onClick: (pageName: string) => void) => {
    return new Plugin({
        key: new PluginKey('WIKILINK_CLICK_LISTENER'),
        props: {
            handleClick: (view: EditorView, pos: number, event: MouseEvent) => {
                const target = event.target as? HTMLElement;
                if (!target || target.tagName !== 'A' || !target.classList.contains('wikilink')) {
                    return false; // 不是我们的 wikilink，交由其他插件处理
                }

                event.preventDefault(); // 阻止默认的跳转行为
                const pageName = target.innerText.slice(2, -2); // 从 "[[Page Name]]" 提取 "Page Name"
                onClick(pageName);
                return true; // 事件已处理
            },
        },
    });
};

第六步：封装为 Milkdown Atom
最后，将所有部分（Node 定义、ProseMirror 插件）组合成一个完整的 Milkdown 插件。

// c-final-plugin.ts
import { createPlugin } from '@milkdown/core';
import { wikilinkNode } from './a-node-factory';
import { createWikilinkClickListener } from './b-prosemirror-plugin';

// 定义插件的选项，例如点击回调
export type WikilinkOptions = {
    onClick: (pageName: string) => void;
};

export const wikilinkPlugin = (options: WikilinkOptions) => {
    return createPlugin()({
        // 1. 添加 ProseMirror 节点定义
        nodes: [wikilinkNode],
        // 2. 添加 ProseMirror 插件用于交互
        prosePlugins: () => [createWikilinkClickListener(options.onClick)],
    });
};

// --- 在编辑器中使用 ---
// import { Editor } from '@milkdown/core';
// import { commonmark } from '@milkdown/preset-commonmark';
// import { wikilinkPlugin } from './c-final-plugin';

// Editor.make()
//   .use(commonmark)
//   .use(wikilinkPlugin({
//     onClick: (pageName) => {
//       console.log('Wikilink clicked:', pageName);
//       // 在这里实现你的跳转或预览逻辑
//     }
//   }))
//   .create();

总结
通过以上六个步骤，你就拥有了一个功能完整的 Wikilink 插件。这个过程覆盖了从数据结构定义（Schema）、语法解析（Parser）、文本生成（Serializer）到用户交互（ProseMirror Plugin）的完整流程，是 Milkdown 插件开发的典型范例。

下一步，你可以尝试为其增加更高级的功能，比如输入 [[ 时弹出页面建议列表。祝你编码愉快！