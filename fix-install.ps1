Write-Host "修复pnpm安装问题..." -ForegroundColor Green

# 设置环境变量以使用国内镜像
Write-Host "设置Electron下载镜像..." -ForegroundColor Yellow
$env:ELECTRON_MIRROR = "https://npmmirror.com/mirrors/electron/"
$env:ELECTRON_BUILDER_BINARIES_MIRROR = "https://npmmirror.com/mirrors/electron-builder-binaries/"
$env:ELECTRON_CACHE = "./cache/electron"
$env:ELECTRON_BUILDER_CACHE = "./cache/electron-builder"

# 创建缓存目录
Write-Host "创建缓存目录..." -ForegroundColor Yellow
if (!(Test-Path "cache")) {
    New-Item -ItemType Directory -Path "cache" -Force
}
if (!(Test-Path "cache/electron")) {
    New-Item -ItemType Directory -Path "cache/electron" -Force
}
if (!(Test-Path "cache/electron-builder")) {
    New-Item -ItemType Directory -Path "cache/electron-builder" -Force
}

# 清理node_modules（如果存在问题）
if ($args -contains "--clean") {
    Write-Host "清理node_modules..." -ForegroundColor Red
    if (Test-Path "node_modules") {
        Remove-Item -Path "node_modules" -Recurse -Force -ErrorAction SilentlyContinue
    }
    if (Test-Path "pnpm-lock.yaml") {
        Remove-Item -Path "pnpm-lock.yaml" -Force -ErrorAction SilentlyContinue
    }
}

# 设置网络超时
Write-Host "设置网络配置..." -ForegroundColor Yellow
pnpm config set network-timeout 300000
pnpm config set fetch-timeout 300000
pnpm config set fetch-retry-mintimeout 20000
pnpm config set fetch-retry-maxtimeout 120000

# 尝试安装
Write-Host "开始安装依赖..." -ForegroundColor Green
Write-Host "如果仍然失败，请尝试以下方案：" -ForegroundColor Cyan
Write-Host "1. 使用VPN或更换网络" -ForegroundColor Cyan
Write-Host "2. 运行: .\fix-install.ps1 --clean" -ForegroundColor Cyan
Write-Host "3. 手动下载Electron到cache目录" -ForegroundColor Cyan

pnpm install

if ($LASTEXITCODE -eq 0) {
    Write-Host "安装成功！" -ForegroundColor Green
    Write-Host "生成Prisma客户端..." -ForegroundColor Yellow
    pnpm exec prisma generate
    if ($LASTEXITCODE -eq 0) {
        Write-Host "所有依赖安装完成！" -ForegroundColor Green
    } else {
        Write-Host "Prisma客户端生成失败，请手动运行: pnpm exec prisma generate" -ForegroundColor Red
    }
} else {
    Write-Host "安装失败！请检查网络连接或尝试其他解决方案。" -ForegroundColor Red
}
