import React, { useState, useEffect, useCallback } from 'react'
import { UnifiedReference } from '../../services/unifiedReferenceService'
import { databaseApi, fileSystemApi } from '../../lib/api'
import { getResourcesPath } from '../../plugins/wikilink/utils'
import { useUserSettingsStore } from '../../store/userSettingsStore'

// 简单的内存缓存
const previewCache = new Map<string, { data: any; timestamp: number }>()
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

// 缓存辅助函数
const getCachedData = (key: string) => {
  const cached = previewCache.get(key)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data
  }
  return null
}

const setCachedData = (key: string, data: any) => {
  previewCache.set(key, { data, timestamp: Date.now() })
}



interface ReferencePreviewProps {
  reference: UnifiedReference
  onClose: () => void
  onNavigate: (reference: UnifiedReference) => void
  className?: string
}

/**
 * 引用预览组件
 * 显示引用的详细信息和预览内容
 */
export const ReferencePreview: React.FC<ReferencePreviewProps> = ({
  reference,
  onClose,
  onNavigate,
  className = ''
}) => {
  const [previewContent, setPreviewContent] = useState<string>('')
  const [previewData, setPreviewData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { settings } = useUserSettingsStore()

  // 加载预览内容
  useEffect(() => {
    const loadPreview = async () => {
      setLoading(true)
      setError(null)

      try {
        switch (reference.referenceType) {
          case 'wikilink':
            const cacheKey = `wikilink:${reference.targetPath}`
            let content = getCachedData(cacheKey)
            if (!content) {
              content = await loadWikiLinkPreview(reference)
              setCachedData(cacheKey, content)
            }
            setPreviewContent(content)
            setPreviewData(null)
            break
          case 'description':
          case 'task':
          case 'note':
            // 对于项目和领域引用，我们只需要设置一个标记，让组件自己加载数据
            setPreviewData({ id: reference.sourceId, type: reference.referenceType })
            setPreviewContent('')
            break
          default:
            setError('暂不支持此类型的预览')
        }
      } catch (err) {
        console.error('加载预览内容失败:', err)
        setError(err instanceof Error ? err.message : '加载预览内容失败')
      } finally {
        setLoading(false)
      }
    }

    // 添加延迟以避免频繁调用
    const timeoutId = setTimeout(loadPreview, 300)

    return () => {
      clearTimeout(timeoutId)
    }
  }, [reference.referenceType, reference.sourceId, reference.targetPath, reference.targetTitle, settings])

  // 加载 WikiLink 预览内容
  const loadWikiLinkPreview = async (ref: UnifiedReference): Promise<string> => {
    try {
      if (!ref.targetPath) {
        return '文件路径不存在，无法加载预览'
      }

      // 将虚拟路径转换为真实路径
      const resourcesPath = await getResourcesPath(settings)
      const realPath = `${resourcesPath}/${ref.targetPath.replace(/^\/+/, '')}`

      const result = await fileSystemApi.readFile({ path: realPath })
      if (result.success && result.data) {
        const content = result.data.content || ''
        if (content.trim() === '') {
          return `文件 "${ref.targetTitle}" 为空`
        }
        return content
      } else {
        return `文件不存在或无法访问: ${ref.targetTitle}\n\n错误信息: ${result.error || '未知错误'}`
      }
    } catch (error) {
      console.error('加载WikiLink预览失败:', error)
      return `读取文件失败: ${error instanceof Error ? error.message : '文件系统错误'}`
    }
  }

  // 获取引用类型的图标和颜色
  const getReferenceStyle = () => {
    switch (reference.referenceType) {
      case 'wikilink':
        return {
          icon: '📄',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200'
        }
      case 'description':
      case 'task':
        return {
          icon: reference.referenceType === 'task' ? '✅' : '📋',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        }
      case 'note':
        return {
          icon: '🏷️',
          color: 'text-purple-600',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200'
        }
      default:
        return {
          icon: '📎',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        }
    }
  }

  const style = getReferenceStyle()

  return (
    <div className={`reference-preview ${className}`}>
      {/* 遮罩层 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* 预览窗口 */}
      <div className="fixed inset-4 md:inset-8 lg:inset-16 bg-white rounded-lg shadow-2xl z-50 flex flex-col">
        {/* 头部 */}
        <div className={`flex items-center justify-between p-4 border-b ${style.borderColor} ${style.bgColor}`}>
          <div className="flex items-center space-x-3">
            <span className="text-2xl">{style.icon}</span>
            <div>
              <h2 className={`text-lg font-semibold ${style.color}`}>
                {reference.targetTitle}
              </h2>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <span className="capitalize">{reference.referenceType}</span>
                <span>•</span>
                <span>强度: {(reference.strength * 100).toFixed(0)}%</span>
                {reference.sourceType !== 'document' && (
                  <>
                    <span>•</span>
                    <span>来源: {reference.sourceTitle}</span>
                  </>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={() => onNavigate(reference)}
              className={`px-3 py-1 text-sm font-medium ${style.color} ${style.bgColor} border ${style.borderColor} rounded hover:opacity-80 transition-opacity`}
            >
              跳转
            </button>
            <button
              type="button"
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="关闭预览"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* 引用上下文 */}
        {reference.context && (
          <div className="p-4 border-b border-gray-200 bg-gray-50">
            <h3 className="text-sm font-medium text-gray-700 mb-2">引用上下文</h3>
            <div className="text-sm text-gray-600 bg-white p-3 rounded border">
              {reference.context}
            </div>
          </div>
        )}

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                <div className="text-gray-500">加载预览内容...</div>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-red-500">
                <div className="text-4xl mb-4">❌</div>
                <div className="font-medium mb-2">加载失败</div>
                <div className="text-sm mb-4">{error}</div>
                <button
                  type="button"
                  onClick={() => onNavigate(reference)}
                  className={`px-4 py-2 ${style.color} ${style.bgColor} border ${style.borderColor} rounded hover:opacity-80 transition-opacity`}
                >
                  跳转查看
                </button>
              </div>
            </div>
          ) : previewContent ? (
            // WikiLink 预览 - 显示文件内容
            <div className="h-full overflow-y-auto p-4">
              <div className="prose prose-sm max-w-none">
                <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed">
                  {previewContent}
                </pre>
              </div>
            </div>
          ) : previewData ? (
            // 项目/领域预览 - 显示完整详情页组件
            <div className="h-full overflow-y-auto">
              <PreviewContentRenderer
                referenceType={reference.referenceType}
                sourceId={reference.sourceId}
              />
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-500">
                <div className="text-4xl mb-4">{style.icon}</div>
                <div className="font-medium mb-2">{reference.targetTitle}</div>
                <div className="text-sm mb-4">暂无预览内容</div>
                <button
                  type="button"
                  onClick={() => onNavigate(reference)}
                  className={`px-4 py-2 ${style.color} ${style.bgColor} border ${style.borderColor} rounded hover:opacity-80 transition-opacity`}
                >
                  跳转查看
                </button>
              </div>
            </div>
          )}
        </div>

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span>创建时间: {new Date(reference.createdAt).toLocaleString()}</span>
            {reference.updatedAt !== reference.createdAt && (
              <span>更新时间: {new Date(reference.updatedAt).toLocaleString()}</span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors"
            >
              关闭
            </button>
            <button
              type="button"
              onClick={() => onNavigate(reference)}
              className={`px-4 py-2 text-white ${style.color.replace('text-', 'bg-')} rounded hover:opacity-90 transition-opacity`}
            >
              打开
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// 统一的预览内容组件 - 避免条件渲染中的hooks问题
const PreviewContentRenderer: React.FC<{
  referenceType: string
  sourceId: string
}> = ({ referenceType, sourceId }) => {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadData = useCallback(async () => {
    try {
      setLoading(true)

      const cacheKey = `${referenceType}:${sourceId}`
      let cachedData = getCachedData(cacheKey)

      if (!cachedData) {
        let result
        if (referenceType === 'description' || referenceType === 'task') {
          result = await databaseApi.getProjectById(sourceId)
        } else if (referenceType === 'note') {
          result = await databaseApi.getAreaById(sourceId)
        } else {
          throw new Error('未知的引用类型')
        }

        if (result.success && result.data) {
          cachedData = result.data
          setCachedData(cacheKey, cachedData)
        } else {
          throw new Error('数据不存在或已被删除')
        }
      }

      setData(cachedData)
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载失败')
    } finally {
      setLoading(false)
    }
  }, [referenceType, sourceId])

  useEffect(() => {
    loadData()
  }, [loadData])

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex items-center gap-2 text-gray-500">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          <span>加载数据...</span>
        </div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500">
          <div className="text-4xl mb-2">❌</div>
          <p className="text-sm">{error || '数据未找到'}</p>
        </div>
      </div>
    )
  }

  // 根据类型渲染不同的内容，但使用相同的结构
  const isProject = referenceType === 'description' || referenceType === 'task'

  return (
    <div className="p-6 space-y-6">
      {/* 基本信息 */}
      <div className="bg-white rounded-lg border p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <h1 className="text-2xl font-bold">{data.name}</h1>
              <div className="text-sm text-gray-600">
                {data.description || '暂无描述'}
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                {isProject ? '项目目标' : '领域标准'}
              </h3>
              <div className="text-sm text-gray-600">
                {isProject ? (data.goal || '暂无目标') : (data.standard || '暂无标准')}
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">状态</span>
                <span className="font-medium">
                  {isProject
                    ? (data.status || '进行中')
                    : (data.archived ? '已归档' : '活跃')
                  }
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">创建时间</span>
                <span>{new Date(data.createdAt).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">最后更新</span>
                <span>{new Date(data.updatedAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 提示信息 */}
      <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
        <div className="text-4xl mb-2">🔍</div>
        <p className="text-sm font-medium">预览模式</p>
        <p className="text-xs mt-1">
          完整的{isProject ? '项目' : '领域'}详情（包括KPI、{isProject ? '任务' : '习惯'}、资源等）请点击"跳转"按钮查看
        </p>
      </div>
    </div>
  )
}

export default ReferencePreview
