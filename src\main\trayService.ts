import { app, Tray, Menu, nativeImage, BrowserWindow } from 'electron'
import path from 'path'

export class TrayService {
  private tray: Tray | null = null
  private mainWindow: BrowserWindow | null = null

  constructor(mainWindow: BrowserWindow) {
    this.mainWindow = mainWindow
  }

  /**
   * 创建系统托盘
   */
  createTray(): void {
    try {
      // 创建托盘图标
      const iconPath = this.getTrayIconPath()
      console.log('🔧 [TrayService] Creating tray with icon path:', iconPath)

      // 检查图标文件是否存在
      const fs = require('fs')
      if (!fs.existsSync(iconPath)) {
        console.error('❌ [TrayService] Tray icon file not found:', iconPath)
        return
      }

      const trayIcon = nativeImage.createFromPath(iconPath)

      if (trayIcon.isEmpty()) {
        console.error('❌ [TrayService] Failed to load tray icon from:', iconPath)
        return
      }

      // 调整图标大小（Windows和Linux需要16x16，macOS需要22x22）
      if (process.platform === 'darwin') {
        trayIcon.setTemplateImage(true)
      }

      this.tray = new Tray(trayIcon)

      // 设置托盘提示文本
      this.tray.setToolTip('PaoLife - 个人知识管理系统')

      // 创建托盘菜单
      this.updateTrayMenu()

      // 托盘图标点击事件
      this.tray.on('click', () => {
        console.log('🖱️ [TrayService] Tray icon clicked')
        this.toggleWindow()
      })

      // 托盘图标双击事件（Windows）
      this.tray.on('double-click', () => {
        console.log('🖱️ [TrayService] Tray icon double-clicked')
        this.showWindow()
      })

      console.log('✅ [TrayService] System tray created successfully')
    } catch (error) {
      console.error('❌ [TrayService] Failed to create system tray:', error)
    }
  }

  /**
   * 更新托盘菜单
   */
  private updateTrayMenu(): void {
    if (!this.tray) return

    const isWindowVisible = this.mainWindow?.isVisible() && !this.mainWindow?.isMinimized()
    
    const contextMenu = Menu.buildFromTemplate([
      {
        label: isWindowVisible ? '隐藏窗口' : '显示窗口',
        click: () => this.toggleWindow()
      },
      {
        type: 'separator'
      },
      {
        label: '新建项目',
        click: () => {
          this.showWindow()
          // 发送新建项目事件到渲染进程
          this.mainWindow?.webContents.send('tray-new-project')
        }
      },
      {
        label: '新建领域',
        click: () => {
          this.showWindow()
          // 发送新建领域事件到渲染进程
          this.mainWindow?.webContents.send('tray-new-area')
        }
      },
      {
        type: 'separator'
      },
      {
        label: '设置',
        click: () => {
          this.showWindow()
          // 发送打开设置事件到渲染进程
          this.mainWindow?.webContents.send('tray-open-settings')
        }
      },
      {
        type: 'separator'
      },
      {
        label: '退出',
        click: () => {
          // 直接退出，不显示确认对话框
          this.forceQuit()
        }
      }
    ])

    this.tray.setContextMenu(contextMenu)
  }

  /**
   * 切换窗口显示/隐藏
   */
  private toggleWindow(): void {
    if (!this.mainWindow) return

    const isVisible = this.mainWindow.isVisible()
    const isMinimized = this.mainWindow.isMinimized()
    console.log('🔄 [TrayService] Toggle window - visible:', isVisible, 'minimized:', isMinimized)

    if (isVisible && !isMinimized) {
      console.log('🔽 [TrayService] Window is visible and not minimized, hiding to tray')
      this.hideWindow()
    } else {
      console.log('🔼 [TrayService] Window is hidden or minimized, showing from tray')
      this.showWindow()
    }
  }

  /**
   * 显示窗口
   */
  private showWindow(): void {
    if (!this.mainWindow) {
      console.warn('⚠️ [TrayService] Cannot show window - mainWindow is null')
      return
    }

    console.log('🔼 [TrayService] Showing window from tray')

    // 确保窗口在任务栏中显示
    try {
      if (typeof this.mainWindow.setSkipTaskbar === 'function') {
        this.mainWindow.setSkipTaskbar(false)
      }
    } catch (error) {
      console.warn('⚠️ [TrayService] setSkipTaskbar not supported:', error.message)
    }

    if (this.mainWindow.isMinimized()) {
      console.log('📐 [TrayService] Restoring minimized window')
      this.mainWindow.restore()
    }

    this.mainWindow.show()
    this.mainWindow.focus()

    // 在Windows上可能需要额外的焦点处理
    if (process.platform === 'win32') {
      this.mainWindow.setAlwaysOnTop(true)
      setTimeout(() => {
        this.mainWindow?.setAlwaysOnTop(false)
      }, 100)
    }

    // 更新托盘菜单
    this.updateTrayMenu()
    console.log('✅ [TrayService] Window shown from tray successfully')
  }

  /**
   * 隐藏窗口
   */
  private hideWindow(): void {
    if (!this.mainWindow) {
      console.warn('⚠️ [TrayService] Cannot hide window - mainWindow is null')
      return
    }

    console.log('🔽 [TrayService] Hiding window to tray')

    // 确保窗口不是最小化状态，而是真正隐藏
    if (this.mainWindow.isMinimized()) {
      console.log('📐 [TrayService] Restoring from minimized state before hiding')
      this.mainWindow.restore()
    }

    // 设置跳过任务栏并隐藏窗口（与IPC处理器相同的逻辑）
    try {
      if (typeof this.mainWindow.setSkipTaskbar === 'function') {
        this.mainWindow.setSkipTaskbar(true)
      }
    } catch (error) {
      console.warn('⚠️ [TrayService] setSkipTaskbar not supported:', error.message)
    }
    this.mainWindow.hide()

    // 更新托盘菜单
    this.updateTrayMenu()
    console.log('✅ [TrayService] Window hidden to tray successfully')
  }

  /**
   * 强制退出应用
   */
  private forceQuit(): void {
    // 设置标志，跳过退出确认
    if (this.mainWindow) {
      (this.mainWindow as any).forceQuit = true
    }
    
    app.quit()
  }

  /**
   * 获取托盘图标路径
   */
  private getTrayIconPath(): string {
    const isDev = process.env.NODE_ENV === 'development'
    
    if (process.platform === 'win32') {
      // Windows 使用 .ico 文件
      return isDev 
        ? path.join(__dirname, '../../resources/tray-icon.ico')
        : path.join(process.resourcesPath, 'tray-icon.ico')
    } else if (process.platform === 'darwin') {
      // macOS 使用 .png 文件，并且需要 Template 模式
      return isDev
        ? path.join(__dirname, '../../resources/tray-iconTemplate.png')
        : path.join(process.resourcesPath, 'tray-iconTemplate.png')
    } else {
      // Linux 使用 .png 文件
      return isDev
        ? path.join(__dirname, '../../resources/tray-icon.png')
        : path.join(process.resourcesPath, 'tray-icon.png')
    }
  }

  /**
   * 窗口状态改变时更新托盘菜单
   */
  onWindowStateChange(): void {
    this.updateTrayMenu()
  }

  /**
   * 销毁托盘
   */
  destroy(): void {
    if (this.tray) {
      this.tray.destroy()
      this.tray = null
      console.log('System tray destroyed')
    }
  }

  /**
   * 检查是否支持托盘
   */
  static isSupported(): boolean {
    return process.platform !== 'linux' || process.env.XDG_CURRENT_DESKTOP !== undefined
  }
}
