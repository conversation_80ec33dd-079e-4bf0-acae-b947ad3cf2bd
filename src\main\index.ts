import './logger'

console.log('🚀 [main] Main process starting...')

import { app, shell, BrowserWindow, ipcMain, protocol, globalShortcut } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../build/icon.png?asset'
import kyselyDatabaseService from './kyselyDatabase'
import fileSystemService from './fileSystem'
import fileWatcherService from './fileWatcher'
import ipcHandler from './ipc'
import { TrayService } from './trayService'
import { configManager } from './configManager'
import fs from 'fs'
import path from 'path'

// Register schemes as privileged before app is ready
protocol.registerSchemesAsPrivileged([
  {
    scheme: 'vditor',
    privileges: {
      standard: true,
      secure: true,
      supportFetchAPI: true,
      corsEnabled: true
    }
  },
  {
    scheme: 'paolife-assets',
    privileges: {
      standard: true,
      secure: true,
      supportFetchAPI: true,
      corsEnabled: true
    }
  }
])

// Global reference to main window
let mainWindow: BrowserWindow
let trayService: TrayService | null = null
let inspirationWindow: BrowserWindow | null = null

// Global shortcuts registry
const registeredShortcuts = new Map<string, string>() // accelerator -> action

/**
 * 检查是否为首次启动
 * 使用新的配置管理器，优先从配置文件读取，必要时从localStorage迁移
 */
async function checkIfFirstTimeStartup(): Promise<boolean> {
  console.log('🔍 [checkIfFirstTimeStartup] Starting first time startup check...')

  try {
    // 首先检查配置文件
    const isFirstTime = configManager.isFirstTimeStartup()

    if (!isFirstTime) {
      console.log('🔍 [checkIfFirstTimeStartup] Found existing config - not first time startup')
      return false
    }

    // 如果配置文件不存在或无效，尝试从localStorage迁移
    console.log('🔄 [checkIfFirstTimeStartup] Attempting migration from localStorage...')
    const migrationSuccess = configManager.migrateFromLocalStorage()

    if (migrationSuccess) {
      console.log('✅ [checkIfFirstTimeStartup] Successfully migrated from localStorage')
      return false
    }

    console.log('🔍 [checkIfFirstTimeStartup] No valid config found - first time startup')
    return true
  } catch (error) {
    console.warn('⚠️ [checkIfFirstTimeStartup] Error during startup check:', error)
    return true
  }
}

/**
 * 加载用户工作目录设置
 * 使用新的配置管理器
 */
async function loadUserWorkspaceDirectory(): Promise<string | null> {
  console.log('📄 [loadUserWorkspaceDirectory] Loading user workspace directory...')

  try {
    // 使用配置管理器获取工作目录
    const workspaceDirectory = configManager.getWorkspaceDirectory()

    if (workspaceDirectory) {
      console.log('✅ [loadUserWorkspaceDirectory] Successfully loaded workspace directory:', workspaceDirectory)
      return workspaceDirectory
    }

    console.warn('⚠️ [loadUserWorkspaceDirectory] No workspace directory found in config')
    return null
  } catch (error) {
    console.error('❌ [loadUserWorkspaceDirectory] Error loading workspace directory:', error)
    return null
  }
}
// Exit confirmation preference management and custom dialog (self-contained)
// We keep this independent of existing renderer/UI code

type ExitAction = 'minimize' | 'exit' | 'cancel'
interface ExitPreference { remember: boolean; action: 'minimize' | 'exit' }

const getExitPrefPath = () => path.join(app.getPath('userData'), 'exit-preference.json')

async function loadExitPreference(): Promise<ExitPreference | null> {
  try {
    const data = await fs.promises.readFile(getExitPrefPath(), 'utf-8')
    const pref = JSON.parse(data)
    if (pref && (pref.action === 'minimize' || pref.action === 'exit') && typeof pref.remember === 'boolean') {
      return pref as ExitPreference
    }
    return null
  } catch {
    return null
  }
}

async function saveExitPreference(pref: ExitPreference): Promise<void> {
  try {
    await fs.promises.writeFile(getExitPrefPath(), JSON.stringify(pref), 'utf-8')
  } catch (e) {
    console.warn('⚠️ Failed to save exit preference:', (e as any)?.message)
  }
}

async function showExitConfirmDialog(parent: BrowserWindow): Promise<{ action: ExitAction; remember: boolean }>
{ return new Promise(async (resolve) => {
  const channel = `exit-confirm-dialog:choice:${Date.now()}`

  const dialogWin = new BrowserWindow({
    parent,
    modal: true,
    show: false,
    width: 520,
    height: 280,
    resizable: false,
    minimizable: false,
    maximizable: false,
    frame: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    backgroundColor: '#ffffff',
    webPreferences: {
      nodeIntegration: true, // contained, not exposing app surface
      contextIsolation: false,
      sandbox: false
    }
  })

  const html = `<!doctype html><html lang="zh-CN"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>退出确认</title><style>
    :root { --bg:#f7f7f9; --fg:#1f2937; --muted:#6b7280; --accent:#2563eb; --danger:#b91c1c; --card:#ffffff; }
    html,body{margin:0;height:100%;overflow:hidden;font-family:ui-sans-serif,system-ui,Segoe UI,Arial;}
    .wrap{display:flex;flex-direction:column;height:100%;background:var(--card);color:var(--fg);border:1px solid #e5e7eb;border-radius:10px;box-shadow:0 6px 24px #00000022;}
    .title{padding:12px 16px;font-weight:600;background:var(--bg);border-bottom:1px solid #e5e7eb;}
    .content{padding:16px;gap:12px;display:flex;flex-direction:column;}
    .desc{color:var(--muted);}
    .actions{margin-top:auto;display:flex;gap:8px;justify-content:flex-end;padding:12px 16px;border-top:1px solid #e5e7eb;background:var(--bg);}
    button{padding:8px 12px;border-radius:8px;border:1px solid #d1d5db;background:#ffffff;color:var(--fg);cursor:pointer}
    button:hover{background:#f3f4f6}
    .danger{background:#fee2e2;border-color:#fecaca;color:#7f1d1d}
    .accent{background:#dbeafe;border-color:#bfdbfe;color:#1e3a8a}
    .cancel{background:#ffffff}
    .row{display:flex;align-items:center;gap:8px;color:var(--muted)}
    .close{position:absolute;right:8px;top:8px;width:28px;height:28px;border-radius:6px;border:1px solid #e5e7eb;background:#ffffff;color:#6b7280}
    .close:hover{background:#f3f4f6}
  </style></head><body>
  <div class="wrap">
    <div class="title">退出确认 <button class="close" id="btn-x" title="关闭">×</button></div>
    <div class="content">
      <div>确定要关闭应用吗？</div>
      <div class="desc">选择“最小化到托盘”可在系统托盘中继续运行，稍后可从托盘重新打开。</div>
      <label class="row"><input type="checkbox" id="remember"> 记住此选择（下次不再询问）</label>
    </div>
    <div class="actions">
      <button class="cancel" id="btn-cancel">取消</button>
      <button class="accent" id="btn-min">最小化到托盘</button>
      <button class="danger" id="btn-exit">退出</button>
    </div>
  </div>
  <script>
    const { ipcRenderer } = require('electron')
    const remember = document.getElementById('remember')
    const send = (action) => {
      ipcRenderer.send('${channel}', { action, remember: !!remember.checked })
    }
    document.getElementById('btn-cancel').addEventListener('click', () => send('cancel'))
    document.getElementById('btn-min').addEventListener('click', () => send('minimize'))
    document.getElementById('btn-exit').addEventListener('click', () => send('exit'))
    document.getElementById('btn-x').addEventListener('click', () => send('cancel'))
    window.addEventListener('keydown', (e) => { if (e.key === 'Escape') send('cancel') })
    // Default focus on Cancel for safety
    setTimeout(() => document.getElementById('btn-cancel').focus(), 0)
  </script>
  </body></html>`

  await dialogWin.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(html))
  dialogWin.once('ready-to-show', () => dialogWin.show())

  const closeDialog = () => {
    if (!dialogWin.isDestroyed()) dialogWin.close()
    if (!dialogWin.isDestroyed()) dialogWin.destroy()
  }

  const handler = (_event: any, payload: { action: ExitAction; remember: boolean }) => {
    resolve(payload)
    closeDialog()
    ipcMain.removeAllListeners(channel)
  }
  ipcMain.once(channel, handler)
}) }


// Pre-create inspiration window for faster access
function preCreateInspirationWindow(shouldShow = false): void {
  if (inspirationWindow && !inspirationWindow.isDestroyed()) {
    if (shouldShow) {
      if (inspirationWindow.isMinimized()) {
        inspirationWindow.restore()
      }
      inspirationWindow.show()
      inspirationWindow.setOpacity(1)
      inspirationWindow.focus()
    }
    return
  }

  // Build platform-aware options for a modern floating inspiration window
  const inspirationOptions: Electron.BrowserWindowConstructorOptions & Record<string, any> = {
    width: 520,
    height: 280, // 增加高度以容纳标签下拉框
    frame: false,
    show: false, // Don't show initially
    alwaysOnTop: true,
    skipTaskbar: true,
    resizable: false,
    transparent: true,
    hasShadow: false, // 完全禁用窗口阴影
    backgroundColor: '#00000000', // Fully transparent
    opacity: 0, // Start with invisible
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      webSecurity: !is.dev,
      allowRunningInsecureContent: is.dev
    }
  }

  // 移除所有可能产生视觉边界的效果
  // macOS: 不使用 vibrancy 效果避免边界
  // Windows: 不使用 backgroundMaterial 避免边界

  inspirationWindow = new BrowserWindow(inspirationOptions)

  // Center the window on screen
  inspirationWindow.center()

  // Load the inspiration capture page
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    inspirationWindow.loadURL(`${process.env['ELECTRON_RENDERER_URL']}#/inspiration-capture`)
  } else {
    inspirationWindow.loadFile(join(__dirname, '../renderer/index.html'), {
      hash: 'inspiration-capture'
    })
  }

  // Show window when ready (if requested)
  if (shouldShow) {
    inspirationWindow.once('ready-to-show', () => {
      inspirationWindow?.show()
      // Fade in the window
      inspirationWindow?.setOpacity(1)
      inspirationWindow?.focus()
    })
  }

  // Handle window closed
  inspirationWindow.on('closed', () => {
    inspirationWindow = null
  })

  // Hide window when it loses focus
  inspirationWindow.on('blur', () => {
    if (inspirationWindow) {
      inspirationWindow.setOpacity(0)
      setTimeout(() => {
        if (inspirationWindow && !inspirationWindow.isDestroyed()) {
          inspirationWindow.hide()
        }
      }, 100)
    }
  })
}

// Show inspiration capture window
function createInspirationWindow(): void {
  preCreateInspirationWindow(true) // Create and show if needed
}

// Global shortcut management functions
function handleGlobalShortcut(action: string) {
  console.log('Global shortcut triggered:', action)

  switch (action) {
    case 'inbox-quick-capture':
      // Create and show inspiration capture window
      createInspirationWindow()
      break
    case 'inbox-navigate':
      // Show main window and navigate to inbox
      if (mainWindow) {
        if (mainWindow.isMinimized()) {
          mainWindow.restore()
        }
        mainWindow.show()
        mainWindow.focus()
        mainWindow.webContents.send('global-navigate', '/inbox')
      }
      break
    default:
      console.log('Unknown global shortcut action:', action)
  }
}

export function registerGlobalShortcut(accelerator: string, action: string): boolean {
  try {
    // Unregister existing shortcut if any
    if (registeredShortcuts.has(accelerator)) {
      globalShortcut.unregister(accelerator)
    }

    // Register new shortcut
    const ret = globalShortcut.register(accelerator, () => {
      handleGlobalShortcut(action)
    })

    if (ret) {
      registeredShortcuts.set(accelerator, action)
      console.log(`Global shortcut registered: ${accelerator} -> ${action}`)
    } else {
      console.log(`Failed to register global shortcut: ${accelerator}`)
    }

    return ret
  } catch (error) {
    console.error('Error registering global shortcut:', error)
    return false
  }
}

export function unregisterGlobalShortcut(accelerator: string): void {
  try {
    globalShortcut.unregister(accelerator)
    registeredShortcuts.delete(accelerator)
    console.log(`Global shortcut unregistered: ${accelerator}`)
  } catch (error) {
    console.error('Error unregistering global shortcut:', error)
  }
}

export function unregisterAllGlobalShortcuts(): void {
  try {
    globalShortcut.unregisterAll()
    registeredShortcuts.clear()
    console.log('All global shortcuts unregistered')
  } catch (error) {
    console.error('Error unregistering all global shortcuts:', error)
  }
}

function createWindow(): void {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 1920,
    height: 1080,
    frame: false,
    show: false,
    autoHideMenuBar: true,
    icon: icon, // 为所有平台设置图标
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      // 开发模式下允许访问外部资源
      webSecurity: !is.dev,
      allowRunningInsecureContent: is.dev,
      // 允许data: URI字体
      additionalArguments: ['--disable-web-security', '--allow-running-insecure-content']
    }
  })

  mainWindow.on('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.maximize()
      mainWindow.show()
    }
  })

  // Handle window close event with custom modal dialog and remembered choice
  mainWindow.on('close', async (event) => {
    if (!mainWindow) return

    // Allow direct close when explicitly quitting
    if ((mainWindow as any).forceQuit) {
      return
    }

    // Intercept default close
    event.preventDefault()

    // 1) If user has a remembered choice, apply it; otherwise show custom modal
    let action: ExitAction = 'cancel'
    let rememberChoice = false

    const pref = await loadExitPreference()
    if (pref?.remember && (pref.action === 'minimize' || pref.action === 'exit')) {
      action = pref.action
      rememberChoice = true
    } else {
      const result = await showExitConfirmDialog(mainWindow)
      action = result.action
      rememberChoice = result.remember
      if (rememberChoice && (action === 'minimize' || action === 'exit')) {
        await saveExitPreference({ remember: true, action })
      }
    }

    // 2) Execute action
    if (action === 'minimize') {
      // Minimize to tray: hide window and remove from taskbar
      try {
        if (mainWindow.isMinimized()) {
          mainWindow.restore()
        }
        if (typeof mainWindow.setSkipTaskbar === 'function') {
          mainWindow.setSkipTaskbar(true)
        }
      } catch (e) {
        console.warn('⚠️ setSkipTaskbar not supported or failed:', (e as any)?.message)
      }

      mainWindow.hide()

      // Keep tray state in sync if available
      try { (trayService as any)?.onWindowStateChange?.() } catch {}

    } else if (action === 'exit') {
      // Exit the app cleanly
      ;(mainWindow as any).forceQuit = true
      app.quit()

      // Fallback: ensure process exits in case quit hangs
      setTimeout(() => {
        try { app.exit(0) } catch {}
      }, 1500)
    } else {
      // cancel -> do nothing
      return
    }
  })

  // 监听窗口状态变化，更新托盘菜单
  mainWindow.on('show', () => {
    console.log('🔼 [MainWindow] Window show event triggered')
    trayService?.onWindowStateChange()
  })

  mainWindow.on('hide', () => {
    console.log('🔽 [MainWindow] Window hide event triggered')
    console.log('🔽 [MainWindow] Stack trace:', new Error().stack?.split('\n').slice(1, 4).join('\n'))
    trayService?.onWindowStateChange()
  })

  mainWindow.on('minimize', () => {
    trayService?.onWindowStateChange()
  })

  mainWindow.on('restore', () => {
    trayService?.onWindowStateChange()
  })

  // Handle window closed event
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    // For testing vditor protocol, load test page first
    if (process.env.TEST_VDITOR_PROTOCOL) {
      mainWindow.loadFile(join(__dirname, '../../test-vditor-protocol.html'))
    } else {
      mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
    }
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  try {
    console.log('🚀 [main] App is ready, starting initialization...')

    // Set app user model id for windows
    electronApp.setAppUserModelId('com.electron')

  // Register protocol to serve vditor static files
  protocol.handle('vditor', async (request) => {
    const url = request.url.substring(9) // Remove 'vditor://' prefix
    // Remove leading 'dist/' if present since our resources are already in dist folder
    const cleanUrl = url.startsWith('dist/') ? url.substring(5) : url
    const resourcePath = join(__dirname, '../../resources/dist', cleanUrl)

    try {
      const { readFile } = await import('fs/promises')
      const data = await readFile(resourcePath)

      // Determine content type based on file extension
      let contentType = 'text/plain'
      if (url.endsWith('.css')) {
        contentType = 'text/css'
      } else if (url.endsWith('.js')) {
        contentType = 'application/javascript'
      } else if (url.endsWith('.json')) {
        contentType = 'application/json'
      } else if (url.endsWith('.png')) {
        contentType = 'image/png'
      } else if (url.endsWith('.svg')) {
        contentType = 'image/svg+xml'
      }

      return new Response(data, {
        headers: { 'Content-Type': contentType }
      })
    } catch (error) {
      console.error(
        'Failed to load vditor resource:',
        resourcePath,
        'Original URL:',
        request.url,
        error
      )
      return new Response('Not Found', { status: 404 })
    }
  })

  // Register protocol to serve local assets (images, etc.)
  protocol.handle('paolife-assets', async (request) => {
    const url = request.url.substring(17) // Remove 'paolife-assets://' prefix
    const userDataPath = app.getPath('userData')
    const resourcePath = join(userDataPath, url)

    try {
      const { readFile } = await import('fs/promises')
      const data = await readFile(resourcePath)

      // Determine content type based on file extension
      let contentType = 'application/octet-stream'
      if (url.endsWith('.png')) {
        contentType = 'image/png'
      } else if (url.endsWith('.jpg') || url.endsWith('.jpeg')) {
        contentType = 'image/jpeg'
      } else if (url.endsWith('.gif')) {
        contentType = 'image/gif'
      } else if (url.endsWith('.svg')) {
        contentType = 'image/svg+xml'
      } else if (url.endsWith('.webp')) {
        contentType = 'image/webp'
      }

      return new Response(data, {
        headers: { 'Content-Type': contentType }
      })
    } catch (error) {
      console.error('Failed to load asset:', resourcePath, 'Original URL:', request.url, error)
      return new Response('Not Found', { status: 404 })
    }
  })

  console.log('🔍 [main] Protocol handlers registered, starting database initialization...')

  // 检查是否为首次启动（通过检查用户设置是否存在）
  console.log('🔍 [main] Starting first time startup check...')
  const isFirstTime = await checkIfFirstTimeStartup()
  console.log('🔍 [main] First time startup check result:', isFirstTime)

  if (isFirstTime) {
    console.log('🆕 First time startup detected - delaying database initialization until user sets workspace directory')
    // 首次启动：延迟数据库初始化，等待用户设置工作目录
    // 数据库将在用户完成首次设置后通过IPC初始化
  } else {
    console.log('🔄 Existing user detected - initializing database with saved settings')
    // 非首次启动：必须使用用户设置目录初始化数据库
    const userWorkspaceDir = await loadUserWorkspaceDirectory()
    if (!userWorkspaceDir) {
      console.error('❌ [main] Failed to load user workspace directory for existing user')
      console.log('🔧 [main] Attempting fallback initialization with default directory...')

      // 尝试使用默认目录作为fallback
      const fallbackDir = app.getPath('userData')
      console.log('🔧 [main] Using fallback directory:', fallbackDir)

      kyselyDatabaseService.setWorkspaceDirectory(fallbackDir)
      const dbResult = await kyselyDatabaseService.initialize()

      if (!dbResult.success) {
        console.error('❌ [main] Fallback initialization also failed:', dbResult.error)
        const { dialog } = await import('electron')
        await dialog.showErrorBox(
          'Configuration Error',
          'Failed to load your workspace directory settings and fallback initialization failed.\n\nPlease contact support or reinstall the application.'
        )
        app.quit()
        return
      } else {
        console.log('✅ [main] Database initialized with fallback directory')
      }
    } else {

    console.log('✅ [main] User workspace directory loaded:', userWorkspaceDir)
    // 设置工作目录并初始化数据库
    console.log('🔧 [main] Setting workspace directory in database service...')
    kyselyDatabaseService.setWorkspaceDirectory(userWorkspaceDir)
    console.log('🚀 [main] Initializing database service...')
    const dbResult = await kyselyDatabaseService.initialize()
    if (!dbResult.success) {
      console.error('❌ [main] Failed to initialize database with user workspace directory:', dbResult.error)
      const { dialog } = await import('electron')
      await dialog.showErrorBox(
        'Database Error',
        `Failed to initialize database at your workspace directory:\n${userWorkspaceDir}\n\nError: ${dbResult.error}\n\nPlease check the directory permissions and try again.`
      )
      app.quit()
      return
      } else {
        console.log('✅ [main] Database initialized successfully')
      }
    }
  }

  // Initialize file system
  const fsResult = await fileSystemService.initialize()
  if (!fsResult.success) {
    console.error('Failed to initialize file system:', fsResult.error)
    app.quit()
    return
  }

  // Initialize file watcher
  const watcherResult = await fileWatcherService.initialize()
  if (!watcherResult.success) {
    console.error('Failed to initialize file watcher:', watcherResult.error)
    // File watcher failure is not critical, continue without it
  } else {
    // Set up file system event handlers
    fileWatcherService.on('file-system-event', (event) => {
      console.log('File system event:', event)
      // TODO: Broadcast to renderer processes via IPC
    })

    fileWatcherService.on('error', (error) => {
      console.error('File watcher error:', error)
    })
  }

  // Initialize IPC handlers
  ipcHandler.initialize()

  // Set up periodic cleanup tasks
  setInterval(() => {
    fileSystemService.cleanupLocks()
  }, 60000) // Clean up locks every minute

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)

    // Add shortcuts for DevTools in all environments
    window.webContents.on('before-input-event', (event, input) => {
      // F12 shortcut
      if (input.key === 'F12') {
        window.webContents.toggleDevTools()
      }
      // Ctrl+Shift+I shortcut
      if (input.control && input.shift && input.key === 'I') {
        window.webContents.toggleDevTools()
      }
    })
  })

  // IPC test
  ipcMain.on('ping', () => console.log('pong'))

  console.log('🚀 [main] All initialization completed, creating window...')
  createWindow()

  } catch (error) {
    console.error('❌ [main] Fatal error during app initialization:', error)
    console.error('❌ [main] Stack trace:', error.stack)

    // Try to show error dialog
    try {
      const { dialog } = await import('electron')
      await dialog.showErrorBox(
        'Application Error',
        `A fatal error occurred during application startup:\n\n${error.message}\n\nPlease restart the application or contact support.`
      )
    } catch (dialogError) {
      console.error('❌ [main] Could not show error dialog:', dialogError)
    }

    app.quit()
    return
  }

  // Initialize system tray if supported
  if (TrayService.isSupported()) {
    trayService = new TrayService(mainWindow)
    trayService.createTray()
  }

  // Register default global shortcuts
  const registerDefaultGlobalShortcuts = () => {
    // Note: Global shortcuts are now managed by the shortcuts store
    // Default shortcuts will be registered when the renderer initializes
    console.log('Global shortcuts will be managed by shortcuts store')
  }

  // Register default shortcuts after window is created
  // registerDefaultGlobalShortcuts() // Disabled: Now managed by shortcuts store

  // Pre-create inspiration window for faster access
  preCreateInspirationWindow(false)

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', async () => {
  console.log('All windows closed, starting cleanup...')

  try {
    // Unregister all global shortcuts
    globalShortcut.unregisterAll()
    console.log('Global shortcuts unregistered')

    // Clean up IPC handlers
    ipcHandler.cleanup()
    console.log('IPC handlers cleaned up')

    // Close file watcher
    await fileWatcherService.close()
    console.log('File watcher closed')

    // Close database connection
    await kyselyDatabaseService.close()
    console.log('Database connection closed')

    // Destroy tray
    if (trayService) {
      trayService.destroy()
      trayService = null
    }

    // Force quit all remaining processes
    if (process.platform !== 'darwin') {
      console.log('Quitting application...')
      app.quit()

      // Force exit if app.quit() doesn't work
      setTimeout(() => {
        console.log('Force exiting...')
        process.exit(0)
      }, 2000)
    }
  } catch (error) {
    console.error('Error during cleanup:', error)
    // Force exit even if cleanup fails
    process.exit(1)
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
