import { app } from 'electron'
import * as fs from 'fs'
import * as path from 'path'

/**
 * 用户配置接口
 */
export interface UserConfig {
  username: string
  workspaceDirectory: string
  resourcePath?: string
  isFirstTime: boolean
  lastLoginTime?: string
  editorMode?: 'wysiwyg' | 'ir'
  editorTheme?: 'classic' | 'dark'
  focusMode?: boolean
  autoSave?: boolean
  autoSaveInterval?: number
  showExitConfirm?: boolean
  version: string
  createdAt: string
  updatedAt: string
}

/**
 * 配置管理器
 * 负责用户配置的持久化存储和读取
 */
export class ConfigManager {
  private configPath: string
  private config: UserConfig | null = null

  constructor() {
    this.configPath = path.join(app.getPath('userData'), 'paolife-config.json')
  }

  /**
   * 获取配置文件路径
   */
  getConfigPath(): string {
    return this.configPath
  }

  /**
   * 检查配置文件是否存在
   */
  configExists(): boolean {
    return fs.existsSync(this.configPath)
  }

  /**
   * 检查是否为首次启动
   */
  isFirstTimeStartup(): boolean {
    try {
      if (!this.configExists()) {
        console.log('🔍 [ConfigManager] No config file found - first time startup')
        return true
      }

      const config = this.loadConfig()
      if (!config) {
        console.log('🔍 [ConfigManager] Failed to load config - treating as first time startup')
        return true
      }

      // 检查配置完整性
      const hasValidWorkspace = config.workspaceDirectory && config.workspaceDirectory.length > 0
      const isNotFirstTime = config.isFirstTime === false
      const isFirstTime = !hasValidWorkspace || !isNotFirstTime

      console.log('🔍 [ConfigManager] Config check details:')
      console.log('  - hasValidWorkspace:', hasValidWorkspace, '(', config.workspaceDirectory, ')')
      console.log('  - isNotFirstTime:', isNotFirstTime, '(isFirstTime =', config.isFirstTime, ')')
      console.log('  - Final isFirstTime result:', isFirstTime)

      return isFirstTime
    } catch (error) {
      console.warn('⚠️ [ConfigManager] Error checking first time startup:', error)
      return true
    }
  }

  /**
   * 加载配置文件
   */
  loadConfig(): UserConfig | null {
    try {
      if (!this.configExists()) {
        console.log('📄 [ConfigManager] Config file does not exist')
        return null
      }

      console.log('📄 [ConfigManager] Loading config from:', this.configPath)
      const configContent = fs.readFileSync(this.configPath, 'utf8')
      console.log('📄 [ConfigManager] Raw config content:', configContent)

      const config = JSON.parse(configContent) as UserConfig
      console.log('📄 [ConfigManager] Parsed config:', JSON.stringify(config, null, 2))

      // 验证配置完整性
      if (!this.validateConfig(config)) {
        console.warn('⚠️ [ConfigManager] Invalid config format, treating as first time')
        console.warn('⚠️ [ConfigManager] Validation failed for:', config)
        return null
      }

      this.config = config
      console.log('✅ [ConfigManager] Config loaded successfully')
      console.log('📄 [ConfigManager] Workspace directory:', config.workspaceDirectory)
      console.log('📄 [ConfigManager] isFirstTime:', config.isFirstTime)
      return config
    } catch (error) {
      console.error('❌ [ConfigManager] Failed to load config:', error)
      return null
    }
  }

  /**
   * 保存配置文件
   */
  saveConfig(config: Partial<UserConfig>): boolean {
    try {
      const now = new Date().toISOString()
      
      // 如果是首次保存，创建完整配置
      if (!this.config) {
        this.config = {
          username: config.username || '',
          workspaceDirectory: config.workspaceDirectory || '',
          resourcePath: config.resourcePath,
          isFirstTime: config.isFirstTime !== undefined ? config.isFirstTime : true,
          lastLoginTime: config.lastLoginTime || now,
          editorMode: config.editorMode || 'ir',
          editorTheme: config.editorTheme || 'dark',
          focusMode: config.focusMode || false,
          autoSave: config.autoSave !== undefined ? config.autoSave : true,
          autoSaveInterval: config.autoSaveInterval || 30,
          showExitConfirm: config.showExitConfirm !== undefined ? config.showExitConfirm : true,
          version: '1.0.0',
          createdAt: now,
          updatedAt: now
        }
      } else {
        // 更新现有配置
        this.config = {
          ...this.config,
          ...config,
          updatedAt: now
        }
      }

      // 确保目录存在
      const configDir = path.dirname(this.configPath)
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true })
      }

      // 写入配置文件
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2), 'utf8')
      console.log('✅ [ConfigManager] Config saved successfully to:', this.configPath)
      return true
    } catch (error) {
      console.error('❌ [ConfigManager] Failed to save config:', error)
      return false
    }
  }

  /**
   * 获取工作目录
   */
  getWorkspaceDirectory(): string | null {
    const config = this.config || this.loadConfig()
    if (!config || !config.workspaceDirectory) {
      console.log('📄 [ConfigManager] No workspace directory found in config')
      return null
    }

    console.log('📄 [ConfigManager] Workspace directory from config:', config.workspaceDirectory)
    return config.workspaceDirectory
  }

  /**
   * 设置工作目录
   */
  setWorkspaceDirectory(directory: string): boolean {
    console.log('📄 [ConfigManager] Setting workspace directory:', directory)
    return this.saveConfig({ 
      workspaceDirectory: directory,
      isFirstTime: false,
      lastLoginTime: new Date().toISOString()
    })
  }

  /**
   * 完成首次设置
   */
  completeFirstTimeSetup(username: string, workspaceDirectory: string): boolean {
    console.log('🎉 [ConfigManager] Completing first time setup')
    console.log('👤 [ConfigManager] Username:', username)
    console.log('📁 [ConfigManager] Workspace directory:', workspaceDirectory)
    
    return this.saveConfig({
      username: username.trim(),
      workspaceDirectory,
      isFirstTime: false,
      lastLoginTime: new Date().toISOString()
    })
  }

  /**
   * 获取当前配置
   */
  getCurrentConfig(): UserConfig | null {
    return this.config || this.loadConfig()
  }

  /**
   * 重置配置（用于测试或重新设置）
   */
  resetConfig(): boolean {
    try {
      if (this.configExists()) {
        fs.unlinkSync(this.configPath)
        console.log('🗑️ [ConfigManager] Config file deleted')
      }
      this.config = null
      return true
    } catch (error) {
      console.error('❌ [ConfigManager] Failed to reset config:', error)
      return false
    }
  }

  /**
   * 验证配置格式
   */
  private validateConfig(config: any): config is UserConfig {
    return (
      typeof config === 'object' &&
      config !== null &&
      typeof config.workspaceDirectory === 'string' &&
      typeof config.isFirstTime === 'boolean'
    )
  }

  /**
   * 从localStorage迁移配置（兼容性方法）
   */
  migrateFromLocalStorage(): boolean {
    try {
      console.log('🔄 [ConfigManager] Attempting to migrate from localStorage...')
      
      // 尝试从localStorage文件中提取配置
      const userDataPath = app.getPath('userData')
      const localStoragePath = path.join(userDataPath, 'Local Storage', 'leveldb')
      
      if (!fs.existsSync(localStoragePath)) {
        console.log('📄 [ConfigManager] No localStorage directory found')
        return false
      }

      const files = fs.readdirSync(localStoragePath)
      for (const file of files) {
        try {
          const filePath = path.join(localStoragePath, file)
          const content = fs.readFileSync(filePath, 'utf8')
          
          // 尝试提取用户设置
          if (content.includes('user-settings')) {
            const extractedConfig = this.extractConfigFromLocalStorage(content)
            if (extractedConfig) {
              console.log('✅ [ConfigManager] Successfully migrated from localStorage')
              return this.saveConfig(extractedConfig)
            }
          }
        } catch (error) {
          continue
        }
      }

      console.log('📄 [ConfigManager] No valid config found in localStorage')
      return false
    } catch (error) {
      console.warn('⚠️ [ConfigManager] Migration from localStorage failed:', error)
      return false
    }
  }

  /**
   * 从localStorage内容中提取配置
   */
  private extractConfigFromLocalStorage(content: string): Partial<UserConfig> | null {
    try {
      // 处理特殊格式的JSON（字符间有空格）
      const cleanContent = content.replace(/\s+/g, ' ').trim()
      
      // 尝试多种提取方式
      const patterns = [
        /"workspaceDirectory"\s*:\s*"([^"]+)"/,
        /'workspaceDirectory'\s*:\s*'([^']+)'/,
        /workspaceDirectory['":\s]+([^'",\s}]+)/
      ]

      for (const pattern of patterns) {
        const match = cleanContent.match(pattern)
        if (match && match[1]) {
          const workspaceDirectory = match[1].replace(/\\\\/g, '\\')
          console.log('📄 [ConfigManager] Extracted workspace directory:', workspaceDirectory)
          
          return {
            workspaceDirectory,
            isFirstTime: false,
            lastLoginTime: new Date().toISOString()
          }
        }
      }

      return null
    } catch (error) {
      console.warn('⚠️ [ConfigManager] Failed to extract config from localStorage:', error)
      return null
    }
  }
}

// 导出单例实例
export const configManager = new ConfigManager()
