// Final script to complete IPC migration from databaseService to kyselyDatabaseService
const fs = require('fs')
const path = require('path')

const ipcFilePath = path.join(__dirname, 'ipc.ts')

// Read the file
let content = fs.readFileSync(ipcFilePath, 'utf-8')

// Replace remaining databaseService references
const replacements = [
  // Document link service references
  {
    from: /const linkService = databaseService\.getDocumentLinkService\(\)/g,
    to: 'const linkService = kyselyDatabaseService.getDocumentLinkService()'
  },
  // Database client references
  {
    from: /const client = databaseService\.getClient\(\)/g,
    to: 'const client = kyselyDatabaseService.db'
  },
  // Config references
  {
    from: /databaseService\.config/g,
    to: 'kyselyDatabaseService.config'
  },
  // Prisma references
  {
    from: /databaseService\.prisma/g,
    to: 'kyselyDatabaseService.db'
  },
  // Final import reference
  {
    from: /const { default: databaseService } = await import\('\.\/database'\)/g,
    to: 'const { default: kyselyDatabaseService } = await import(\'./kyselyDatabase\')'
  },
  {
    from: /await databaseService\.close\(\)/g,
    to: 'await kyselyDatabaseService.close()'
  }
]

// Apply replacements
replacements.forEach(replacement => {
  content = content.replace(replacement.from, replacement.to)
})

// Write the updated content back
fs.writeFileSync(ipcFilePath, content, 'utf-8')

console.log('Final IPC migration completed!')
console.log('Note: Some complex Prisma queries may need manual adjustment.')
