import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '../ui/dialog'
import { Button } from '../ui/button'
import { Checkbox } from '../ui/checkbox'
import { useLanguage } from '../../contexts/LanguageContext'

interface ExitConfirmDialogProps {
  isOpen: boolean
  onConfirm: () => void
  onCancel: () => void
  onMinimizeToTray?: () => void
}

export function ExitConfirmDialog({ isOpen, onConfirm, onCancel, onMinimizeToTray }: ExitConfirmDialogProps) {
  const { t } = useLanguage()
  const [dontShowAgain, setDontShowAgain] = useState(false)

  // Load saved preference
  useEffect(() => {
    const saved = localStorage.getItem('paolife-exit-confirm-disabled')
    if (saved === 'true') {
      // If user previously chose not to show again, auto-confirm
      if (isOpen) {
        onConfirm()
      }
    }
  }, [isOpen, onConfirm])

  const handleConfirm = () => {
    if (dontShowAgain) {
      localStorage.setItem('paolife-exit-confirm-disabled', 'true')
    }
    onConfirm()
  }

  const handleCancel = () => {
    onCancel()
  }

  // Don't render if user has disabled confirmations
  const isDisabled = localStorage.getItem('paolife-exit-confirm-disabled') === 'true'
  if (isDisabled) {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onCancel()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <svg
              className="w-5 h-5 text-amber-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            {t('components.dialog.exitConfirm.title')}
          </DialogTitle>
          <DialogDescription>
            {t('components.dialog.exitConfirm.message')}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 space-y-4">
          {onMinimizeToTray && (
            <div className="p-3 bg-muted/50 rounded-lg">
              <p className="text-xs text-muted-foreground text-center">
                {t('components.dialog.exitConfirm.trayHint')}
              </p>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Checkbox
              id="dont-show-again"
              checked={dontShowAgain}
              onCheckedChange={(checked) => setDontShowAgain(checked as boolean)}
            />
            <label
              htmlFor="dont-show-again"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {t('components.dialog.exitConfirm.dontShowAgain')}
            </label>
          </div>
        </div>

        <DialogFooter>
          <div className="flex gap-2 w-full">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex-1"
            >
              {t('common.cancel')}
            </Button>
            {onMinimizeToTray && (
              <Button
                variant="secondary"
                onClick={() => {
                  console.log('🔽 [ExitConfirmDialog] Minimize to tray button clicked')
                  if (dontShowAgain) {
                    console.log('🔽 [ExitConfirmDialog] Saving "don\'t show again" preference')
                    localStorage.setItem('paolife-exit-confirm-disabled', 'true')
                  }
                  console.log('🔽 [ExitConfirmDialog] Calling onMinimizeToTray callback')
                  onMinimizeToTray()
                }}
                className="flex-1"
              >
                {t('components.dialog.exitConfirm.minimizeToTray')}
              </Button>
            )}
            <Button
              variant="destructive"
              onClick={handleConfirm}
              className="flex-1"
            >
              {t('components.dialog.exitConfirm.confirm')}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Hook to manage exit confirmation
export function useExitConfirmation() {
  const [showExitDialog, setShowExitDialog] = useState(false)

  useEffect(() => {
    const handleCloseRequest = () => {
      console.log('🔔 [ExitConfirmDialog] Received app-close-requested from main process')
      const isDisabled = localStorage.getItem('paolife-exit-confirm-disabled') === 'true'
      console.log('🔔 [ExitConfirmDialog] Exit confirmation disabled:', isDisabled)

      if (isDisabled) {
        // If confirmations are disabled, quit immediately
        console.log('🔔 [ExitConfirmDialog] Auto-quitting due to disabled confirmation')
        if (window.electronAPI?.window?.forceQuit) {
          window.electronAPI.window.forceQuit()
        }
      } else {
        // Show confirmation dialog
        console.log('🔔 [ExitConfirmDialog] Showing exit confirmation dialog')
        setShowExitDialog(true)
      }
    }

    // Listen for close request from main process
    if (window.electronAPI?.ipcRenderer) {
      console.log('🔔 [ExitConfirmDialog] Setting up app-close-requested listener')
      window.electronAPI.ipcRenderer.on('app-close-requested', handleCloseRequest)
    }

    return () => {
      if (window.electronAPI?.ipcRenderer) {
        window.electronAPI.ipcRenderer.removeAllListeners('app-close-requested')
      }
    }
  }, [])

  const handleConfirmExit = async () => {
    setShowExitDialog(false)
    if (window.electronAPI?.window?.confirmExit) {
      await window.electronAPI.window.confirmExit('exit')
    }
  }

  const handleCancelExit = async () => {
    setShowExitDialog(false)
    if (window.electronAPI?.window?.confirmExit) {
      await window.electronAPI.window.confirmExit('cancel')
    }
  }

  const handleMinimizeToTray = async () => {
    console.log('🔽 [ExitConfirmDialog] handleMinimizeToTray called')
    setShowExitDialog(false)
    console.log('🔽 [ExitConfirmDialog] Exit dialog closed')

    if (window.electronAPI?.window?.confirmExit) {
      console.log('🔽 [ExitConfirmDialog] Calling confirmExit with "minimize" action')
      try {
        const result = await window.electronAPI.window.confirmExit('minimize')
        console.log('🔽 [ExitConfirmDialog] confirmExit result:', result)
      } catch (error) {
        console.error('❌ [ExitConfirmDialog] Error calling confirmExit:', error)
      }
    } else {
      console.error('❌ [ExitConfirmDialog] window.electronAPI.window.confirmExit not available')
    }
  }

  return {
    showExitDialog,
    handleConfirmExit,
    handleCancelExit,
    handleMinimizeToTray
  }
}
