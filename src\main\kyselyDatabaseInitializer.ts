// Kysely Database Initializer
// Replaces the Prisma-based databaseInitializer.ts

import { createKyselyClient, closeKyselyClient } from './kyselyClient'
import { KyselyMigrationManager } from './kyselyMigrationManager'
import { app } from 'electron'
import path from 'path'

export class KyselyDatabaseInitializer {
  private migrationManager: KyselyMigrationManager | null = null

  private getMigrationManager(): KyselyMigrationManager {
    if (!this.migrationManager) {
      this.migrationManager = new KyselyMigrationManager()
    }
    return this.migrationManager
  }

  /**
   * Initialize the database system
   */
  async initialize(): Promise<void> {
    console.log('🚀 Starting Kysely database initialization...')

    try {
      // Create database client
      const db = createKyselyClient()
      console.log('✅ Kysely client created successfully')

      // Check if database is already initialized
      const migrationManager = this.getMigrationManager()
      const isInitialized = await migrationManager.isDatabaseInitialized()

      if (!isInitialized) {
        console.log('📦 Database not initialized, running setup...')

        // Initialize database schema
        await migrationManager.initializeDatabase()

        // Create default data
        await migrationManager.createDefaultData()

        console.log('✅ Database initialization completed')
      } else {
        console.log('✅ Database already initialized')
      }

      // Verify database integrity
      const isValid = await migrationManager.verifyDatabaseIntegrity()
      if (!isValid) {
        throw new Error('Database integrity check failed')
      }

      // Log migration status
      const status = await migrationManager.getMigrationStatus()
      console.log('📊 Database Status:', {
        initialized: status.initialized,
        tables: status.tableCount,
        totalRecords: Object.values(status.recordCounts).reduce((sum, count) => sum + count, 0)
      })

      console.log('🎉 Kysely database system ready!')

    } catch (error) {
      console.error('❌ Database initialization failed:', error)
      throw error
    }
  }

  /**
   * Migrate from existing Prisma database
   */
  async migrateFromPrisma(): Promise<void> {
    console.log('🔄 Starting migration from Prisma...')

    try {
      // Determine Prisma database path
      const isDevelopment = process.env.NODE_ENV === 'development' || !app.isPackaged
      const prismaDbPath = isDevelopment 
        ? path.join(process.cwd(), 'prisma', 'dev.db')
        : path.join(app.getPath('userData'), 'prisma', 'dev.db')

      const migrationManager = this.getMigrationManager()
      await migrationManager.migrateFromPrisma(prismaDbPath)
      console.log('✅ Prisma migration completed')

    } catch (error) {
      console.error('❌ Prisma migration failed:', error)
      throw error
    }
  }

  /**
   * Get current database status
   */
  async getStatus(): Promise<{
    initialized: boolean
    tableCount: number
    recordCounts: Record<string, number>
    integrity: boolean
  }> {
    try {
      const migrationManager = this.getMigrationManager()
      const status = await migrationManager.getMigrationStatus()
      const integrity = await migrationManager.verifyDatabaseIntegrity()

      return {
        ...status,
        integrity
      }
    } catch (error) {
      console.error('Failed to get database status:', error)
      return {
        initialized: false,
        tableCount: 0,
        recordCounts: {},
        integrity: false
      }
    }
  }

  /**
   * Cleanup database connections
   */
  async cleanup(): Promise<void> {
    try {
      await closeKyselyClient()
      console.log('✅ Database connections closed')
    } catch (error) {
      console.error('❌ Database cleanup failed:', error)
    }
  }

  /**
   * Reset database (for development/testing)
   */
  async reset(): Promise<void> {
    console.log('🔄 Resetting database...')

    try {
      // This would drop all tables and recreate them
      // For safety, we'll just reinitialize
      const migrationManager = this.getMigrationManager()
      await migrationManager.initializeDatabase()
      await migrationManager.createDefaultData()
      
      console.log('✅ Database reset completed')
    } catch (error) {
      console.error('❌ Database reset failed:', error)
      throw error
    }
  }

  /**
   * Backup database
   */
  async backup(backupPath: string): Promise<void> {
    console.log(`📦 Creating database backup at: ${backupPath}`)

    try {
      // For SQLite, we can simply copy the database file
      const isDevelopment = process.env.NODE_ENV === 'development' || !app.isPackaged
      const currentDbPath = isDevelopment 
        ? path.join(process.cwd(), 'prisma', 'dev.db')
        : path.join(app.getPath('userData'), 'paolife.db')

      const fs = require('fs')
      fs.copyFileSync(currentDbPath, backupPath)
      
      console.log('✅ Database backup completed')
    } catch (error) {
      console.error('❌ Database backup failed:', error)
      throw error
    }
  }

  /**
   * Restore database from backup
   */
  async restore(backupPath: string): Promise<void> {
    console.log(`🔄 Restoring database from: ${backupPath}`)

    try {
      const isDevelopment = process.env.NODE_ENV === 'development' || !app.isPackaged
      const currentDbPath = isDevelopment 
        ? path.join(process.cwd(), 'prisma', 'dev.db')
        : path.join(app.getPath('userData'), 'paolife.db')

      // Close current connections
      await closeKyselyClient()

      // Copy backup file
      const fs = require('fs')
      fs.copyFileSync(backupPath, currentDbPath)

      // Reinitialize
      createKyselyClient()
      
      console.log('✅ Database restore completed')
    } catch (error) {
      console.error('❌ Database restore failed:', error)
      throw error
    }
  }
}

// Singleton instance (lazy initialization)
let kyselyDatabaseInitializer: KyselyDatabaseInitializer | null = null

function getKyselyDatabaseInitializer(): KyselyDatabaseInitializer {
  if (!kyselyDatabaseInitializer) {
    kyselyDatabaseInitializer = new KyselyDatabaseInitializer()
  }
  return kyselyDatabaseInitializer
}

// Export initialization function for backward compatibility
export async function initializeKyselyDatabase(): Promise<void> {
  await getKyselyDatabaseInitializer().initialize()
}

// Export migration function
export async function migrateFromPrismaToKysely(): Promise<void> {
  await getKyselyDatabaseInitializer().migrateFromPrisma()
}

// Export the getter for the singleton
export { getKyselyDatabaseInitializer as kyselyDatabaseInitializer }
