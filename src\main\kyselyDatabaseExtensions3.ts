// Kysely Database Service Extensions Part 3
// Task, Review, Checklist, and ResourceLink operations

import type { Kysely } from 'kysely'
import type { DatabaseResult } from '../shared/types'
import type { Database } from '../shared/database-types'
import { 
  serialize<PERSON>son,
  deserialize<PERSON>son,
  booleanToInt,
  intToBoolean,
  getCurrentTimestamp,
  generateId
} from './kyselyClient'

export class KyselyDatabaseExtensions3 {
  constructor(private db: Kysely<Database>) {}

  /**
   * REVIEW OPERATIONS
   */

  async createReview(data: {
    type: string
    period: string
    title?: string
    content: any
    status?: string
    templateId?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const review = await this.db.insertInto('Review')
        .values({
          id: generateId(),
          type: data.type,
          period: data.period,
          title: data.title || null,
          content: serializeJson(data.content),
          status: data.status || 'draft',
          templateId: data.templateId || null,
          completedAt: null,
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...review,
          content: deserializeJson(review.content)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create review'
      }
    }
  }

  async getReviews(filters: {
    type?: string
    period?: string
    status?: string
    limit?: number
  } = {}): Promise<DatabaseResult<any[]>> {
    try {
      let query = this.db.selectFrom('Review')
        .selectAll()
        .orderBy('createdAt', 'desc')

      if (filters.type) {
        query = query.where('type', '=', filters.type)
      }
      if (filters.period) {
        query = query.where('period', '=', filters.period)
      }
      if (filters.status) {
        query = query.where('status', '=', filters.status)
      }
      if (filters.limit) {
        query = query.limit(filters.limit)
      }

      const reviews = await query.execute()

      return {
        success: true,
        data: reviews.map(review => ({
          ...review,
          content: deserializeJson(review.content)
        }))
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get reviews'
      }
    }
  }

  async getReviewById(id: string): Promise<DatabaseResult<any>> {
    try {
      const review = await this.db.selectFrom('Review')
        .selectAll()
        .where('id', '=', id)
        .executeTakeFirst()

      if (!review) {
        throw new Error('Review not found')
      }

      return {
        success: true,
        data: {
          ...review,
          content: deserializeJson(review.content)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get review by id'
      }
    }
  }

  async updateReview(id: string, data: {
    title?: string
    content?: any
    status?: string
    completedAt?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const updateData: any = {
        updatedAt: getCurrentTimestamp()
      }

      if (data.title !== undefined) updateData.title = data.title
      if (data.content !== undefined) updateData.content = serializeJson(data.content)
      if (data.status !== undefined) updateData.status = data.status
      if (data.completedAt !== undefined) updateData.completedAt = data.completedAt

      const review = await this.db.updateTable('Review')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...review,
          content: deserializeJson(review.content)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update review'
      }
    }
  }

  async deleteReview(id: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('Review')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete review'
      }
    }
  }

  /**
   * REVIEW TEMPLATE OPERATIONS
   */

  async createReviewTemplate(data: {
    name: string
    description?: string
    type: string
    structure: any
    isDefault?: boolean
  }): Promise<DatabaseResult<any>> {
    try {
      const template = await this.db.insertInto('ReviewTemplate')
        .values({
          id: generateId(),
          name: data.name,
          description: data.description || null,
          type: data.type,
          structure: serializeJson(data.structure),
          isDefault: booleanToInt(data.isDefault || false),
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...template,
          structure: deserializeJson(template.structure),
          isDefault: intToBoolean(template.isDefault)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create review template'
      }
    }
  }

  async getReviewTemplates(type?: string): Promise<DatabaseResult<any[]>> {
    try {
      let query = this.db.selectFrom('ReviewTemplate')
        .selectAll()
        .orderBy('createdAt', 'desc')

      if (type) {
        query = query.where('type', '=', type)
      }

      const templates = await query.execute()

      return {
        success: true,
        data: templates.map(template => ({
          ...template,
          structure: deserializeJson(template.structure),
          isDefault: intToBoolean(template.isDefault)
        }))
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get review templates'
      }
    }
  }

  async getReviewTemplateById(id: string): Promise<DatabaseResult<any>> {
    try {
      const template = await this.db.selectFrom('ReviewTemplate')
        .selectAll()
        .where('id', '=', id)
        .executeTakeFirst()

      if (!template) {
        throw new Error('Review template not found')
      }

      return {
        success: true,
        data: {
          ...template,
          structure: deserializeJson(template.structure),
          isDefault: intToBoolean(template.isDefault)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get review template by id'
      }
    }
  }

  async updateReviewTemplate(id: string, data: {
    name?: string
    description?: string
    structure?: any
    isDefault?: boolean
  }): Promise<DatabaseResult<any>> {
    try {
      const updateData: any = {
        updatedAt: getCurrentTimestamp()
      }

      if (data.name !== undefined) updateData.name = data.name
      if (data.description !== undefined) updateData.description = data.description
      if (data.structure !== undefined) updateData.structure = serializeJson(data.structure)
      if (data.isDefault !== undefined) updateData.isDefault = booleanToInt(data.isDefault)

      const template = await this.db.updateTable('ReviewTemplate')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...template,
          structure: deserializeJson(template.structure),
          isDefault: intToBoolean(template.isDefault)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update review template'
      }
    }
  }

  async deleteReviewTemplate(id: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('ReviewTemplate')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete review template'
      }
    }
  }

  /**
   * CHECKLIST OPERATIONS
   */

  async createChecklist(data: {
    name: string
    template: any
    areaId: string
  }): Promise<DatabaseResult<any>> {
    try {
      const checklist = await this.db.insertInto('Checklist')
        .values({
          id: generateId(),
          name: data.name,
          template: serializeJson(data.template),
          areaId: data.areaId,
          createdAt: getCurrentTimestamp()
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...checklist,
          template: deserializeJson(checklist.template)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create checklist'
      }
    }
  }

  async getChecklists(areaId?: string): Promise<DatabaseResult<any[]>> {
    try {
      let query = this.db.selectFrom('Checklist')
        .selectAll()
        .orderBy('createdAt', 'desc')

      if (areaId) {
        query = query.where('areaId', '=', areaId)
      }

      const checklists = await query.execute()

      return {
        success: true,
        data: checklists.map(checklist => ({
          ...checklist,
          template: deserializeJson(checklist.template)
        }))
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get checklists'
      }
    }
  }

  async updateChecklist(id: string, data: {
    name?: string
    template?: any
  }): Promise<DatabaseResult<any>> {
    try {
      const updateData: any = {}

      if (data.name !== undefined) updateData.name = data.name
      if (data.template !== undefined) updateData.template = serializeJson(data.template)

      const checklist = await this.db.updateTable('Checklist')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...checklist,
          template: deserializeJson(checklist.template)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update checklist'
      }
    }
  }

  async deleteChecklist(id: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('Checklist')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete checklist'
      }
    }
  }

  /**
   * CHECKLIST INSTANCE OPERATIONS
   */

  async createChecklistInstance(data: {
    status: any
    checklistId: string
  }): Promise<DatabaseResult<any>> {
    try {
      const instance = await this.db.insertInto('ChecklistInstance')
        .values({
          id: generateId(),
          status: serializeJson(data.status),
          checklistId: data.checklistId,
          completedAt: null,
          createdAt: getCurrentTimestamp()
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...instance,
          status: deserializeJson(instance.status)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create checklist instance'
      }
    }
  }

  async getChecklistInstances(areaId?: string): Promise<DatabaseResult<any[]>> {
    try {
      let query = this.db.selectFrom('ChecklistInstance')
        .selectAll()
        .orderBy('createdAt', 'desc')

      if (areaId) {
        // Join with Checklist to filter by areaId
        query = this.db.selectFrom('ChecklistInstance')
          .innerJoin('Checklist', 'Checklist.id', 'ChecklistInstance.checklistId')
          .selectAll('ChecklistInstance')
          .where('Checklist.areaId', '=', areaId)
          .orderBy('ChecklistInstance.createdAt', 'desc')
      }

      const instances = await query.execute()

      return {
        success: true,
        data: instances.map(instance => ({
          ...instance,
          status: deserializeJson(instance.status)
        }))
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get checklist instances'
      }
    }
  }

  async updateChecklistInstance(id: string, data: {
    status?: any
    completedAt?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const updateData: any = {}

      if (data.status !== undefined) updateData.status = serializeJson(data.status)
      if (data.completedAt !== undefined) updateData.completedAt = data.completedAt

      const instance = await this.db.updateTable('ChecklistInstance')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...instance,
          status: deserializeJson(instance.status)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update checklist instance'
      }
    }
  }

  async deleteChecklistInstance(id: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('ChecklistInstance')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete checklist instance'
      }
    }
  }

  /**
   * RESOURCE LINK OPERATIONS
   */

  async getProjectResources(projectId: string): Promise<DatabaseResult<any[]>> {
    try {
      const resources = await this.db.selectFrom('ResourceLink')
        .selectAll()
        .where('projectId', '=', projectId)
        .execute()

      return {
        success: true,
        data: resources
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get project resources'
      }
    }
  }

  async linkResourceToProject(data: {
    resourcePath: string
    title?: string
    projectId: string
  }): Promise<DatabaseResult<any>> {
    try {
      const resource = await this.db.insertInto('ResourceLink')
        .values({
          id: generateId(),
          resourcePath: data.resourcePath,
          title: data.title || null,
          projectId: data.projectId,
          areaId: null
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: resource
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to link resource to project'
      }
    }
  }

  async unlinkResourceFromProject(resourceId: string, projectId: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('ResourceLink')
        .where('id', '=', resourceId)
        .where('projectId', '=', projectId)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to unlink resource from project'
      }
    }
  }

  async getAreaResources(areaId: string): Promise<DatabaseResult<any[]>> {
    try {
      const resources = await this.db.selectFrom('ResourceLink')
        .selectAll()
        .where('areaId', '=', areaId)
        .execute()

      return {
        success: true,
        data: resources
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get area resources'
      }
    }
  }

  async getResourceReferences(resourcePath: string): Promise<DatabaseResult<{
    projects: any[]
    areas: any[]
  }>> {
    try {
      const [projectResources, areaResources] = await Promise.all([
        this.db.selectFrom('ResourceLink')
          .innerJoin('Project', 'Project.id', 'ResourceLink.projectId')
          .select(['Project.id', 'Project.name', 'ResourceLink.title'])
          .where('ResourceLink.resourcePath', '=', resourcePath)
          .where('ResourceLink.projectId', 'is not', null)
          .execute(),
        this.db.selectFrom('ResourceLink')
          .innerJoin('Area', 'Area.id', 'ResourceLink.areaId')
          .select(['Area.id', 'Area.name', 'ResourceLink.title'])
          .where('ResourceLink.resourcePath', '=', resourcePath)
          .where('ResourceLink.areaId', 'is not', null)
          .execute()
      ])

      return {
        success: true,
        data: {
          projects: projectResources,
          areas: areaResources
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get resource references'
      }
    }
  }

  async unlinkResourceFromArea(resourceId: string, areaId: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('ResourceLink')
        .where('id', '=', resourceId)
        .where('areaId', '=', areaId)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to unlink resource from area'
      }
    }
  }
}
