// Kysely Database Service Extensions
// Additional methods for the KyselyDatabaseService

import type { Kysely } from 'kysely'
import type { DatabaseResult } from '../shared/types'
import type { Database } from '../shared/database-types'
import {
  serialize<PERSON><PERSON>,
  deserialize<PERSON>son,
  booleanToInt,
  intToBoolean,
  getCurrentTimestamp,
  generateId,
  withTransaction
} from './kyselyClient'

export class KyselyDatabaseExtensions {
  constructor(private db: Kysely<Database>) {}

  /**
   * PROJECT KPI OPERATIONS
   */

  async createProjectKPI(data: {
    name: string
    value: string
    target?: string
    unit?: string
    frequency?: string
    direction?: string
    projectId: string
  }): Promise<DatabaseResult<any>> {
    try {
      const kpi = await this.db.insertInto('ProjectKPI')
        .values({
          id: generateId(),
          name: data.name,
          value: data.value,
          target: data.target || null,
          unit: data.unit || null,
          frequency: data.frequency || null,
          direction: data.direction || 'increase',
          projectId: data.projectId,
          updatedAt: getCurrentTimestamp()
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: kpi
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create project KPI'
      }
    }
  }

  async getProjectKPIs(projectId: string): Promise<DatabaseResult<any[]>> {
    try {
      const kpis = await this.db.selectFrom('ProjectKPI')
        .selectAll()
        .where('projectId', '=', projectId)
        .orderBy('updatedAt', 'desc')
        .execute()

      return {
        success: true,
        data: kpis
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get project KPIs'
      }
    }
  }

  async updateProjectKPI(id: string, data: {
    name?: string
    value?: string
    target?: string
    unit?: string
    frequency?: string
    direction?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const updateData: any = {
        updatedAt: getCurrentTimestamp()
      }

      if (data.name !== undefined) updateData.name = data.name
      if (data.value !== undefined) updateData.value = data.value
      if (data.target !== undefined) updateData.target = data.target
      if (data.unit !== undefined) updateData.unit = data.unit
      if (data.frequency !== undefined) updateData.frequency = data.frequency
      if (data.direction !== undefined) updateData.direction = data.direction

      const kpi = await this.db.updateTable('ProjectKPI')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: kpi
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update project KPI'
      }
    }
  }

  async deleteProjectKPI(id: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('ProjectKPI')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete project KPI'
      }
    }
  }

  /**
   * KPI RECORD OPERATIONS
   */

  async createKPIRecord(data: {
    value: string
    note?: string
    kpiId: string
    recordedAt?: string
  }): Promise<DatabaseResult<any>> {
    try {
      // Create the KPI record
      const record = await this.db.insertInto('KPIRecord')
        .values({
          id: generateId(),
          value: data.value,
          note: data.note || null,
          kpiId: data.kpiId,
          recordedAt: data.recordedAt || getCurrentTimestamp()
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      // Update the ProjectKPI with the latest value
      await this.db.updateTable('ProjectKPI')
        .set({
          value: data.value,
          updatedAt: getCurrentTimestamp()
        })
        .where('id', '=', data.kpiId)
        .execute()

      return {
        success: true,
        data: record
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create KPI record'
      }
    }
  }

  async getKPIRecords(kpiId: string, limit?: number): Promise<DatabaseResult<any[]>> {
    try {
      let query = this.db.selectFrom('KPIRecord')
        .selectAll()
        .where('kpiId', '=', kpiId)
        .orderBy('recordedAt', 'desc')

      if (limit) {
        query = query.limit(limit)
      }

      const records = await query.execute()

      return {
        success: true,
        data: records
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get KPI records'
      }
    }
  }

  async updateKPIRecord(id: string, data: {
    value?: string
    note?: string
    recordedAt?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const updateData: any = {}

      if (data.value !== undefined) updateData.value = data.value
      if (data.note !== undefined) updateData.note = data.note
      if (data.recordedAt !== undefined) updateData.recordedAt = data.recordedAt

      const record = await this.db.updateTable('KPIRecord')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: record
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update KPI record'
      }
    }
  }

  async deleteKPIRecord(id: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('KPIRecord')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete KPI record'
      }
    }
  }

  /**
   * AREA METRIC OPERATIONS
   */

  async createAreaMetric(data: {
    name: string
    value?: string
    target?: string
    unit?: string
    trackingType?: string
    direction?: string
    areaId: string
    relatedHabits?: any[]
  }): Promise<DatabaseResult<any>> {
    try {
      const metric = await this.db.insertInto('AreaMetric')
        .values({
          id: generateId(),
          name: data.name,
          value: data.value || '',
          target: data.target || null,
          unit: data.unit || null,
          trackingType: data.trackingType || 'manual',
          direction: data.direction || 'higher_better',
          areaId: data.areaId,
          relatedHabits: data.relatedHabits ? serializeJson(data.relatedHabits) : null,
          updatedAt: getCurrentTimestamp()
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...metric,
          relatedHabits: deserializeJson(metric.relatedHabits)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create area metric'
      }
    }
  }

  async getAreaMetrics(areaId: string): Promise<DatabaseResult<any[]>> {
    try {
      const metrics = await this.db.selectFrom('AreaMetric')
        .selectAll()
        .where('areaId', '=', areaId)
        .orderBy('updatedAt', 'desc')
        .execute()

      return {
        success: true,
        data: metrics.map(metric => ({
          ...metric,
          relatedHabits: deserializeJson(metric.relatedHabits)
        }))
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get area metrics'
      }
    }
  }

  async updateAreaMetric(id: string, data: {
    name?: string
    value?: string
    target?: string
    unit?: string
    trackingType?: string
    direction?: string
    relatedHabits?: any[]
  }): Promise<DatabaseResult<any>> {
    try {
      const updateData: any = {
        updatedAt: getCurrentTimestamp()
      }

      if (data.name !== undefined) updateData.name = data.name
      if (data.value !== undefined) updateData.value = data.value
      if (data.target !== undefined) updateData.target = data.target
      if (data.unit !== undefined) updateData.unit = data.unit
      if (data.trackingType !== undefined) updateData.trackingType = data.trackingType
      if (data.direction !== undefined) updateData.direction = data.direction
      if (data.relatedHabits !== undefined) {
        updateData.relatedHabits = data.relatedHabits ? serializeJson(data.relatedHabits) : null
      }

      const metric = await this.db.updateTable('AreaMetric')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...metric,
          relatedHabits: deserializeJson(metric.relatedHabits)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update area metric'
      }
    }
  }

  async deleteAreaMetric(id: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('AreaMetric')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete area metric'
      }
    }
  }

  /**
   * AREA METRIC RECORD OPERATIONS
   */

  async createAreaMetricRecord(data: {
    value: string
    note?: string
    metricId: string
    recordedAt?: string
    source?: string
    confidence?: number
    tags?: any[]
    context?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const record = await this.db.insertInto('AreaMetricRecord')
        .values({
          id: generateId(),
          value: data.value,
          note: data.note || null,
          metricId: data.metricId,
          recordedAt: data.recordedAt || getCurrentTimestamp(),
          source: data.source || null,
          confidence: data.confidence || null,
          tags: data.tags ? serializeJson(data.tags) : null,
          context: data.context || null
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...record,
          tags: deserializeJson(record.tags)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create area metric record'
      }
    }
  }

  async getAreaMetricRecords(metricId: string, limit?: number): Promise<DatabaseResult<any[]>> {
    try {
      let query = this.db.selectFrom('AreaMetricRecord')
        .selectAll()
        .where('metricId', '=', metricId)
        .orderBy('recordedAt', 'desc')

      if (limit) {
        query = query.limit(limit)
      }

      const records = await query.execute()

      return {
        success: true,
        data: records.map(record => ({
          ...record,
          tags: deserializeJson(record.tags)
        }))
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get area metric records'
      }
    }
  }

  async updateAreaMetricRecord(id: string, data: {
    value?: string
    note?: string
    recordedAt?: string
    source?: string
    confidence?: number
    tags?: any[]
    context?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const updateData: any = {}

      if (data.value !== undefined) updateData.value = data.value
      if (data.note !== undefined) updateData.note = data.note
      if (data.recordedAt !== undefined) updateData.recordedAt = data.recordedAt
      if (data.source !== undefined) updateData.source = data.source
      if (data.confidence !== undefined) updateData.confidence = data.confidence
      if (data.context !== undefined) updateData.context = data.context
      if (data.tags !== undefined) {
        updateData.tags = data.tags ? serializeJson(data.tags) : null
      }

      const record = await this.db.updateTable('AreaMetricRecord')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...record,
          tags: deserializeJson(record.tags)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update area metric record'
      }
    }
  }

  async deleteAreaMetricRecord(id: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('AreaMetricRecord')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete area metric record'
      }
    }
  }

  /**
   * HABIT OPERATIONS
   */

  async createHabit(data: {
    name: string
    frequency: string
    target: number
    areaId: string
  }): Promise<DatabaseResult<any>> {
    try {
      const habit = await this.db.insertInto('Habit')
        .values({
          id: generateId(),
          name: data.name,
          frequency: data.frequency,
          target: data.target,
          areaId: data.areaId,
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp()
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: habit
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create habit'
      }
    }
  }

  async getHabitsByArea(areaId: string): Promise<DatabaseResult<any[]>> {
    try {
      const habits = await this.db.selectFrom('Habit')
        .selectAll()
        .where('areaId', '=', areaId)
        .orderBy('updatedAt', 'desc')
        .execute()

      return {
        success: true,
        data: habits
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get habits by area'
      }
    }
  }

  async updateHabit(id: string, data: {
    name?: string
    frequency?: string
    target?: number
  }): Promise<DatabaseResult<any>> {
    try {
      const updateData: any = {
        updatedAt: getCurrentTimestamp()
      }

      if (data.name !== undefined) updateData.name = data.name
      if (data.frequency !== undefined) updateData.frequency = data.frequency
      if (data.target !== undefined) updateData.target = data.target

      const habit = await this.db.updateTable('Habit')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: habit
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update habit'
      }
    }
  }

  async deleteHabit(id: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('Habit')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete habit'
      }
    }
  }
}
