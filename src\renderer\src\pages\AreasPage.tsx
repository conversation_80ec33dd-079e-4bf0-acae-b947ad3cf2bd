import { Outlet, useLocation } from 'react-router-dom'
import { useState, useMemo } from 'react'
import { EmptyStates, EmptyStateContainer } from '../components/shared'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select'
import { Badge } from '../components/ui/badge'
import { Plus } from 'lucide-react'
import AreaCard from '../components/features/AreaCard'
import CreateAreaDialog from '../components/features/CreateAreaDialog'
import { useConfirmDialog } from '../components/shared/ConfirmDialog'
import { useAreaStore } from '../store/areaStore'
import { useProjectStore } from '../store/projectStore'
// {{ AURA-X: Add - 导入数据库API用于保存Area. Approval: 寸止(ID:1738157400). }}
import { databaseApi } from '../lib/api'
import { useLanguage } from '../contexts/LanguageContext'
import { useUIStore } from '../store/uiStore'
import { cn } from '../lib/utils'
import type { Area } from '../../../shared/types'

export function AreasPage() {
  const location = useLocation()
  const isDetailView = location.pathname !== '/areas'

  // Always call all hooks before any conditional returns
  const { areas, addArea, updateArea, deleteArea, archiveArea } = useAreaStore()
  const { projects } = useProjectStore()
  const { t } = useLanguage()
  const { addNotification } = useUIStore()
  const { confirm, ConfirmDialog: ConfirmDialogComponent } = useConfirmDialog()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingArea, setEditingArea] = useState<Area | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'name' | 'status' | 'updated'>('updated')

  // Filter and sort areas - always calculate even for detail view
  const filteredAreas = useMemo(() => {
    let filtered = areas.filter((area) => !area.archived)

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (area) =>
          area.name.toLowerCase().includes(query) ||
          area.description?.toLowerCase().includes(query) ||
          area.standard?.toLowerCase().includes(query)
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter((area) => (area.status || 'Active') === statusFilter)
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'status':
          return (a.status || 'Active').localeCompare(b.status || 'Active')
        case 'updated':
        default:
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      }
    })

    return filtered
  }, [areas, searchQuery, statusFilter, sortBy])

  // Calculate related projects count for each area
  const getRelatedProjectsCount = (areaId: string) => {
    return projects.filter((project) => project.areaId === areaId && !project.archived).length
  }

  // Calculate habit completion rate (placeholder - would be calculated from actual habit data)
  const getHabitCompletionRate = (area: Area) => {
    if (!(area as any).habits || (area as any).habits.length === 0) return 0
    // This would be calculated from actual habit tracking data
    return Math.floor(Math.random() * 100) // Placeholder
  }

  const handleCreateArea = async (areaData: Omit<Area, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      // {{ AURA-X: Modify - 同时保存到数据库和本地状态. Approval: 寸止(ID:1738157400). }}
      // 先保存到数据库
      const result = await databaseApi.createArea({
        name: areaData.name,
        description: areaData.description,
        color: areaData.color,
        icon: areaData.icon,
        standard: areaData.standard,
        status: areaData.status,
        reviewFrequency: areaData.reviewFrequency,
        archived: areaData.archived
      })

      if (result.success) {
        // 使用数据库返回的 Area 数据（包含正确的 ID）
        const dbArea = result.data
        const newArea: Area = {
          id: dbArea.id,
          name: dbArea.name,
          description: dbArea.description,
          standard: areaData.standard,
          status: areaData.status,
          reviewFrequency: areaData.reviewFrequency,
          archived: areaData.archived,
          color: dbArea.color,
          icon: dbArea.icon,
          createdAt: new Date(dbArea.createdAt),
          updatedAt: new Date(dbArea.updatedAt)
        }
        addArea(newArea)
      } else {
        console.error('Failed to create area in database:', result.error)
        // 如果数据库保存失败，仍然保存到本地状态（离线模式）
        const newArea: Area = {
          ...areaData,
          id: `area-${Date.now()}`,
          createdAt: new Date(),
          updatedAt: new Date()
        }
        addArea(newArea)
      }
    } catch (error) {
      console.error('Error creating area:', error)
      // 错误处理：保存到本地状态
      const newArea: Area = {
        ...areaData,
        id: `area-${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      addArea(newArea)
    }
  }

  // 统一编辑入口：调用 store.updateArea（已内置持久化）
  const handleEditArea = async (areaData: Omit<Area, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingArea) {
      updateArea(editingArea.id, {
        ...areaData,
        updatedAt: new Date()
      })
      setEditingArea(null)
    }
  }

  const handleDeleteArea = async (area: Area) => {
    const confirmed = await confirm({
      title: t('pages.areas.detail.deleteConfirmTitle'),
      description: t('pages.areas.detail.deleteConfirmMessage', { name: area.name }),
      variant: 'destructive',
      confirmText: t('common.delete'),
      cancelText: t('common.cancel')
    })

    if (confirmed) {
      deleteArea(area.id)
    }
  }

  const handleArchiveArea = async (area: Area) => {
    // {{ AURA-X: Fix - 修复确认对话框的使用方式，使用Promise而不是回调. Approval: 寸止(ID:1738157400). }}
    const confirmed = await confirm({
      title: t('pages.areas.detail.archiveConfirmTitle'),
      description: t('pages.areas.detail.archiveConfirmMessage', { name: area.name }),
      variant: 'warning',
      confirmText: t('pages.areas.detail.archiveArea'),
      cancelText: t('common.cancel')
    })

    if (confirmed) {
      try {
        await archiveArea(area.id)
        // {{ AURA-X: Add - 添加归档成功通知. Approval: 寸止(ID:1738157400). }}
        addNotification({
          type: 'success',
          title: t('pages.areas.detail.archiveSuccessTitle'),
          message: t('pages.areas.detail.archiveSuccessMessage', { name: area.name })
        })
      } catch (error) {
        addNotification({
          type: 'error',
          title: t('pages.areas.detail.archiveFailedTitle'),
          message: t('pages.areas.detail.archiveFailedMessage')
        })
      }
    }
  }

  const statusOptions = [
    { value: 'all', label: t('pages.areas.allStatus') },
    { value: 'Active', label: t('pages.areas.filters.status.active') },
    { value: 'Needs Attention', label: t('pages.areas.filters.status.needsAttention') },
    { value: 'On Hold', label: t('pages.areas.filters.status.onHold') },
    { value: 'Review Required', label: t('pages.areas.filters.status.reviewRequired') }
  ]

  const sortOptions = [
    { value: 'updated', label: t('pages.areas.filters.sort.updated') },
    { value: 'name', label: t('pages.areas.filters.sort.name') },
    { value: 'status', label: t('pages.areas.filters.sort.status') }
  ]

  // Return detail view after all hooks have been called
  if (isDetailView) {
    return <Outlet />
  }

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      <div className="container mx-auto p-6 flex flex-col h-full">
        {/* 统一移除页面大标题与描述，依赖顶部面包屑与局部控件 */}
        <div className="flex-shrink-0" />

        {areas.filter((a) => !a.archived).length === 0 ? (
          <EmptyStateContainer>
            <EmptyStates.Areas onCreate={() => setIsCreateDialogOpen(true)} />
          </EmptyStateContainer>
        ) : (
          <div className="flex-1 flex flex-col min-h-0">
            {/* Filters and Controls */}
            <div className="flex-shrink-0 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between py-4">
            <div className="flex flex-col sm:flex-row gap-3 flex-1">
              <Input
                placeholder={t('pages.areas.searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="sm:max-w-xs"
              />

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="sm:w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="sm:w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <div className="flex items-center border rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-7 px-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                    />
                  </svg>
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-7 px-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 10h16M4 14h16M4 18h16"
                    />
                  </svg>
                </Button>
              </div>

              <Button
                onClick={() => setIsCreateDialogOpen(true)}
                size="sm"
                className="h-8"
              >
                <Plus className="w-4 h-4 mr-2" />
                {t('pages.areas.newArea')}
              </Button>

              <Badge variant="outline" className="text-xs">
                {filteredAreas.length} {t('nav.areas').toLowerCase()}
              </Badge>
            </div>
          </div>

          {/* Areas Grid/List - Fixed height relative to window */}
          <div className="h-[calc(100vh-280px)] overflow-hidden">
            {filteredAreas.length === 0 ? (
              <div className="flex items-center justify-center py-16">
                <EmptyState
                  title={t('components.emptyStates.noItemsFound')}
                  description={t('components.emptyStates.tryAdjustingFilters')}
                  icon={
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  }
                  size="sm"
                  className="bg-gradient-to-br from-muted/20 to-muted/40 border-dashed"
                />
              </div>
            ) : (
              <div className="h-full overflow-y-auto">
                <div
                  className={cn(
                    'grid gap-6 pb-6',
                    viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'
                  )}
                >
              {filteredAreas.map((area) => (
                <AreaCard
                  key={area.id}
                  area={area}
                  onEdit={setEditingArea}
                  onDelete={handleDeleteArea}
                  onArchive={handleArchiveArea}
                  relatedProjectsCount={getRelatedProjectsCount(area.id)}
                  habitCompletionRate={getHabitCompletionRate(area)}
                  className={viewMode === 'list' ? 'max-w-none' : ''}
                />
              ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      </div>

      {/* Create/Edit Area Dialog */}
      <CreateAreaDialog
        isOpen={isCreateDialogOpen || !!editingArea}
        onClose={() => {
          setIsCreateDialogOpen(false)
          setEditingArea(null)
        }}
        onSubmit={editingArea ? handleEditArea : handleCreateArea}
        initialData={editingArea || undefined}
      />

      {/* Confirm Dialog */}
      <ConfirmDialogComponent />
    </div>
  )
}

export default AreasPage
