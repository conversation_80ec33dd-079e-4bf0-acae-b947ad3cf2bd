class ReferenceParser {
  /**
   * 解析文档中的所有引用
   */
  parseAllReferences(content, documentPath) {
    console.log("🔍 开始解析文档引用:", documentPath);
    const references = [];
    const wikiLinks = this.parseWikiLinks(content);
    references.push(...wikiLinks);
    const projectRefs = this.parseProjectReferences(content);
    references.push(...projectRefs);
    const areaRefs = this.parseAreaReferences(content);
    references.push(...areaRefs);
    console.log("✅ 引用解析完成:", {
      总数: references.length,
      WikiLink: wikiLinks.length,
      项目引用: projectRefs.length,
      领域引用: areaRefs.length,
      详细信息: {
        WikiLink: wikiLinks.map((ref) => ref.target),
        项目引用: projectRefs.map((ref) => ref.target),
        领域引用: areaRefs.map((ref) => ref.target)
      }
    });
    return references;
  }
  /**
   * 解析 WikiLink 引用：[[文档名]] 或 [[文档名|显示文本]]
   */
  parseWikiLinks(content) {
    const references = [];
    const wikiLinkRegex = /\[\[([^\]|]+)(\|([^\]]+))?\]\]/g;
    let match;
    while ((match = wikiLinkRegex.exec(content)) !== null) {
      const [fullMatch, target, , display] = match;
      const startIndex = match.index;
      const endIndex = match.index + fullMatch.length;
      const beforeText = content.substring(0, startIndex);
      const lines = beforeText.split("\n");
      const lineNumber = lines.length;
      const columnNumber = lines[lines.length - 1].length + 1;
      const contextBefore = content.substring(Math.max(0, startIndex - 50), startIndex);
      const contextAfter = content.substring(endIndex, Math.min(content.length, endIndex + 50));
      const context = `${contextBefore}${fullMatch}${contextAfter}`;
      references.push({
        type: "wikilink",
        target: target.trim(),
        display: display?.trim(),
        context,
        startIndex,
        endIndex,
        lineNumber,
        columnNumber,
        strength: 1
        // WikiLink 默认强度为 1.0
      });
    }
    return references;
  }
  /**
   * 解析项目引用：@project:项目名 或 @task:任务名
   */
  parseProjectReferences(content) {
    console.log("🔍 开始解析项目引用，内容长度:", content.length);
    const references = [];
    const projectRegex = /@project:([^\s\]，。！？；：""''（）【】]+)/g;
    console.log("📋 项目引用正则表达式:", projectRegex);
    let match;
    while ((match = projectRegex.exec(content)) !== null) {
      const [fullMatch, projectName] = match;
      console.log("✅ 找到项目引用匹配:", { fullMatch, projectName, index: match.index });
      const startIndex = match.index;
      const endIndex = match.index + fullMatch.length;
      const beforeText = content.substring(0, startIndex);
      const lines = beforeText.split("\n");
      const lineNumber = lines.length;
      const columnNumber = lines[lines.length - 1].length + 1;
      const contextBefore = content.substring(Math.max(0, startIndex - 50), startIndex);
      const contextAfter = content.substring(endIndex, Math.min(content.length, endIndex + 50));
      const context = `${contextBefore}${fullMatch}${contextAfter}`;
      references.push({
        type: "project",
        target: projectName.trim(),
        context,
        startIndex,
        endIndex,
        lineNumber,
        columnNumber,
        strength: 0.8
        // 项目引用强度稍低
      });
    }
    const taskRegex = /@task:([^\s\]，。！？；：""''（）【】]+)/g;
    console.log("✅ 任务引用正则表达式:", taskRegex);
    while ((match = taskRegex.exec(content)) !== null) {
      const [fullMatch, taskName] = match;
      console.log("✅ 找到任务引用匹配:", { fullMatch, taskName, index: match.index });
      const startIndex = match.index;
      const endIndex = match.index + fullMatch.length;
      const beforeText = content.substring(0, startIndex);
      const lines = beforeText.split("\n");
      const lineNumber = lines.length;
      const columnNumber = lines[lines.length - 1].length + 1;
      const contextBefore = content.substring(Math.max(0, startIndex - 50), startIndex);
      const contextAfter = content.substring(endIndex, Math.min(content.length, endIndex + 50));
      const context = `${contextBefore}${fullMatch}${contextAfter}`;
      references.push({
        type: "project",
        target: taskName.trim(),
        context,
        startIndex,
        endIndex,
        lineNumber,
        columnNumber,
        strength: 0.9
        // 任务引用强度较高
      });
    }
    return references;
  }
  /**
   * 解析领域引用：#领域名 或 @area:领域名
   */
  parseAreaReferences(content) {
    console.log("🔍 开始解析领域引用，内容长度:", content.length);
    const references = [];
    const lines = content.split("\n");
    lines.forEach((line, lineIndex) => {
      if (line.trim().startsWith("#")) {
        return;
      }
      const tagRegex = /#([^\s#，。！？；：""''（）【】\]]+)/g;
      let match2;
      while ((match2 = tagRegex.exec(line)) !== null) {
        const [fullMatch, areaName] = match2;
        console.log("🏷️ 找到领域标签匹配:", { fullMatch, areaName, line: lineIndex + 1 });
        const lineStartIndex = content.split("\n").slice(0, lineIndex).join("\n").length + (lineIndex > 0 ? 1 : 0);
        const startIndex = lineStartIndex + match2.index;
        const endIndex = startIndex + fullMatch.length;
        const contextBefore = content.substring(Math.max(0, startIndex - 50), startIndex);
        const contextAfter = content.substring(endIndex, Math.min(content.length, endIndex + 50));
        const context = `${contextBefore}${fullMatch}${contextAfter}`;
        references.push({
          type: "area",
          target: areaName.trim(),
          context,
          startIndex,
          endIndex,
          lineNumber: lineIndex + 1,
          columnNumber: match2.index + 1,
          strength: 0.6
          // 标签引用强度较低
        });
      }
    });
    const areaRefRegex = /@area:([^\s\]，。！？；：""''（）【】]+)/g;
    console.log("🏷️ 显式领域引用正则表达式:", areaRefRegex);
    let match;
    while ((match = areaRefRegex.exec(content)) !== null) {
      const [fullMatch, areaName] = match;
      console.log("✅ 找到显式领域引用匹配:", { fullMatch, areaName, index: match.index });
      const startIndex = match.index;
      const endIndex = match.index + fullMatch.length;
      const beforeText = content.substring(0, startIndex);
      const lines2 = beforeText.split("\n");
      const lineNumber = lines2.length;
      const columnNumber = lines2[lines2.length - 1].length + 1;
      const contextBefore = content.substring(Math.max(0, startIndex - 50), startIndex);
      const contextAfter = content.substring(endIndex, Math.min(content.length, endIndex + 50));
      const context = `${contextBefore}${fullMatch}${contextAfter}`;
      references.push({
        type: "area",
        target: areaName.trim(),
        context,
        startIndex,
        endIndex,
        lineNumber,
        columnNumber,
        strength: 0.8
        // 显式领域引用强度较高
      });
    }
    return references;
  }
  /**
   * 验证引用目标是否有效
   */
  validateReference(reference) {
    if (!reference.target || reference.target.trim().length === 0) {
      return false;
    }
    if (reference.target.length > 255) {
      return false;
    }
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(reference.target)) {
      return false;
    }
    return true;
  }
  /**
   * 计算引用强度
   * 基于引用类型、上下文、位置等因素
   */
  calculateReferenceStrength(reference, content) {
    let strength = reference.strength;
    const contextWords = reference.context.toLowerCase();
    if (contextWords.includes("# ") || contextWords.includes("## ") || contextWords.includes("- ")) {
      strength += 0.1;
    }
    if (contextWords.includes("参考") || contextWords.includes("相关") || contextWords.includes("详见")) {
      strength += 0.1;
    }
    return Math.min(1, Math.max(0, strength));
  }
  /**
   * 去重引用
   */
  deduplicateReferences(references) {
    const seen = /* @__PURE__ */ new Set();
    const deduplicated = [];
    for (const ref of references) {
      const key = `${ref.type}-${ref.target}-${ref.startIndex}`;
      if (!seen.has(key)) {
        seen.add(key);
        deduplicated.push(ref);
      }
    }
    return deduplicated;
  }
}
const referenceParser = new ReferenceParser();
export {
  ReferenceParser,
  referenceParser
};
