import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '../ui/dialog'
import { useUserSettingsStore } from '../../store/userSettingsStore'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'
import { fileSystemApi } from '../../lib/api'

interface FirstTimeSetupProps {
  isOpen: boolean
  onComplete: () => void
}

export function FirstTimeSetup({ isOpen, onComplete }: FirstTimeSetupProps) {
  const [step, setStep] = useState(1)
  const [username, setUsername] = useState('')
  const [workspaceDirectory, setWorkspaceDirectory] = useState('')
  const [isSelectingDirectory, setIsSelectingDirectory] = useState(false)

  const { updateSettings, completeFirstTimeSetup } = useUserSettingsStore()
  const { addNotification } = useUIStore()
  const { t } = useLanguage()

  const handleSelectDirectory = async () => {
    setIsSelectingDirectory(true)
    try {
      // 检查是否在Electron环境中
      if (window.electronAPI?.app?.showOpenDialog) {
        const result = await window.electronAPI.app.showOpenDialog({
          properties: ['openDirectory'],
          title: t('firstTimeSetup.dialog.selectDirectoryTitle'),
          buttonLabel: t('firstTimeSetup.dialog.selectDirectoryButton')
        })

        if (!result.canceled && result.filePaths.length > 0) {
          setWorkspaceDirectory(result.filePaths[0])
        }
      } else {
        // 浏览器环境：使用默认目录
        const defaultDir = './PaoLife-Workspace'
        setWorkspaceDirectory(defaultDir)

        addNotification({
          type: 'info',
          title: t('firstTimeSetup.notifications.useDefaultDirectory'),
          message: t('firstTimeSetup.notifications.browserEnvironmentMessage', { directory: defaultDir })
        })
      }
    } catch (error) {
      // 浏览器环境回退方案
      const defaultDir = './PaoLife-Workspace'
      setWorkspaceDirectory(defaultDir)

      addNotification({
        type: 'warning',
        title: t('firstTimeSetup.notifications.useDefaultDirectory'),
        message: t('firstTimeSetup.notifications.directoryPickerFailed', { directory: defaultDir })
      })
    } finally {
      setIsSelectingDirectory(false)
    }
  }

  const handleNext = () => {
    if (step === 1) {
      if (!username.trim()) {
        addNotification({
          type: 'warning',
          title: t('firstTimeSetup.notifications.usernameRequired'),
          message: t('firstTimeSetup.notifications.usernameEmpty')
        })
        return
      }
      setStep(2)
    } else if (step === 2) {
      if (!workspaceDirectory) {
        addNotification({
          type: 'warning',
          title: t('firstTimeSetup.notifications.directoryRequired'),
          message: t('firstTimeSetup.notifications.directoryNeeded')
        })
        return
      }
      handleComplete()
    }
  }

  const handleComplete = async () => {
    try {
      // 检查是否在Electron环境中
      if (window.electronAPI) {
        // Electron环境：使用新的配置管理器
        console.log('🎉 Completing first time setup with config manager...')

        // 保存配置到配置文件
        const configResult = await window.electronAPI.config.completeFirstTimeSetup({
          username: username.trim(),
          workspaceDirectory
        })

        if (!configResult.success) {
          throw new Error(configResult.error || 'Failed to save configuration')
        }

        // 同时更新Zustand store状态
        updateSettings({
          username: username.trim(),
          workspaceDirectory
        })
        completeFirstTimeSetup()

        // 初始化数据库
        const dbResult = await window.electronAPI.window?.initializeDatabaseWithWorkspace(workspaceDirectory)
        if (!dbResult?.success) {
          throw new Error(dbResult?.error || 'Failed to initialize database')
        }

        // 重新初始化文件系统
        const fsResult = await fileSystemApi.reinitialize(workspaceDirectory)
        if (!fsResult.success) {
          throw new Error(fsResult.error || t('firstTimeSetup.notifications.fileSystemInitFailed'))
        }

        console.log('✅ First time setup completed successfully')
      } else {
        // 浏览器环境：使用localStorage
        console.log('Browser environment: Using localStorage for settings')

        // 更新用户设置
        updateSettings({
          username: username.trim(),
          workspaceDirectory
        })

        // 完成首次设置标记
        completeFirstTimeSetup()

        // 等待Zustand persist将设置写入localStorage
        await new Promise(resolve => setTimeout(resolve, 500))
      }

      addNotification({
        type: 'success',
        title: t('firstTimeSetup.notifications.setupComplete'),
        message: t('firstTimeSetup.notifications.welcomeMessage', { username })
      })

      onComplete()
    } catch (error) {
      console.error('Setup error:', error)
      addNotification({
        type: 'error',
        title: t('firstTimeSetup.notifications.setupFailed'),
        message: error instanceof Error ? error.message : t('firstTimeSetup.notifications.initializationError')
      })
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>{t('firstTimeSetup.title')}</DialogTitle>
          <DialogDescription className="space-y-2">
            <div>{t('firstTimeSetup.description')}</div>
            <div className="text-xs bg-blue-50 text-blue-700 p-2 rounded border">
              💡 首次启动需要完成配置后才能初始化数据库，请耐心完成设置
            </div>
          </DialogDescription>
        </DialogHeader>

        {step === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t('firstTimeSetup.step1.title')}</CardTitle>
              <CardDescription>{t('firstTimeSetup.step1.description')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">{t('firstTimeSetup.step1.usernameLabel')}</label>
                <Input
                  placeholder={t('firstTimeSetup.step1.usernamePlaceholder')}
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleNext()}
                  autoFocus
                />
              </div>
              <div className="flex justify-end">
                <Button onClick={handleNext} disabled={!username.trim()}>
                  {t('firstTimeSetup.step1.nextButton')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {step === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t('firstTimeSetup.step2.title')}</CardTitle>
              <CardDescription>{t('firstTimeSetup.step2.description')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">{t('firstTimeSetup.step2.directoryLabel')}</label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      placeholder={t('firstTimeSetup.step2.directoryPlaceholder')}
                      value={workspaceDirectory}
                      onChange={(e) => setWorkspaceDirectory(e.target.value)}
                      className="flex-1"
                    />
                    <Button
                      variant="outline"
                      onClick={handleSelectDirectory}
                      disabled={isSelectingDirectory}
                    >
                      {isSelectingDirectory ? t('firstTimeSetup.step2.browsing') : t('firstTimeSetup.step2.browseButton')}
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setWorkspaceDirectory('./PaoLife-Workspace')}
                    >
                      {t('firstTimeSetup.step2.useDefault')}
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => setWorkspaceDirectory('')}>
                      {t('firstTimeSetup.step2.clear')}
                    </Button>
                  </div>
                </div>
                {workspaceDirectory && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('firstTimeSetup.step2.selected', { path: workspaceDirectory })}
                  </p>
                )}
              </div>
              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(1)}>
                  {t('firstTimeSetup.step2.previousButton')}
                </Button>
                <Button onClick={handleNext} disabled={!workspaceDirectory}>
                  {t('firstTimeSetup.step2.completeButton')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </DialogContent>
    </Dialog>
  )
}
