import { databaseService } from './database'
import type { PrismaClient } from '@prisma/client'

interface ReviewPeriod {
  type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  period: string
  startDate: Date
  endDate: Date
}

interface AggregatedData {
  projects: {
    completed: number
    inProgress: number
    total: number
    completedTasks: number
    totalTasks: number
    completionRate: number
    recentCompletions: Array<{
      name: string
      completedAt: Date
    }>
  }
  areas: {
    total: number
    habitsTracked: number
    habitsCompleted: number
    habitCompletionRate: number
    kpiChanges: Array<{
      areaName: string
      kpiName: string
      previousValue: number
      currentValue: number
      change: number
      changePercent: number
    }>
    recentHabits: Array<{
      areaName: string
      habitName: string
      completedDays: number
      totalDays: number
    }>
  }
  tasks: {
    completed: number
    created: number
    overdue: number
    completionRate: number
    avgCompletionTime: number
    recentCompletions: Array<{
      title: string
      projectName?: string
      completedAt: Date
    }>
  }
  insights: {
    mostProductiveDay?: string
    mostActiveArea?: string
    topProject?: string
    improvementAreas: string[]
    achievements: string[]
  }
}

export class ReviewDataAggregator {
  private prisma: PrismaClient

  constructor(database: DatabaseService) {
    this.prisma = database.getClient()
  }

  /**
   * Parse period string and return date range
   */
  private parsePeriod(type: string, period: string): ReviewPeriod {
    const now = new Date()
    let startDate: Date
    let endDate: Date

    switch (type) {
      case 'daily':
        // Period format: YYYY-MM-DD
        startDate = new Date(period)
        endDate = new Date(startDate)
        endDate.setDate(endDate.getDate() + 1)
        break

      case 'weekly':
        // Period format: YYYY-WXX
        const [year, weekStr] = period.split('-W')
        const week = parseInt(weekStr)
        startDate = this.getDateOfWeek(parseInt(year), week)
        endDate = new Date(startDate)
        endDate.setDate(endDate.getDate() + 7)
        break

      case 'monthly':
        // Period format: YYYY-MM
        const [monthYear, month] = period.split('-')
        startDate = new Date(parseInt(monthYear), parseInt(month) - 1, 1)
        endDate = new Date(parseInt(monthYear), parseInt(month), 1)
        break

      case 'quarterly':
        // Period format: YYYY-QX
        const [qYear, quarterStr] = period.split('-Q')
        const quarter = parseInt(quarterStr)
        const startMonth = (quarter - 1) * 3
        startDate = new Date(parseInt(qYear), startMonth, 1)
        endDate = new Date(parseInt(qYear), startMonth + 3, 1)
        break

      case 'yearly':
        // Period format: YYYY
        const yearNum = parseInt(period)
        startDate = new Date(yearNum, 0, 1)
        endDate = new Date(yearNum + 1, 0, 1)
        break

      default:
        throw new Error(`Unsupported period type: ${type}`)
    }

    return {
      type: type as any,
      period,
      startDate,
      endDate
    }
  }

  /**
   * Get date of the first day of a specific week
   */
  private getDateOfWeek(year: number, week: number): Date {
    const simple = new Date(year, 0, 1 + (week - 1) * 7)
    const dow = simple.getDay()
    const ISOweekStart = simple
    if (dow <= 4) {
      ISOweekStart.setDate(simple.getDate() - simple.getDay() + 1)
    } else {
      ISOweekStart.setDate(simple.getDate() + 8 - simple.getDay())
    }
    return ISOweekStart
  }

  /**
   * Aggregate project data for the review period
   */
  private async aggregateProjectData(period: ReviewPeriod): Promise<AggregatedData['projects']> {
    const { startDate, endDate } = period

    // Get all projects
    const allProjects = await this.prisma.project.findMany({
      include: {
        tasks: {
          where: {
            OR: [
              { createdAt: { gte: startDate, lt: endDate } },
              { completedAt: { gte: startDate, lt: endDate } }
            ]
          }
        }
      }
    })

    // Get completed projects in period
    const completedProjects = allProjects.filter(p => 
      p.completedAt && p.completedAt >= startDate && p.completedAt < endDate
    )

    // Get in-progress projects
    const inProgressProjects = allProjects.filter(p => 
      p.status === 'active' && !p.completedAt
    )

    // Calculate task statistics
    const allTasks = allProjects.flatMap(p => p.tasks)
    const completedTasks = allTasks.filter(t => 
      t.completedAt && t.completedAt >= startDate && t.completedAt < endDate
    )

    const completionRate = allTasks.length > 0 ? (completedTasks.length / allTasks.length) * 100 : 0

    // Get recent completions
    const recentCompletions = completedProjects
      .sort((a, b) => (b.completedAt?.getTime() || 0) - (a.completedAt?.getTime() || 0))
      .slice(0, 5)
      .map(p => ({
        name: p.name,
        completedAt: p.completedAt!
      }))

    return {
      completed: completedProjects.length,
      inProgress: inProgressProjects.length,
      total: allProjects.length,
      completedTasks: completedTasks.length,
      totalTasks: allTasks.length,
      completionRate: Math.round(completionRate),
      recentCompletions
    }
  }

  /**
   * Aggregate area and habit data for the review period
   */
  private async aggregateAreaData(period: ReviewPeriod): Promise<AggregatedData['areas']> {
    const { startDate, endDate } = period

    // Get all areas with their habits and KPIs
    const areas = await this.prisma.area.findMany({
      include: {
        habits: {
          include: {
            completions: {
              where: {
                date: { gte: startDate, lt: endDate }
              }
            }
          }
        },
        kpis: {
          include: {
            values: {
              where: {
                date: { gte: startDate, lt: endDate }
              },
              orderBy: { date: 'desc' }
            }
          }
        }
      }
    })

    // Calculate habit statistics
    let totalHabitsTracked = 0
    let totalHabitsCompleted = 0
    const recentHabits: AggregatedData['areas']['recentHabits'] = []

    areas.forEach(area => {
      area.habits.forEach(habit => {
        const daysInPeriod = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
        const completedDays = habit.completions.length
        
        totalHabitsTracked += daysInPeriod
        totalHabitsCompleted += completedDays

        recentHabits.push({
          areaName: area.name,
          habitName: habit.name,
          completedDays,
          totalDays: daysInPeriod
        })
      })
    })

    const habitCompletionRate = totalHabitsTracked > 0 ? (totalHabitsCompleted / totalHabitsTracked) * 100 : 0

    // Calculate KPI changes
    const kpiChanges: AggregatedData['areas']['kpiChanges'] = []
    areas.forEach(area => {
      area.kpis.forEach(kpi => {
        if (kpi.values.length >= 2) {
          const latest = kpi.values[0]
          const previous = kpi.values[kpi.values.length - 1]
          const change = latest.value - previous.value
          const changePercent = previous.value !== 0 ? (change / previous.value) * 100 : 0

          kpiChanges.push({
            areaName: area.name,
            kpiName: kpi.name,
            previousValue: previous.value,
            currentValue: latest.value,
            change,
            changePercent: Math.round(changePercent * 100) / 100
          })
        }
      })
    })

    return {
      total: areas.length,
      habitsTracked: totalHabitsTracked,
      habitsCompleted: totalHabitsCompleted,
      habitCompletionRate: Math.round(habitCompletionRate),
      kpiChanges,
      recentHabits: recentHabits.slice(0, 10)
    }
  }

  /**
   * Aggregate task data for the review period
   */
  private async aggregateTaskData(period: ReviewPeriod): Promise<AggregatedData['tasks']> {
    const { startDate, endDate } = period

    // Get tasks created in period
    const createdTasks = await this.prisma.task.findMany({
      where: {
        createdAt: { gte: startDate, lt: endDate }
      },
      include: {
        project: true
      }
    })

    // Get tasks completed in period
    const completedTasks = await this.prisma.task.findMany({
      where: {
        completedAt: { gte: startDate, lt: endDate }
      },
      include: {
        project: true
      }
    })

    // Get overdue tasks
    const overdueTasks = await this.prisma.task.findMany({
      where: {
        dueDate: { lt: endDate },
        completedAt: null,
        status: { not: 'completed' }
      }
    })

    const completionRate = createdTasks.length > 0 ? (completedTasks.length / createdTasks.length) * 100 : 0

    // Calculate average completion time
    const tasksWithCompletionTime = completedTasks.filter(t => t.completedAt && t.createdAt)
    const avgCompletionTime = tasksWithCompletionTime.length > 0
      ? tasksWithCompletionTime.reduce((sum, task) => {
          const completionTime = task.completedAt!.getTime() - task.createdAt.getTime()
          return sum + completionTime
        }, 0) / tasksWithCompletionTime.length / (1000 * 60 * 60 * 24) // Convert to days
      : 0

    // Get recent completions
    const recentCompletions = completedTasks
      .sort((a, b) => (b.completedAt?.getTime() || 0) - (a.completedAt?.getTime() || 0))
      .slice(0, 10)
      .map(task => ({
        title: task.title,
        projectName: task.project?.name,
        completedAt: task.completedAt!
      }))

    return {
      completed: completedTasks.length,
      created: createdTasks.length,
      overdue: overdueTasks.length,
      completionRate: Math.round(completionRate),
      avgCompletionTime: Math.round(avgCompletionTime * 10) / 10,
      recentCompletions
    }
  }

  /**
   * Generate insights based on aggregated data
   */
  private generateInsights(data: Omit<AggregatedData, 'insights'>): AggregatedData['insights'] {
    const achievements: string[] = []
    const improvementAreas: string[] = []

    // Project achievements
    if (data.projects.completionRate > 80) {
      achievements.push(`Excellent project completion rate of ${data.projects.completionRate}%`)
    }
    if (data.projects.completed > 0) {
      achievements.push(`Completed ${data.projects.completed} project${data.projects.completed > 1 ? 's' : ''}`)
    }

    // Habit achievements
    if (data.areas.habitCompletionRate > 70) {
      achievements.push(`Strong habit consistency at ${data.areas.habitCompletionRate}%`)
    }

    // Task achievements
    if (data.tasks.completionRate > 75) {
      achievements.push(`High task completion rate of ${data.tasks.completionRate}%`)
    }

    // Improvement areas
    if (data.projects.completionRate < 50) {
      improvementAreas.push('Focus on completing more projects')
    }
    if (data.areas.habitCompletionRate < 50) {
      improvementAreas.push('Improve habit consistency')
    }
    if (data.tasks.overdue > 5) {
      improvementAreas.push('Reduce overdue tasks')
    }

    // Find most active area
    const mostActiveArea = data.areas.recentHabits.length > 0
      ? data.areas.recentHabits.reduce((prev, current) => 
          current.completedDays > prev.completedDays ? current : prev
        ).areaName
      : undefined

    // Find top project (most tasks completed)
    const topProject = data.tasks.recentCompletions.length > 0
      ? data.tasks.recentCompletions[0].projectName
      : undefined

    return {
      mostActiveArea,
      topProject,
      achievements,
      improvementAreas
    }
  }

  /**
   * Aggregate all data for a review period
   */
  async aggregateReviewData(type: string, period: string): Promise<AggregatedData> {
    const reviewPeriod = this.parsePeriod(type, period)

    const [projects, areas, tasks] = await Promise.all([
      this.aggregateProjectData(reviewPeriod),
      this.aggregateAreaData(reviewPeriod),
      this.aggregateTaskData(reviewPeriod)
    ])

    const insights = this.generateInsights({ projects, areas, tasks })

    return {
      projects,
      areas,
      tasks,
      insights
    }
  }
}

export default ReviewDataAggregator
