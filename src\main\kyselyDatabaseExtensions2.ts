// Kysely Database Service Extensions Part 2
// Additional methods for Task, Review, and other operations

import type { Kysely } from 'kysely'
import type { DatabaseResult } from '../shared/types'
import type { Database } from '../shared/database-types'
import { 
  serialize<PERSON><PERSON>,
  deserialize<PERSON>son,
  booleanToInt,
  intToBoolean,
  getCurrentTimestamp,
  generateId
} from './kyselyClient'

export class KyselyDatabaseExtensions2 {
  constructor(private db: Kysely<Database>) {}

  /**
   * HABIT RECORD OPERATIONS
   */

  async createHabitRecord(data: {
    date: string | Date
    completed?: boolean
    value?: number
    note?: string
    habitId: string
  }): Promise<DatabaseResult<any>> {
    try {
      // Debug: log the input data
      console.log('Creating habit record with data:', JSON.stringify(data, null, 2))

      // Ensure all values are properly typed for SQLite
      const insertData = {
        id: generateId(),
        date: data.date instanceof Date ? data.date.toISOString().split('T')[0] : String(data.date), // Convert Date to YYYY-MM-DD string
        completed: booleanToInt(data.completed ?? true),
        value: data.value ? Number(data.value) : null, // Ensure number or null
        note: data.note ? String(data.note) : null, // Ensure string or null
        habitId: String(data.habitId) // Ensure string
      }

      console.log('Inserting habit record with processed data:', JSON.stringify(insertData, null, 2))

      const record = await this.db.insertInto('HabitRecord')
        .values(insertData)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...record,
          completed: intToBoolean(record.completed)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create habit record'
      }
    }
  }

  async updateHabitRecord(id: string, data: {
    completed?: boolean
    value?: number
    note?: string
  }): Promise<DatabaseResult<any>> {
    try {
      console.log('Updating habit record:', id, 'with data:', JSON.stringify(data, null, 2))

      const updateData: any = {}

      if (data.completed !== undefined) updateData.completed = booleanToInt(data.completed)
      if (data.value !== undefined) updateData.value = data.value
      if (data.note !== undefined) updateData.note = data.note

      // Check if there's anything to update
      if (Object.keys(updateData).length === 0) {
        throw new Error('No fields to update')
      }

      console.log('Update data:', JSON.stringify(updateData, null, 2))

      const record = await this.db.updateTable('HabitRecord')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...record,
          completed: intToBoolean(record.completed)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update habit record'
      }
    }
  }

  async getHabitRecords(habitId: string): Promise<DatabaseResult<any[]>> {
    try {
      const records = await this.db.selectFrom('HabitRecord')
        .selectAll()
        .where('habitId', '=', habitId)
        .orderBy('date', 'desc')
        .execute()

      return {
        success: true,
        data: records.map(record => ({
          ...record,
          completed: intToBoolean(record.completed)
        }))
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get habit records'
      }
    }
  }

  async deleteHabitRecord(id: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('HabitRecord')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete habit record'
      }
    }
  }

  /**
   * RECURRING TASK OPERATIONS
   */

  async createRecurringTask(data: any): Promise<DatabaseResult<any>> {
    try {
      console.log('Creating recurring task with data:', JSON.stringify(data, null, 2))

      // Only extract the fields that exist in the database table
      const insertData = {
        id: generateId(),
        title: String(data.title),
        description: data.description ? String(data.description) : null,
        repeatRule: String(data.repeatRule),
        repeatInterval: data.repeatInterval ? Number(data.repeatInterval) : 1,
        nextDueDate: data.nextDueDate ? String(data.nextDueDate) : null,
        lastCompletedAt: null,
        isActive: booleanToInt(true),
        areaId: String(data.areaId),
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp()
      }

      console.log('Inserting recurring task with processed data:', JSON.stringify(insertData, null, 2))

      const task = await this.db.insertInto('RecurringTask')
        .values(insertData)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...task,
          isActive: intToBoolean(task.isActive)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create recurring task'
      }
    }
  }

  async getRecurringTasks(areaId: string): Promise<DatabaseResult<any[]>> {
    try {
      const tasks = await this.db.selectFrom('RecurringTask')
        .selectAll()
        .where('areaId', '=', areaId)
        .where('isActive', '=', booleanToInt(true))
        .orderBy('updatedAt', 'desc')
        .execute()

      return {
        success: true,
        data: tasks.map(task => ({
          ...task,
          isActive: intToBoolean(task.isActive)
        }))
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get recurring tasks'
      }
    }
  }

  async updateRecurringTask(id: string, data: {
    title?: string
    description?: string
    repeatRule?: string
    repeatInterval?: number
    nextDueDate?: string
    lastCompletedAt?: string
    isActive?: boolean
  }): Promise<DatabaseResult<any>> {
    try {
      const updateData: any = {
        updatedAt: getCurrentTimestamp()
      }

      if (data.title !== undefined) updateData.title = data.title
      if (data.description !== undefined) updateData.description = data.description
      if (data.repeatRule !== undefined) updateData.repeatRule = data.repeatRule
      if (data.repeatInterval !== undefined) updateData.repeatInterval = data.repeatInterval
      if (data.nextDueDate !== undefined) updateData.nextDueDate = data.nextDueDate
      if (data.lastCompletedAt !== undefined) updateData.lastCompletedAt = data.lastCompletedAt
      if (data.isActive !== undefined) updateData.isActive = booleanToInt(data.isActive)

      const task = await this.db.updateTable('RecurringTask')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...task,
          isActive: intToBoolean(task.isActive)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update recurring task'
      }
    }
  }

  async deleteRecurringTask(id: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('RecurringTask')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete recurring task'
      }
    }
  }

  /**
   * DELIVERABLE OPERATIONS
   */

  async createDeliverable(data: any): Promise<DatabaseResult<any>> {
    try {
      console.log('Creating deliverable with data:', JSON.stringify(data, null, 2))

      // Only extract the fields that exist in the database table and ensure proper types
      const insertData = {
        id: generateId(),
        title: String(data.title),
        description: data.description ? String(data.description) : null,
        type: data.type ? String(data.type) : 'document',
        status: data.status ? String(data.status) : 'planned',
        content: data.content ? String(data.content) : null,
        url: data.url ? String(data.url) : null,
        filePath: data.filePath ? String(data.filePath) : null,
        acceptanceCriteria: data.acceptanceCriteria ? serializeJson(data.acceptanceCriteria) : null,
        plannedDate: data.plannedDate ? (data.plannedDate instanceof Date ? data.plannedDate.toISOString().split('T')[0] : String(data.plannedDate)) : null,
        actualDate: data.actualDate ? (data.actualDate instanceof Date ? data.actualDate.toISOString().split('T')[0] : String(data.actualDate)) : null,
        projectId: String(data.projectId),
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp()
      }

      console.log('Inserting deliverable with processed data:', JSON.stringify(insertData, null, 2))

      const deliverable = await this.db.insertInto('Deliverable')
        .values(insertData)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...deliverable,
          acceptanceCriteria: deserializeJson(deliverable.acceptanceCriteria)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create deliverable'
      }
    }
  }

  async getProjectDeliverables(projectId: string): Promise<DatabaseResult<any[]>> {
    try {
      const deliverables = await this.db.selectFrom('Deliverable')
        .selectAll()
        .where('projectId', '=', projectId)
        .orderBy('updatedAt', 'desc')
        .execute()

      return {
        success: true,
        data: deliverables.map(deliverable => ({
          ...deliverable,
          acceptanceCriteria: deserializeJson(deliverable.acceptanceCriteria)
        }))
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get project deliverables'
      }
    }
  }

  async updateDeliverable(id: string, data: {
    title?: string
    description?: string
    type?: string
    status?: string
    content?: string
    url?: string
    filePath?: string
    acceptanceCriteria?: any[]
    plannedDate?: string
    actualDate?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const updateData: any = {
        updatedAt: getCurrentTimestamp()
      }

      if (data.title !== undefined) updateData.title = data.title
      if (data.description !== undefined) updateData.description = data.description
      if (data.type !== undefined) updateData.type = data.type
      if (data.status !== undefined) updateData.status = data.status
      if (data.content !== undefined) updateData.content = data.content
      if (data.url !== undefined) updateData.url = data.url
      if (data.filePath !== undefined) updateData.filePath = data.filePath
      if (data.plannedDate !== undefined) updateData.plannedDate = data.plannedDate
      if (data.actualDate !== undefined) updateData.actualDate = data.actualDate
      if (data.acceptanceCriteria !== undefined) {
        updateData.acceptanceCriteria = data.acceptanceCriteria ? serializeJson(data.acceptanceCriteria) : null
      }

      const deliverable = await this.db.updateTable('Deliverable')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: {
          ...deliverable,
          acceptanceCriteria: deserializeJson(deliverable.acceptanceCriteria)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update deliverable'
      }
    }
  }

  async deleteDeliverable(id: string): Promise<DatabaseResult<void>> {
    try {
      await this.db.deleteFrom('Deliverable')
        .where('id', '=', id)
        .execute()

      return {
        success: true,
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete deliverable'
      }
    }
  }
}
