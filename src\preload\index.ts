import { context<PERSON>ridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { IPC_CHANNELS } from '../shared/ipcTypes'
import type {
  ElectronApi,
  CreateProjectRequest,
  UpdateProjectRequest,
  CreateAreaRequest,
  UpdateAreaRequest,
  CreateTaskRequest,
  UpdateTaskRequest,
  CreateResourceRequest,
  CreateProjectKPIRequest,
  UpdateProjectKPIRequest,
  CreateKPIRecordRequest,
  UpdateKPIRecordRequest,
  CreateAreaMetricRequest,
  UpdateAreaMetricRequest,
  CreateAreaMetricRecordRequest,
  UpdateAreaMetricRecordRequest,
  CreateDeliverableRequest,
  CreateHabitRequest,
  UpdateHabitRequest,
  CreateHabitRecordRequest,
  UpdateHabitRecordRequest,
  CreateReviewRequest,
  UpdateReviewRequest,
  GetReviewsRequest,
  CreateReviewTemplateRequest,
  UpdateReviewTemplateRequest,
  UpdateDeliverableRequest,
  LinkResourceToProjectRequest,
  CreateDocumentLinkRequest,
  UpdateDocumentLinkRequest,
  UpdateDocumentPathRequest,
  SearchLinksRequest,
  ReplaceDocumentLinksRequest,
  CreateChecklistRequest,
  UpdateChecklistRequest,
  CreateChecklistInstanceRequest,
  UpdateChecklistInstanceRequest,
  ReadFileRequest,
  WriteFileRequest,
  MoveFileRequest,
  CopyFileRequest,
  // {{ AURA-X: Add - 导入定期维护任务类型. Approval: 寸止(ID:1738157400). }}
  CreateRecurringTaskRequest,
  UpdateRecurringTaskRequest
} from '../shared/ipcTypes'

// Custom APIs for renderer
const api: ElectronApi = {
  // Database API
  database: {
    initialize: () => ipcRenderer.invoke(IPC_CHANNELS.DB_INITIALIZE),
    testConnection: () => ipcRenderer.invoke(IPC_CHANNELS.DB_TEST_CONNECTION),
    getStatus: () => ipcRenderer.invoke('db-get-status'),
    createProject: (data: CreateProjectRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_PROJECT, data),
    getProjects: () => ipcRenderer.invoke(IPC_CHANNELS.DB_GET_PROJECTS),
    getProjectById: (id: string, includeArchived?: boolean) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_PROJECT_BY_ID, id, includeArchived),
    updateProject: (data: UpdateProjectRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_PROJECT, data),
    deleteProject: (id: string) => ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_PROJECT, id),
    archiveProject: (id: string) => ipcRenderer.invoke(IPC_CHANNELS.DB_ARCHIVE_PROJECT, id),
    createArea: (data: CreateAreaRequest) => ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_AREA, data),
    getAreas: () => ipcRenderer.invoke(IPC_CHANNELS.DB_GET_AREAS),
    getAreaById: (id: string, includeArchived?: boolean) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_AREA_BY_ID, id, includeArchived),
    updateArea: (data: UpdateAreaRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_AREA, data),
    deleteArea: (id: string) => ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_AREA, id),
    archiveArea: (id: string) => ipcRenderer.invoke(IPC_CHANNELS.DB_ARCHIVE_AREA, id),
    getArchivedProjects: () => ipcRenderer.invoke(IPC_CHANNELS.DB_GET_ARCHIVED_PROJECTS),
    getArchivedAreas: () => ipcRenderer.invoke(IPC_CHANNELS.DB_GET_ARCHIVED_AREAS),
    restoreProject: (id: string) => ipcRenderer.invoke(IPC_CHANNELS.DB_RESTORE_PROJECT, id),
    restoreArea: (id: string) => ipcRenderer.invoke(IPC_CHANNELS.DB_RESTORE_AREA, id),
    createTask: (data: CreateTaskRequest) => ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_TASK, data),
    getTasks: (filters?: any) => ipcRenderer.invoke(IPC_CHANNELS.DB_GET_TASKS, filters),
    updateTask: (data: UpdateTaskRequest) => ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_TASK, data),
    createResource: (data: CreateResourceRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_RESOURCE, data),
    getResources: (filters?: any) => ipcRenderer.invoke(IPC_CHANNELS.DB_GET_RESOURCES, filters),

    // ProjectKPI operations
    createProjectKPI: (data: CreateProjectKPIRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_PROJECT_KPI, data),
    getProjectKPIs: (projectId: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_PROJECT_KPIS, projectId),
    updateProjectKPI: (data: UpdateProjectKPIRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_PROJECT_KPI, data),
    deleteProjectKPI: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_PROJECT_KPI, id),

    // KPI Record operations
    createKPIRecord: (data: CreateKPIRecordRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_KPI_RECORD, data),
    getKPIRecords: (kpiId: string, limit?: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_KPI_RECORDS, kpiId, limit),
    updateKPIRecord: (data: UpdateKPIRecordRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_KPI_RECORD, data),
    deleteKPIRecord: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_KPI_RECORD, id),

    // Area Metric operations
    createAreaMetric: (data: CreateAreaMetricRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_AREA_METRIC, data),
    getAreaMetrics: (areaId: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_AREA_METRICS, areaId),
    updateAreaMetric: (data: UpdateAreaMetricRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_AREA_METRIC, data),
    deleteAreaMetric: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_AREA_METRIC, id),

    // Area Metric Record operations
    createAreaMetricRecord: (data: CreateAreaMetricRecordRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_AREA_METRIC_RECORD, data),
    getAreaMetricRecords: (metricId: string, limit?: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_AREA_METRIC_RECORDS, metricId, limit),
    updateAreaMetricRecord: (data: UpdateAreaMetricRecordRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_AREA_METRIC_RECORD, data),
    deleteAreaMetricRecord: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_AREA_METRIC_RECORD, id),

    // Habit operations
    getHabitsByArea: (areaId: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_HABITS_BY_AREA, areaId),

    // Deliverable operations
    createDeliverable: (data: CreateDeliverableRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_DELIVERABLE, data),
    getProjectDeliverables: (projectId: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_PROJECT_DELIVERABLES, projectId),
    updateDeliverable: (data: UpdateDeliverableRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_DELIVERABLE, data),
    deleteDeliverable: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_DELIVERABLE, id),
    // Habit operations
    createHabit: (data: CreateHabitRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_HABIT, data),
    updateHabit: (data: UpdateHabitRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_HABIT, data),
    deleteHabit: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_HABIT, id),
    // Habit record operations
    createHabitRecord: (data: CreateHabitRecordRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_HABIT_RECORD, data),
    updateHabitRecord: (data: UpdateHabitRecordRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_HABIT_RECORD, data),
    getHabitRecords: (habitId: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_HABIT_RECORDS, habitId),
    deleteHabitRecord: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_HABIT_RECORD, id),

    // {{ AURA-X: Add - 定期维护任务preload API. Approval: 寸止(ID:1738157400). }}
    // Recurring task operations
    createRecurringTask: (data: CreateRecurringTaskRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_RECURRING_TASK, data),
    getRecurringTasks: (areaId: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_RECURRING_TASKS, areaId),
    updateRecurringTask: (data: UpdateRecurringTaskRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_RECURRING_TASK, data),
    deleteRecurringTask: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_RECURRING_TASK, id),

    // Resource linking operations
    getProjectResources: (projectId: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_PROJECT_RESOURCES, projectId),
    linkResourceToProject: (data: LinkResourceToProjectRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_LINK_RESOURCE_TO_PROJECT, data),
    unlinkResourceFromProject: (resourceId: string, projectId: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UNLINK_RESOURCE_FROM_PROJECT, resourceId, projectId),
    getAreaResources: (areaId: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_AREA_RESOURCES, areaId),
    unlinkResourceFromArea: (resourceId: string, areaId: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UNLINK_RESOURCE_FROM_AREA, resourceId, areaId),
    getResourceReferences: (resourcePath: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_RESOURCE_REFERENCES, resourcePath),

    // Document Link operations
    createDocumentLink: (data: CreateDocumentLinkRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_DOCUMENT_LINK, data),
    getDocumentLinks: (docPath: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_DOCUMENT_LINKS, docPath),
    getBacklinks: (docPath: string) => ipcRenderer.invoke(IPC_CHANNELS.DB_GET_BACKLINKS, docPath),
    getOutlinks: (docPath: string) => ipcRenderer.invoke(IPC_CHANNELS.DB_GET_OUTLINKS, docPath),
    updateDocumentLink: (data: UpdateDocumentLinkRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_DOCUMENT_LINK, data),
    deleteDocumentLink: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_DOCUMENT_LINK, id),
    deleteDocumentLinks: (docPath: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_DOCUMENT_LINKS, docPath),
    updateDocumentPath: (data: UpdateDocumentPathRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_DOCUMENT_PATH, data),
    markLinksAsInvalid: (targetDocPath: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_MARK_LINKS_INVALID, targetDocPath),
    markLinksAsValid: (targetDocPath: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_MARK_LINKS_VALID, targetDocPath),
    getLinkStatistics: (docPath: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_LINK_STATISTICS, docPath),
    searchLinks: (data: SearchLinksRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_SEARCH_LINKS, data),
    replaceDocumentLinks: (data: ReplaceDocumentLinksRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_REPLACE_DOCUMENT_LINKS, data),

    // Checklist operations
    createChecklist: (data: CreateChecklistRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_CHECKLIST, data),
    getChecklists: (areaId?: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_CHECKLISTS, areaId),
    updateChecklist: (data: UpdateChecklistRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_CHECKLIST, data),
    deleteChecklist: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_CHECKLIST, id),

    // Checklist Instance operations
    createChecklistInstance: (data: CreateChecklistInstanceRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_CHECKLIST_INSTANCE, data),
    getChecklistInstances: (areaId?: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_CHECKLIST_INSTANCES, areaId),
    updateChecklistInstance: (data: UpdateChecklistInstanceRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_CHECKLIST_INSTANCE, data),
    deleteChecklistInstance: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_CHECKLIST_INSTANCE, id),

    // Review operations
    createReview: (data: CreateReviewRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_REVIEW, data),
    getReviews: (filters?: GetReviewsRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_REVIEWS, filters),
    getReviewById: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_REVIEW_BY_ID, id),
    updateReview: (data: UpdateReviewRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_REVIEW, data),
    deleteReview: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_REVIEW, id),

    // Review Template operations
    createReviewTemplate: (data: CreateReviewTemplateRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_CREATE_REVIEW_TEMPLATE, data),
    getReviewTemplates: (type?: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_REVIEW_TEMPLATES, type),
    getReviewTemplateById: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_REVIEW_TEMPLATE_BY_ID, id),
    updateReviewTemplate: (data: UpdateReviewTemplateRequest) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_UPDATE_REVIEW_TEMPLATE, data),
    deleteReviewTemplate: (id: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_DELETE_REVIEW_TEMPLATE, id),

    // Review Data Aggregation
    getReviewAggregatedData: (type: string, period: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_REVIEW_AGGREGATED_DATA, type, period),

    // Review Analysis
    getReviewAnalysis: (type: string, period: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_GET_REVIEW_ANALYSIS, type, period)
  },

  // File System API
  fileSystem: {
    initialize: () => ipcRenderer.invoke(IPC_CHANNELS.FS_INITIALIZE),
    reinitialize: (workspaceDirectory: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.FS_REINITIALIZE, workspaceDirectory),
    readFile: (data: ReadFileRequest) => ipcRenderer.invoke(IPC_CHANNELS.FS_READ_FILE, data),
    writeFile: (data: WriteFileRequest) => ipcRenderer.invoke(IPC_CHANNELS.FS_WRITE_FILE, data),
    deleteFile: (path: string) => ipcRenderer.invoke(IPC_CHANNELS.FS_DELETE_FILE, path),
    moveFile: (data: MoveFileRequest) => ipcRenderer.invoke(IPC_CHANNELS.FS_MOVE_FILE, data),
    copyFile: (data: CopyFileRequest) => ipcRenderer.invoke(IPC_CHANNELS.FS_COPY_FILE, data),
    fileExists: (path: string) => ipcRenderer.invoke(IPC_CHANNELS.FS_FILE_EXISTS, path),
    getFileInfo: (path: string) => ipcRenderer.invoke(IPC_CHANNELS.FS_GET_FILE_INFO, path),
    listDirectory: (path: string) => ipcRenderer.invoke(IPC_CHANNELS.FS_LIST_DIRECTORY, path),
    createDirectory: (path: string) => ipcRenderer.invoke(IPC_CHANNELS.FS_CREATE_DIRECTORY, path),
    deleteDirectory: (path: string) => ipcRenderer.invoke(IPC_CHANNELS.FS_DELETE_DIRECTORY, path),
    rename: (oldPath: string, newPath: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.FS_RENAME, oldPath, newPath),
    watchDirectory: (path: string) => ipcRenderer.invoke(IPC_CHANNELS.FS_WATCH_DIRECTORY, path),
    unwatchDirectory: (path: string) => ipcRenderer.invoke(IPC_CHANNELS.FS_UNWATCH_DIRECTORY, path)
  },

  // Application API
  app: {
    getVersion: () => ipcRenderer.invoke(IPC_CHANNELS.APP_GET_VERSION),
    getPath: (name: string) => ipcRenderer.invoke(IPC_CHANNELS.APP_GET_PATH, name),
    showMessageBox: (options: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.APP_SHOW_MESSAGE_BOX, options),
    showErrorBox: (title: string, content: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.APP_SHOW_ERROR_BOX, title, content),
    showOpenDialog: (options: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.APP_SHOW_OPEN_DIALOG, options),
    showSaveDialog: (options: any) => ipcRenderer.invoke(IPC_CHANNELS.APP_SHOW_SAVE_DIALOG, options),

    // Exit preference controls
    getExitPreference: () => ipcRenderer.invoke(IPC_CHANNELS.APP_GET_EXIT_PREFERENCE),
    setExitPreference: (mode: 'ask' | 'minimize' | 'exit') => ipcRenderer.invoke(IPC_CHANNELS.APP_SET_EXIT_PREFERENCE, { mode }),
    clearExitPreference: () => ipcRenderer.invoke(IPC_CHANNELS.APP_CLEAR_EXIT_PREFERENCE)
  },

  // Settings API
  settings: {
    getUserSettings: () => ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_GET_USER_SETTINGS),
    updateUserSettings: (settings: any) => ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_UPDATE_USER_SETTINGS, settings),
    resetUserSettings: () => ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_RESET_USER_SETTINGS),
    exportData: () => ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_EXPORT_DATA),
    importData: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_IMPORT_DATA, data),
    createBackup: () => ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_CREATE_BACKUP),
    getDatabaseInfo: () => ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_GET_DATABASE_INFO),
    selectResourcePath: () => ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_SELECT_RESOURCE_PATH),
    updateResourcePath: (path: string) => ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_UPDATE_RESOURCE_PATH, path)
  },

  // Configuration API
  config: {
    completeFirstTimeSetup: (data: { username: string; workspaceDirectory: string }) =>
      ipcRenderer.invoke('config:complete-first-time-setup', data),
    getStatus: () => ipcRenderer.invoke('config:get-status')
  },

  // Window API
  window: {
    minimize: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_MINIMIZE),
    maximize: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_MAXIMIZE),
    close: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_CLOSE),
    toggleDevTools: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_TOGGLE_DEVTOOLS),
    isMaximized: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_IS_MAXIMIZED),
    registerGlobalShortcut: (accelerator: string, action: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.GLOBAL_SHORTCUT_REGISTER, accelerator, action),
    unregisterGlobalShortcut: (accelerator: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.GLOBAL_SHORTCUT_UNREGISTER, accelerator),
    unregisterAllGlobalShortcuts: () =>
      ipcRenderer.invoke(IPC_CHANNELS.GLOBAL_SHORTCUT_UNREGISTER_ALL),
    forceQuit: () => ipcRenderer.invoke(IPC_CHANNELS.APP_FORCE_QUIT),
    cancelQuit: () => ipcRenderer.invoke(IPC_CHANNELS.APP_CANCEL_QUIT),
    exitAction: (action: 'exit' | 'minimize' | 'cancel') => ipcRenderer.invoke('app-exit-action', action),
    setWorkspaceDirectory: (directory: string) => ipcRenderer.invoke(IPC_CHANNELS.DB_SET_WORKSPACE_DIRECTORY, directory),
    initializeDatabaseWithWorkspace: (directory: string) => ipcRenderer.invoke(IPC_CHANNELS.DB_INITIALIZE_WITH_WORKSPACE, directory),
    emergencyInitDatabase: () => ipcRenderer.invoke('emergency-init-database')
  },

  // Event listeners
  onFileSystemEvent: (callback) => {
    const unsubscribe = () => ipcRenderer.removeAllListeners(IPC_CHANNELS.FS_EVENT)
    ipcRenderer.on(IPC_CHANNELS.FS_EVENT, (_, event) => callback(event))
    return unsubscribe
  },

  onDatabaseEvent: (callback) => {
    const unsubscribe = () => ipcRenderer.removeAllListeners('database-event')
    ipcRenderer.on('database-event', (_, event) => callback(event))
    return unsubscribe
  },

  // IPC Renderer for global shortcuts and events
  ipcRenderer: {
    on: (channel: string, listener: (...args: any[]) => void) => {
      ipcRenderer.on(channel, listener)
    },
    removeAllListeners: (channel: string) => {
      ipcRenderer.removeAllListeners(channel)
    },
    removeListener: (channel: string, listener: (...args: any[]) => void) => {
      ipcRenderer.removeListener(channel, listener)
    }
  }
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('electronAPI', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.electronAPI = api
}
