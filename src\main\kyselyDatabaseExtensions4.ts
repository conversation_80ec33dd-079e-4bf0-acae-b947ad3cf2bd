// Kysely Database Service Extensions Part 4
// Task operations with complex relationships

import type { Kysely } from 'kysely'
import type { DatabaseResult } from '../shared/types'
import type { Database } from '../shared/database-types'
import { 
  serializeJson,
  deserializeJson,
  booleanToInt,
  intToBoolean,
  getCurrentTimestamp,
  generateId,
  withTransaction
} from './kyselyClient'

export class KyselyDatabaseExtensions4 {
  constructor(private db: Kysely<Database>) {}

  /**
   * TASK OPERATIONS
   */

  async createTask(data: {
    title: string
    description?: string
    status?: string
    priority?: string
    dueDate?: string
    estimatedTime?: number
    actualTime?: number
    projectId?: string
    areaId?: string
    parentTaskId?: string
    tags?: string[]
  }): Promise<DatabaseResult<any>> {
    try {
      return await withTransaction(this.db, async (trx) => {
        // Create the task
        const task = await trx.insertInto('Task')
          .values({
            id: generateId(),
            content: data.title, // Map title to content
            description: data.description || null,
            priority: data.priority || null,
            dueDate: data.dueDate || null,
            projectId: data.projectId || null,
            areaId: data.areaId || null,
            parentId: data.parentTaskId || null, // Map parentTaskId to parentId
            completed: booleanToInt(false),
            completedAt: null,
            position: 0,
            sourceType: null,
            sourceId: null,
            sourceContext: null,
            resourceLinkId: null,
            createdAt: getCurrentTimestamp(),
            updatedAt: getCurrentTimestamp()
          })
          .returningAll()
          .executeTakeFirstOrThrow()

        // Handle tags if provided
        if (data.tags && data.tags.length > 0) {
          for (const tagName of data.tags) {
            // Find or create tag
            let tag = await trx.selectFrom('Tag')
              .selectAll()
              .where('name', '=', tagName)
              .executeTakeFirst()

            if (!tag) {
              tag = await trx.insertInto('Tag')
                .values({
                  id: generateId(),
                  name: tagName,
                  color: null,
                  icon: null
                })
                .returningAll()
                .executeTakeFirstOrThrow()
            }

            // Create task-tag relationship
            await trx.insertInto('TaskTag')
              .values({
                taskId: task.id,
                tagId: tag.id
              })
              .execute()
          }
        }

        // Return the created task with converted boolean and mapped fields
        console.log('Created task from database:', task)
        const mappedTask = {
          ...task,
          completed: intToBoolean(task.completed),
          // Ensure field mapping for frontend compatibility
          deadline: task.dueDate, // Map dueDate back to deadline for frontend
          parentId: task.parentId // Ensure parentId is correctly mapped
        }
        console.log('Mapped task for frontend:', mappedTask)
        return {
          success: true,
          data: mappedTask
        }
      })
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create task'
      }
    }
  }

  async getTasks(filters: {
    projectId?: string
    areaId?: string
    status?: string
    priority?: string
    parentTaskId?: string
    includeCompleted?: boolean
    tags?: string[]
    limit?: number
    offset?: number
  } = {}): Promise<DatabaseResult<any[]>> {
    try {
      let query = this.db.selectFrom('Task')
        .selectAll()
        .orderBy('updatedAt', 'desc')

      // Apply filters
      if (filters.projectId) {
        query = query.where('projectId', '=', filters.projectId)
      }
      if (filters.areaId) {
        query = query.where('areaId', '=', filters.areaId)
      }
      if (filters.status) {
        query = query.where('status', '=', filters.status)
      }
      if (filters.priority) {
        query = query.where('priority', '=', filters.priority)
      }
      if (filters.parentTaskId !== undefined) {
        if (filters.parentTaskId === null) {
          query = query.where('parentTaskId', 'is', null)
        } else {
          query = query.where('parentTaskId', '=', filters.parentTaskId)
        }
      }
      if (!filters.includeCompleted) {
        query = query.where('completed', '=', booleanToInt(false))
      }

      // Handle tag filtering
      if (filters.tags && filters.tags.length > 0) {
        query = query.where('id', 'in', (eb) =>
          eb.selectFrom('TaskTag')
            .innerJoin('Tag', 'Tag.id', 'TaskTag.tagId')
            .select('TaskTag.taskId')
            .where('Tag.name', 'in', filters.tags!)
        )
      }

      if (filters.limit) {
        query = query.limit(filters.limit)
      }
      if (filters.offset) {
        query = query.offset(filters.offset)
      }

      const tasks = await query.execute()

      // Get tags for each task
      const tasksWithTags = await Promise.all(
        tasks.map(async (task) => {
          const tags = await this.db.selectFrom('TaskTag')
            .innerJoin('Tag', 'Tag.id', 'TaskTag.tagId')
            .select(['Tag.id', 'Tag.name', 'Tag.color'])
            .where('TaskTag.taskId', '=', task.id)
            .execute()

          return {
            ...task,
            completed: intToBoolean(task.completed),
            tags
          }
        })
      )

      return {
        success: true,
        data: tasksWithTags
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get tasks'
      }
    }
  }

  async getTaskById(id: string): Promise<DatabaseResult<any>> {
    try {
      const task = await this.db.selectFrom('Task')
        .selectAll()
        .where('id', '=', id)
        .executeTakeFirst()

      if (!task) {
        throw new Error('Task not found')
      }

      // Get tags for the task
      const tags = await this.db.selectFrom('TaskTag')
        .innerJoin('Tag', 'Tag.id', 'TaskTag.tagId')
        .select(['Tag.id', 'Tag.name', 'Tag.color'])
        .where('TaskTag.taskId', '=', id)
        .execute()

      // Get subtasks
      const subtasks = await this.db.selectFrom('Task')
        .selectAll()
        .where('parentId', '=', id) // Use parentId instead of parentTaskId
        .execute()

      return {
        success: true,
        data: {
          ...task,
          completed: intToBoolean(task.completed),
          tags,
          subtasks: subtasks.map(subtask => ({
            ...subtask,
            completed: intToBoolean(subtask.completed)
          }))
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get task by id'
      }
    }
  }

  async updateTask(id: string, data: {
    title?: string
    description?: string
    status?: string
    priority?: string
    dueDate?: string
    estimatedTime?: number
    actualTime?: number
    completed?: boolean
    projectId?: string
    areaId?: string
    parentTaskId?: string
    tags?: string[]
  }): Promise<DatabaseResult<any>> {
    try {
      return await withTransaction(this.db, async (trx) => {
        // Update task fields
        const updateData: any = {
          updatedAt: getCurrentTimestamp()
        }

        if (data.title !== undefined) updateData.content = data.title // Map title to content
        if (data.description !== undefined) updateData.description = data.description
        if (data.priority !== undefined) updateData.priority = data.priority
        if (data.dueDate !== undefined) updateData.dueDate = data.dueDate
        if (data.completed !== undefined) updateData.completed = booleanToInt(data.completed)
        if (data.projectId !== undefined) updateData.projectId = data.projectId
        if (data.areaId !== undefined) updateData.areaId = data.areaId
        if (data.parentTaskId !== undefined) updateData.parentId = data.parentTaskId // Map parentTaskId to parentId

        const task = await trx.updateTable('Task')
          .set(updateData)
          .where('id', '=', id)
          .returningAll()
          .executeTakeFirstOrThrow()

        // Handle tags update if provided
        if (data.tags !== undefined) {
          // Remove existing tag relationships
          await trx.deleteFrom('TaskTag')
            .where('taskId', '=', id)
            .execute()

          // Add new tag relationships
          if (data.tags.length > 0) {
            for (const tagName of data.tags) {
              // Find or create tag
              let tag = await trx.selectFrom('TaskTag')
                .selectAll()
                .where('name', '=', tagName)
                .executeTakeFirst()

              if (!tag) {
                tag = await trx.insertInto('TaskTag')
                  .values({
                    id: generateId(),
                    name: tagName,
                    color: null
                  })
                  .returningAll()
                  .executeTakeFirstOrThrow()
              }

              // Create task-tag relationship
              await trx.insertInto('TaskTag')
                .values({
                  taskId: id,
                  tagId: tag.id
                })
                .execute()
            }
          }
        }

        // Return updated task with tags
        const taskWithTags = await this.getTaskById(id)
        return taskWithTags
      })
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update task'
      }
    }
  }

  async deleteTask(id: string): Promise<DatabaseResult<void>> {
    try {
      return await withTransaction(this.db, async (trx) => {
        // Delete tag relationships
        await trx.deleteFrom('TaskTag')
          .where('taskId', '=', id)
          .execute()

        // Delete subtasks (if any)
        const subtasks = await trx.selectFrom('Task')
          .select('id')
          .where('parentId', '=', id) // Use parentId instead of parentTaskId
          .execute()

        for (const subtask of subtasks) {
          await trx.deleteFrom('TaskTag')
            .where('taskId', '=', subtask.id)
            .execute()
        }

        await trx.deleteFrom('Task')
          .where('parentTaskId', '=', id)
          .execute()

        // Delete the main task
        await trx.deleteFrom('Task')
          .where('id', '=', id)
          .execute()

        return {
          success: true,
          data: undefined
        }
      })
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete task'
      }
    }
  }

  /**
   * TASK TAG OPERATIONS
   */

  async createTaskTag(data: {
    name: string
    color?: string
  }): Promise<DatabaseResult<any>> {
    try {
      // Check if tag already exists
      const existingTag = await this.db.selectFrom('TaskTag')
        .selectAll()
        .where('name', '=', data.name)
        .executeTakeFirst()

      if (existingTag) {
        return {
          success: false,
          error: 'Tag with this name already exists'
        }
      }

      const tag = await this.db.insertInto('TaskTag')
        .values({
          id: generateId(),
          name: data.name,
          color: data.color || null
        })
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: tag
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create task tag'
      }
    }
  }

  async getTaskTags(): Promise<DatabaseResult<any[]>> {
    try {
      const tags = await this.db.selectFrom('TaskTag')
        .selectAll()
        .orderBy('name', 'asc')
        .execute()

      return {
        success: true,
        data: tags
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get task tags'
      }
    }
  }

  async updateTaskTag(id: string, data: {
    name?: string
    color?: string
  }): Promise<DatabaseResult<any>> {
    try {
      const updateData: any = {}

      if (data.name !== undefined) {
        // Check if another tag with this name exists
        const existingTag = await this.db.selectFrom('TaskTag')
          .selectAll()
          .where('name', '=', data.name)
          .where('id', '!=', id)
          .executeTakeFirst()

        if (existingTag) {
          return {
            success: false,
            error: 'Tag with this name already exists'
          }
        }

        updateData.name = data.name
      }
      if (data.color !== undefined) updateData.color = data.color

      const tag = await this.db.updateTable('TaskTag')
        .set(updateData)
        .where('id', '=', id)
        .returningAll()
        .executeTakeFirstOrThrow()

      return {
        success: true,
        data: tag
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update task tag'
      }
    }
  }

  async deleteTaskTag(id: string): Promise<DatabaseResult<void>> {
    try {
      return await withTransaction(this.db, async (trx) => {
        // Remove all task-tag relationships
        await trx.deleteFrom('TaskTag')
          .where('tagId', '=', id)
          .execute()

        // Delete the tag
        await trx.deleteFrom('Tag')
          .where('id', '=', id)
          .execute()

        return {
          success: true,
          data: undefined
        }
      })
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete task tag'
      }
    }
  }

  /**
   * USER SETTINGS OPERATIONS
   */

  async getUserSettings(): Promise<DatabaseResult<any>> {
    try {
      const user = await this.db.selectFrom('User')
        .selectAll()
        .executeTakeFirst()

      if (!user) {
        // Return default settings if no user exists
        return {
          success: true,
          data: {
            theme: 'system',
            language: 'zh-CN',
            autoSave: true,
            notifications: true,
            shortcuts: {}
          }
        }
      }

      return {
        success: true,
        data: {
          theme: user.theme || 'system',
          language: user.language || 'zh-CN',
          autoSave: intToBoolean(user.autoSave || 1),
          notifications: intToBoolean(user.notifications || 1),
          shortcuts: deserializeJson(user.shortcuts) || {}
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get user settings'
      }
    }
  }

  async updateUserSettings(data: {
    theme?: string
    language?: string
    autoSave?: boolean
    notifications?: boolean
    shortcuts?: any
  }): Promise<DatabaseResult<any>> {
    try {
      const existingUser = await this.db.selectFrom('User')
        .selectAll()
        .executeTakeFirst()

      const updateData: any = {}
      if (data.theme !== undefined) updateData.theme = data.theme
      if (data.language !== undefined) updateData.language = data.language
      if (data.autoSave !== undefined) updateData.autoSave = booleanToInt(data.autoSave)
      if (data.notifications !== undefined) updateData.notifications = booleanToInt(data.notifications)
      if (data.shortcuts !== undefined) updateData.shortcuts = serializeJson(data.shortcuts)

      let user
      if (existingUser) {
        // Update existing user
        user = await this.db.updateTable('User')
          .set(updateData)
          .where('id', '=', existingUser.id)
          .returningAll()
          .executeTakeFirstOrThrow()
      } else {
        // Create new user
        user = await this.db.insertInto('User')
          .values({
            id: generateId(),
            theme: data.theme || 'system',
            language: data.language || 'zh-CN',
            autoSave: booleanToInt(data.autoSave ?? true),
            notifications: booleanToInt(data.notifications ?? true),
            shortcuts: data.shortcuts ? serializeJson(data.shortcuts) : null
          })
          .returningAll()
          .executeTakeFirstOrThrow()
      }

      return {
        success: true,
        data: {
          theme: user.theme,
          language: user.language,
          autoSave: intToBoolean(user.autoSave),
          notifications: intToBoolean(user.notifications),
          shortcuts: deserializeJson(user.shortcuts) || {}
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update user settings'
      }
    }
  }
}
