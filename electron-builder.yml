appId: com.codec.paolife
productName: PaoLife
directories:
  buildResources: build
  output: release
icon: build/icon.png
asar: false
files:
  - from: out
    to: .
  - package.json
extraResources:
  # 复制 better-sqlite3 原生模块
  - from: node_modules/better-sqlite3
    to: node_modules/better-sqlite3
    filter:
      - "**/*"
  # 复制 kysely 库
  - from: node_modules/kysely
    to: node_modules/kysely
  # 复制数据库迁移文件到多个位置以确保能找到
  - from: src/main/migrations.sql
    to: src/main/migrations.sql
  - from: src/main/migrations.sql
    to: main/migrations.sql
compression: normal
buildDependenciesFromSource: false
nodeGypRebuild: false
npmRebuild: false
win:
  target:
    - target: portable
      arch: [x64]
  executableName: PaoLife
  icon: build/icon.ico
mac:
  icon: build/icon.icns
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${name}-${version}.${ext}
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: electronjs.org
  category: Utility
portable:
  artifactName: ${name}-${version}-portable.${ext}
  requestExecutionLevel: user
  unpackDirName: PaoLife
appImage:
  artifactName: ${name}-${version}.${ext}
publish:
  provider: generic
  url: https://example.com/auto-updates
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
