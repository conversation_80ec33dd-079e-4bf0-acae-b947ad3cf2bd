// Database types for Kysely
// Generated from Prisma schema

export interface User {
  id: string
  username: string
  password: string
  settings: string | null // JSON stored as string
}

export interface Project {
  id: string
  name: string
  description: string | null
  status: string
  progress: number
  goal: string | null
  deliverable: string | null
  startDate: string | null // DateTime stored as ISO string
  deadline: string | null
  createdAt: string
  updatedAt: string
  archived: number // Boolean stored as 0/1
  areaId: string | null
}

export interface ProjectKPI {
  id: string
  name: string
  value: string
  target: string | null
  unit: string | null
  frequency: string | null
  direction: string
  updatedAt: string
  projectId: string
}

export interface KPIRecord {
  id: string
  value: string
  note: string | null
  recordedAt: string
  kpiId: string
}

export interface Deliverable {
  id: string
  title: string
  description: string | null
  type: string
  status: string
  content: string | null
  url: string | null
  filePath: string | null
  acceptanceCriteria: string | null // JSON stored as string
  plannedDate: string | null
  actualDate: string | null
  projectId: string
  createdAt: string
  updatedAt: string
}

export interface DeliverableResource {
  id: string
  deliverableId: string
  resourcePath: string
  resourceType: string
  title: string | null
  description: string | null
  createdAt: string
}

export interface Area {
  id: string
  name: string
  standard: string | null
  description: string | null
  icon: string | null
  color: string | null
  archived: number
  createdAt: string
  updatedAt: string
}

export interface AreaMetric {
  id: string
  name: string
  value: string
  target: string | null
  unit: string | null
  trackingType: string
  direction: string
  updatedAt: string
  areaId: string
  relatedHabits: string | null // JSON stored as string
}

export interface AreaMetricRecord {
  id: string
  value: string
  note: string | null
  recordedAt: string
  source: string | null
  confidence: number | null
  tags: string | null // JSON stored as string
  context: string | null
  metricId: string
}

export interface Habit {
  id: string
  name: string
  frequency: string
  target: number
  createdAt: string
  updatedAt: string
  areaId: string
}

export interface HabitRecord {
  id: string
  date: string
  completed: number // Boolean stored as 0/1
  value: number | null
  note: string | null
  habitId: string
}

export interface Checklist {
  id: string
  name: string
  template: string // JSON stored as string
  createdAt: string
  areaId: string
}

export interface ChecklistInstance {
  id: string
  status: string // JSON stored as string
  createdAt: string
  completedAt: string | null
  checklistId: string
}

export interface Task {
  id: string
  content: string
  description: string | null
  completed: number // Boolean stored as 0/1
  priority: string | null
  dueDate: string | null
  completedAt: string | null
  createdAt: string
  updatedAt: string
  parentId: string | null
  projectId: string | null
  areaId: string | null
  position: number
  sourceType: string | null
  sourceId: string | null
  sourceContext: string | null
  resourceLinkId: string | null
}

export interface Tag {
  id: string
  name: string
  color: string | null
  icon: string | null
}

export interface TaskTag {
  taskId: string
  tagId: string
}

export interface RecurringTask {
  id: string
  title: string
  description: string | null
  repeatRule: string
  repeatInterval: number
  nextDueDate: string | null
  lastCompletedAt: string | null
  isActive: number // Boolean stored as 0/1
  areaId: string
  createdAt: string
  updatedAt: string
}

export interface ResourceLink {
  id: string
  resourcePath: string
  title: string | null
  projectId: string | null
  areaId: string | null
}

export interface Review {
  id: string
  type: string
  period: string
  title: string | null
  content: string // JSON stored as string
  status: string
  createdAt: string
  updatedAt: string
  completedAt: string | null
  templateId: string | null
}

export interface ReviewTemplate {
  id: string
  name: string
  description: string | null
  type: string
  structure: string // JSON stored as string
  isDefault: number // Boolean stored as 0/1
  createdAt: string
  updatedAt: string
}

export interface InboxNote {
  id: string
  title: string | null
  content: string
  isDaily: number // Boolean stored as 0/1
  processed: number // Boolean stored as 0/1
  createdAt: string
  updatedAt: string
}

export interface DocumentLink {
  id: string
  sourceDocPath: string
  sourceDocTitle: string | null
  targetDocPath: string
  targetDocTitle: string | null
  linkText: string | null
  contextBefore: string | null
  contextAfter: string | null
  lineNumber: number | null
  linkType: string
  isValid: number // Boolean stored as 0/1
  createdAt: string
  updatedAt: string
}

// Database interface for Kysely
export interface Database {
  User: User
  Project: Project
  ProjectKPI: ProjectKPI
  KPIRecord: KPIRecord
  Deliverable: Deliverable
  DeliverableResource: DeliverableResource
  Area: Area
  AreaMetric: AreaMetric
  AreaMetricRecord: AreaMetricRecord
  Habit: Habit
  HabitRecord: HabitRecord
  Checklist: Checklist
  ChecklistInstance: ChecklistInstance
  Task: Task
  Tag: Tag
  TaskTag: TaskTag
  RecurringTask: RecurringTask
  ResourceLink: ResourceLink
  Review: Review
  ReviewTemplate: ReviewTemplate
  InboxNote: InboxNote
  DocumentLink: DocumentLink
}

// Helper types for inserts (optional fields)
export type NewUser = Omit<User, 'id'>
export type NewProject = Omit<Project, 'id' | 'createdAt' | 'updatedAt'>
export type NewArea = Omit<Area, 'id' | 'createdAt' | 'updatedAt'>
export type NewTask = Omit<Task, 'id' | 'createdAt' | 'updatedAt'>
export type NewHabit = Omit<Habit, 'id' | 'createdAt' | 'updatedAt'>
export type NewReview = Omit<Review, 'id' | 'createdAt' | 'updatedAt'>
export type NewReviewTemplate = Omit<ReviewTemplate, 'id' | 'createdAt' | 'updatedAt'>

// Helper types for updates (all fields optional except id)
export type ProjectUpdate = Partial<Omit<Project, 'id' | 'createdAt'>> & { updatedAt: string }
export type AreaUpdate = Partial<Omit<Area, 'id' | 'createdAt'>> & { updatedAt: string }
export type TaskUpdate = Partial<Omit<Task, 'id' | 'createdAt'>> & { updatedAt: string }
export type HabitUpdate = Partial<Omit<Habit, 'id' | 'createdAt'>> & { updatedAt: string }
export type ReviewUpdate = Partial<Omit<Review, 'id' | 'createdAt'>> & { updatedAt: string }
export type ReviewTemplateUpdate = Partial<Omit<ReviewTemplate, 'id' | 'createdAt'>> & { updatedAt: string }
