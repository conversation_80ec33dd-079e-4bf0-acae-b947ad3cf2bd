<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Electron</title>
    <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' vditor: paolife-assets: https://cdn.jsdelivr.net; script-src 'self' 'unsafe-inline' vditor: https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' vditor: https://cdn.jsdelivr.net; img-src 'self' data: blob: vditor: paolife-assets: file: https://cdn.jsdelivr.net; font-src 'self' vditor: https://cdn.jsdelivr.net; connect-src 'self' vditor: https://cdn.jsdelivr.net; object-src 'none';"
    />
    <script type="module" crossorigin src="./assets/index-CGF6OdO0.js"></script>
    <link rel="stylesheet" crossorigin href="./assets/index-C4HKdjZW.css">
  </head>

  <body>
    <div id="root"></div>
  </body>
</html>
